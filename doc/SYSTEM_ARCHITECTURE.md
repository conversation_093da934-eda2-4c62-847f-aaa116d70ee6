# AgroVue MVP - System Architecture Documentation

## Project Overview

AgroVue is a satellite-powered agricultural intelligence platform delivering geospatial insights for subsidy fraud detection, disaster monitoring, yield optimization, and sustainable supply chain auditing across Africa. The platform serves government agencies, NGOs, agribusinesses, financial institutions, and farmers' cooperatives.

## Technology Stack

### Backend
- **Framework**: Laravel 12
- **Database**: PostgreSQL with PostGIS extension
- **Cache**: Redis
- **Queue**: Laravel Queue with Redis driver
- **Storage**: AWS S3 for satellite imagery and file storage
- **API**: RESTful API with Laravel Sanctum authentication

### Frontend
- **Framework**: React 18 with TypeScript
- **Integration**: Laravel Inertia.js for seamless SPA experience
- **UI Library**: ShadCN UI components
- **Styling**: Tailwind CSS
- **State Management**: React Query for server state, Zustand for client state
- **Maps**: Leaflet.js with custom satellite imagery layers

### Infrastructure
- **Deployment**: Docker containers
- **Web Server**: Nginx
- **Process Manager**: Supervisor for queue workers
- **Monitoring**: <PERSON><PERSON> Telescope for development, Sentry for production

## System Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Application<br/>React + Inertia.js]
        MOB[Mobile Web<br/>PWA]
        API_CLIENT[External API Clients]
    end

    subgraph "Application Layer"
        NGINX[Nginx Load Balancer]
        LARAVEL[Laravel 12 Application]
        INERTIA[Inertia.js Middleware]
    end

    subgraph "Service Layer"
        AUTH[Authentication Service]
        GEO[Geospatial Service]
        ALERT[Alert Service]
        REPORT[Report Service]
        AI[AI Advisory Service]
        SMS[SMS Service]
    end

    subgraph "Data Layer"
        POSTGRES[(PostgreSQL + PostGIS)]
        REDIS[(Redis Cache/Queue)]
        S3[AWS S3 Storage]
        SATELLITE[Satellite Data APIs]
    end

    subgraph "External Services"
        WEATHER[Weather APIs]
        MAPS[Map Services]
        WHATSAPP[WhatsApp API]
        EMAIL[Email Service]
    end

    WEB --> NGINX
    MOB --> NGINX
    API_CLIENT --> NGINX
    NGINX --> LARAVEL
    LARAVEL --> INERTIA
    LARAVEL --> AUTH
    LARAVEL --> GEO
    LARAVEL --> ALERT
    LARAVEL --> REPORT
    LARAVEL --> AI
    LARAVEL --> SMS

    AUTH --> POSTGRES
    GEO --> POSTGRES
    ALERT --> REDIS
    REPORT --> POSTGRES
    AI --> POSTGRES
    SMS --> REDIS

    LARAVEL --> S3
    GEO --> SATELLITE
    ALERT --> WEATHER
    GEO --> MAPS
    SMS --> WHATSAPP
    ALERT --> EMAIL
```

## Database Architecture

```mermaid
erDiagram
    USERS {
        uuid id PK
        string email
        string password
        enum role
        string name
        string phone
        json preferences
        timestamp created_at
        timestamp updated_at
    }

    ORGANIZATIONS {
        uuid id PK
        string name
        enum type
        string contact_email
        string contact_phone
        json settings
        timestamp created_at
        timestamp updated_at
    }

    FIELDS {
        uuid id PK
        uuid user_id FK
        uuid organization_id FK
        string name
        geometry boundary
        float area_hectares
        string crop_type
        json metadata
        timestamp created_at
        timestamp updated_at
    }

    SATELLITE_DATA {
        uuid id PK
        uuid field_id FK
        date capture_date
        float ndvi_value
        float precipitation
        string image_url
        json analysis_data
        timestamp created_at
    }

    SUBSIDY_CLAIMS {
        uuid id PK
        uuid field_id FK
        uuid user_id FK
        string claim_number
        decimal amount
        enum status
        json verification_data
        timestamp submitted_at
        timestamp verified_at
    }

    ALERTS {
        uuid id PK
        uuid field_id FK
        enum type
        enum severity
        string title
        text description
        json metadata
        boolean acknowledged
        timestamp created_at
    }

    REPORTS {
        uuid id PK
        uuid user_id FK
        string title
        enum type
        json data
        string file_path
        timestamp generated_at
    }

    USERS ||--o{ FIELDS : owns
    ORGANIZATIONS ||--o{ USERS : employs
    ORGANIZATIONS ||--o{ FIELDS : manages
    FIELDS ||--o{ SATELLITE_DATA : monitored_by
    FIELDS ||--o{ SUBSIDY_CLAIMS : claimed_for
    FIELDS ||--o{ ALERTS : generates
    USERS ||--o{ REPORTS : creates
```

## Core Modules Architecture

### 1. Authentication & Authorization Module
- Role-based access control (Government, NGO, Corporate, Co-op, Bank)
- Multi-factor authentication with OTP
- Session management with Laravel Sanctum
- Permission-based feature access

### 2. Geospatial Intelligence Module
- PostGIS for spatial data storage
- Satellite imagery processing
- NDVI calculation and analysis
- Field boundary management
- Timeline visualization

### 3. Alert & Notification System
- Real-time alert generation
- Multi-channel notifications (SMS, Email, Push)
- Severity-based alert routing
- Acknowledgment tracking

### 4. Subsidy Verification Module
- Claim submission and tracking
- Satellite-based verification
- Fraud detection algorithms
- Compliance reporting

### 5. AI Advisory Module
- Multilingual chatbot (English, Hausa, Swahili, French)
- Crop-specific recommendations
- Weather-based advisories
- Integration with WhatsApp

### 6. Reporting & Analytics Module
- Customizable dashboards
- Export capabilities (PDF, CSV)
- Yield forecasting
- Performance analytics

## API Architecture

### RESTful API Endpoints

```
Authentication:
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/verify-otp

Fields Management:
GET /api/fields
POST /api/fields
GET /api/fields/{id}
PUT /api/fields/{id}
DELETE /api/fields/{id}
GET /api/fields/{id}/satellite-data

Alerts:
GET /api/alerts
POST /api/alerts
PUT /api/alerts/{id}/acknowledge
GET /api/alerts/dashboard

Subsidy Claims:
GET /api/subsidy-claims
POST /api/subsidy-claims
PUT /api/subsidy-claims/{id}/verify
GET /api/subsidy-claims/{id}/verification-report

Reports:
GET /api/reports
POST /api/reports/generate
GET /api/reports/{id}/download

AI Advisory:
POST /api/ai/chat
GET /api/ai/recommendations/{field_id}
```

## Security Architecture

### Data Protection
- Encryption at rest and in transit
- API rate limiting
- Input validation and sanitization
- SQL injection prevention
- XSS protection

### Access Control
- JWT token-based authentication
- Role-based permissions
- Field-level security
- Audit logging

### Compliance
- GDPR compliance for data handling
- Data retention policies
- User consent management
- Privacy controls

## Performance Optimization

### Caching Strategy
- Redis for session storage
- Query result caching
- Satellite imagery caching
- API response caching

### Database Optimization
- Spatial indexing for geospatial queries
- Query optimization
- Connection pooling
- Read replicas for reporting

### Frontend Optimization
- Code splitting
- Lazy loading
- Image optimization
- Progressive Web App features

## Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        LB[Load Balancer]
        WEB1[Web Server 1]
        WEB2[Web Server 2]
        APP1[App Server 1]
        APP2[App Server 2]
        QUEUE1[Queue Worker 1]
        QUEUE2[Queue Worker 2]
        DB_MASTER[(Database Master)]
        DB_REPLICA[(Database Replica)]
        REDIS_CLUSTER[(Redis Cluster)]
        S3_BUCKET[S3 Storage]
    end

    LB --> WEB1
    LB --> WEB2
    WEB1 --> APP1
    WEB2 --> APP2
    APP1 --> DB_MASTER
    APP2 --> DB_MASTER
    APP1 --> DB_REPLICA
    APP2 --> DB_REPLICA
    APP1 --> REDIS_CLUSTER
    APP2 --> REDIS_CLUSTER
    QUEUE1 --> DB_MASTER
    QUEUE2 --> DB_MASTER
    QUEUE1 --> REDIS_CLUSTER
    QUEUE2 --> REDIS_CLUSTER
    APP1 --> S3_BUCKET
    APP2 --> S3_BUCKET
```

## Monitoring & Logging

### Application Monitoring
- Laravel Telescope for development
- Sentry for error tracking
- Performance monitoring
- Health checks

### Infrastructure Monitoring
- Server resource monitoring
- Database performance tracking
- Queue monitoring
- Storage usage tracking

### Logging Strategy
- Structured logging
- Log aggregation
- Security event logging
- Audit trail maintenance
