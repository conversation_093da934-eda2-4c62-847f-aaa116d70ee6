# AgroVue Technical Implementation Guide
**Vuexy + Laravel 12 + MySQL Stack**

## Prerequisites

### Development Environment
- **PHP**: 8.2 or higher
- **Node.js**: 18.x or higher
- **MySQL**: 8.0 or higher
- **Redis**: 7.x or higher
- **Composer**: Latest version
- **Git**: Latest version

### Required Extensions
```bash
# PHP Extensions
php-mysql
php-redis
php-gd
php-curl
php-mbstring
php-xml
php-zip
php-intl
php-bcmath
```

## Project Setup

### 1. Laravel 12 Backend Setup

#### Initial Setup
```bash
# Create new Laravel project
composer create-project laravel/laravel agrovue-backend "12.*"
cd agrovue-backend

# Install additional packages
composer require laravel/sanctum
composer require spatie/laravel-permission
composer require maatwebsite/excel
composer require barryvdh/laravel-dompdf
composer require pusher/pusher-php-server

# Development packages
composer require --dev laravel/telescope
composer require --dev pestphp/pest
composer require --dev pestphp/pest-plugin-laravel
```

#### Environment Configuration
```env
# .env
APP_NAME=AgroVue
APP_ENV=local
APP_KEY=base64:generated_key
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=agrovue
DB_USERNAME=root
DB_PASSWORD=

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

# Satellite APIs
SENTINEL_API_URL=https://scihub.copernicus.eu/dhus/search
SENTINEL_USERNAME=your_username
SENTINEL_PASSWORD=your_password

# Weather API
OPENWEATHER_API_KEY=your_api_key

# SMS Gateway
TWILIO_SID=your_sid
TWILIO_TOKEN=your_token
TWILIO_FROM=your_phone_number
```

#### Database Migrations
```bash
# Create core migrations
php artisan make:migration create_organizations_table
php artisan make:migration create_fields_table
php artisan make:migration create_satellite_data_table
php artisan make:migration create_alerts_table
php artisan make:migration create_subsidy_claims_table

# Run migrations
php artisan migrate
```

#### Models and Relationships
```php
// app/Models/User.php
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'role', 'phone', 
        'language', 'organization_id', 'permissions', 'is_active'
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
        'email_verified_at' => 'datetime',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function fields()
    {
        return $this->hasMany(Field::class, 'owner_id');
    }
}

// app/Models/Field.php
class Field extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'field_id', 'owner_id', 'organization_id',
        'area_hectares', 'crop_type', 'crop_variety',
        'coordinates', 'center_latitude', 'center_longitude',
        'state', 'lga', 'ward', 'status', 'planting_date',
        'expected_harvest_date', 'expected_yield_tons',
        'soil_data', 'notes', 'is_mapped', 'health_status', 'latest_ndvi'
    ];

    protected $casts = [
        'coordinates' => 'array',
        'soil_data' => 'array',
        'planting_date' => 'date',
        'expected_harvest_date' => 'date',
        'is_mapped' => 'boolean',
        'last_satellite_update' => 'datetime',
    ];

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function satelliteData()
    {
        return $this->hasMany(SatelliteData::class);
    }

    public function alerts()
    {
        return $this->hasMany(Alert::class);
    }
}
```

### 2. Vuexy Frontend Setup

#### Project Initialization
```bash
# Clone Vuexy template (assuming you have access)
git clone https://github.com/themeselection/vuexy-vuejs-admin-template.git agrovue-frontend
cd agrovue-frontend

# Install dependencies
npm install

# Install additional packages
npm install @vueuse/core
npm install leaflet
npm install @types/leaflet
npm install axios
npm install pinia
npm install @pinia/nuxt
npm install vee-validate
npm install yup
npm install @vuelidate/core
npm install @vuelidate/validators

# Offline-First PWA packages
npm install workbox-webpack-plugin
npm install workbox-window
npm install dexie
npm install @types/dexie
npm install pinia-plugin-persistedstate
npm install @vite-pwa/vite-plugin-pwa
npm install localforage
npm install idb
```

#### Environment Configuration
```env
# .env
VITE_APP_TITLE=AgroVue
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_ENV=development
VITE_OPENWEATHER_API_KEY=your_api_key
VITE_MAPBOX_ACCESS_TOKEN=your_token

# Offline-First Configuration
VITE_ENABLE_PWA=true
VITE_OFFLINE_MODE=true
VITE_SYNC_INTERVAL=30000
VITE_MAX_OFFLINE_STORAGE=100MB
VITE_CACHE_STRATEGY=cache-first
```

#### Project Structure Setup
```typescript
// src/types/index.ts
export interface User {
  id: number
  name: string
  email: string
  role: 'farmer' | 'government_admin' | 'ngo_coordinator' | 'bank_analyst' | 'corporate'
  phone?: string
  organization?: Organization
  permissions: string[]
  is_active: boolean
}

export interface Field {
  id: number
  name: string
  field_id: string
  owner_id: number
  area_hectares: number
  crop_type: string
  crop_variety?: string
  coordinates: GeoJSON.Polygon
  center_latitude: number
  center_longitude: number
  state: string
  lga: string
  status: 'active' | 'inactive' | 'disputed' | 'verified'
  health_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
  latest_ndvi?: number
  owner: User
}

export interface SatelliteData {
  id: number
  field_id: number
  satellite_source: string
  capture_date: string
  ndvi_average: number
  vegetation_health: string
  anomalies_detected: string[]
}
```

#### API Service Setup
```typescript
// src/services/api.ts
import axios from 'axios'
import { useAuthStore } from '@/stores/auth'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use((config) => {
  const authStore = useAuthStore()
  if (authStore.token) {
    config.headers.Authorization = `Bearer ${authStore.token}`
  }
  return config
})

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
    }
    return Promise.reject(error)
  }
)

export default api

// src/services/offline.ts - Offline-First Service
import Dexie, { Table } from 'dexie'
import { useNetworkStatus } from '@/composables/useNetworkStatus'

interface OfflineData {
  id?: number
  type: string
  data: any
  timestamp: number
  synced: boolean
  retryCount: number
}

interface SyncConflict {
  id?: number
  localData: any
  serverData: any
  field: string
  timestamp: number
  resolved: boolean
}

class OfflineDatabase extends Dexie {
  offlineData!: Table<OfflineData>
  syncConflicts!: Table<SyncConflict>

  constructor() {
    super('AgroVueOfflineDB')
    this.version(1).stores({
      offlineData: '++id, type, timestamp, synced',
      syncConflicts: '++id, field, timestamp, resolved'
    })
  }
}

const db = new OfflineDatabase()

export class OfflineService {
  private syncInProgress = false
  private syncQueue: OfflineData[] = []

  async saveOfflineData(type: string, data: any): Promise<void> {
    const offlineData: OfflineData = {
      type,
      data,
      timestamp: Date.now(),
      synced: false,
      retryCount: 0
    }

    await db.offlineData.add(offlineData)

    // Attempt immediate sync if online
    if (useNetworkStatus().isOnline.value) {
      this.syncData()
    }
  }

  async syncData(): Promise<void> {
    if (this.syncInProgress) return

    this.syncInProgress = true

    try {
      const unsyncedData = await db.offlineData
        .where('synced')
        .equals(false)
        .toArray()

      for (const item of unsyncedData) {
        try {
          await this.syncSingleItem(item)
          await db.offlineData.update(item.id!, { synced: true })
        } catch (error) {
          await db.offlineData.update(item.id!, {
            retryCount: item.retryCount + 1
          })

          if (item.retryCount >= 3) {
            console.error('Max retry attempts reached for item:', item.id)
          }
        }
      }
    } finally {
      this.syncInProgress = false
    }
  }

  private async syncSingleItem(item: OfflineData): Promise<void> {
    const endpoint = this.getEndpointForType(item.type)
    const response = await api.post(endpoint, item.data)

    // Handle potential conflicts
    if (response.status === 409) {
      await this.handleConflict(item, response.data)
      throw new Error('Conflict detected')
    }
  }

  private async handleConflict(localItem: OfflineData, serverData: any): Promise<void> {
    await db.syncConflicts.add({
      localData: localItem.data,
      serverData,
      field: localItem.type,
      timestamp: Date.now(),
      resolved: false
    })
  }

  private getEndpointForType(type: string): string {
    const endpoints = {
      'field': '/fields',
      'satellite_data': '/satellite-data',
      'alert': '/alerts',
      'photo': '/photos'
    }
    return endpoints[type] || '/sync'
  }
}

export const offlineService = new OfflineService()

// vite.config.ts - PWA Configuration
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'

export default defineConfig({
  plugins: [
    vue(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\.agrovue\.com\/.*$/,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              },
              cacheKeyWillBeUsed: async ({ request }) => {
                return `${request.url}?timestamp=${Date.now()}`
              }
            }
          },
          {
            urlPattern: /^https:\/\/.*\.(?:png|jpg|jpeg|svg|gif)$/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'images-cache',
              expiration: {
                maxEntries: 200,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
              }
            }
          }
        ]
      },
      manifest: {
        name: 'AgroVue - Agricultural Intelligence',
        short_name: 'AgroVue',
        description: 'Satellite-powered agricultural monitoring platform',
        theme_color: '#10b981',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: '/icons/icon-72x72.png',
            sizes: '72x72',
            type: 'image/png'
          },
          {
            src: '/icons/icon-96x96.png',
            sizes: '96x96',
            type: 'image/png'
          },
          {
            src: '/icons/icon-128x128.png',
            sizes: '128x128',
            type: 'image/png'
          },
          {
            src: '/icons/icon-144x144.png',
            sizes: '144x144',
            type: 'image/png'
          },
          {
            src: '/icons/icon-152x152.png',
            sizes: '152x152',
            type: 'image/png'
          },
          {
            src: '/icons/icon-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: '/icons/icon-384x384.png',
            sizes: '384x384',
            type: 'image/png'
          },
          {
            src: '/icons/icon-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      }
    })
  ]
})

// src/composables/useNetworkStatus.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const connectionType = ref('')
  const effectiveType = ref('')

  const updateOnlineStatus = () => {
    isOnline.value = navigator.onLine

    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connectionType.value = connection.type || ''
      effectiveType.value = connection.effectiveType || ''
    }
  }

  onMounted(() => {
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    updateOnlineStatus()
  })

  onUnmounted(() => {
    window.removeEventListener('online', updateOnlineStatus)
    window.removeEventListener('offline', updateOnlineStatus)
  })

  return {
    isOnline,
    connectionType,
    effectiveType
  }
}
```

#### Pinia Store Setup
```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'
import type { User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  
  const isAuthenticated = computed(() => !!token.value)
  
  const login = async (credentials: { email: string; password: string }) => {
    try {
      const response = await api.post('/auth/login', credentials)
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', token.value)
      return response.data
    } catch (error) {
      throw error
    }
  }
  
  const logout = async () => {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      // Handle error
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
    }
  }
  
  const fetchUser = async () => {
    try {
      const response = await api.get('/auth/user')
      user.value = response.data
      return response.data
    } catch (error) {
      logout()
      throw error
    }
  }
  
  return {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    fetchUser,
  }
})
```

## Development Workflow

### 1. Backend Development
```bash
# Start Laravel development server
php artisan serve

# Run queue workers
php artisan queue:work

# Run scheduled tasks (in development)
php artisan schedule:work

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### 2. Frontend Development
```bash
# Start Vuexy development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### 3. Database Management
```bash
# Create new migration
php artisan make:migration create_table_name

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Seed database
php artisan db:seed

# Create model with migration and factory
php artisan make:model ModelName -mf

# Create offline sync controllers
php artisan make:controller Api/SyncController
php artisan make:controller Api/OfflineController

// Backend Sync Implementation

// app/Http/Controllers/Api/SyncController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SyncService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SyncController extends Controller
{
    public function __construct(
        private SyncService $syncService
    ) {}

    public function batchSync(Request $request): JsonResponse
    {
        $request->validate([
            'data' => 'required|array',
            'data.*.type' => 'required|string',
            'data.*.payload' => 'required|array',
            'data.*.timestamp' => 'required|integer',
            'data.*.client_id' => 'required|string'
        ]);

        $results = [];
        $conflicts = [];

        foreach ($request->data as $item) {
            try {
                $result = $this->syncService->syncItem(
                    $item['type'],
                    $item['payload'],
                    $item['timestamp'],
                    $item['client_id'],
                    $request->user()
                );

                if ($result['conflict']) {
                    $conflicts[] = [
                        'client_id' => $item['client_id'],
                        'conflict_data' => $result['conflict_data']
                    ];
                } else {
                    $results[] = [
                        'client_id' => $item['client_id'],
                        'server_id' => $result['server_id'],
                        'status' => 'synced'
                    ];
                }
            } catch (\Exception $e) {
                $results[] = [
                    'client_id' => $item['client_id'],
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'synced' => $results,
            'conflicts' => $conflicts,
            'timestamp' => now()->timestamp
        ]);
    }

    public function resolveConflict(Request $request): JsonResponse
    {
        $request->validate([
            'conflict_id' => 'required|string',
            'resolution' => 'required|in:use_local,use_server,merge',
            'merged_data' => 'sometimes|array'
        ]);

        $result = $this->syncService->resolveConflict(
            $request->conflict_id,
            $request->resolution,
            $request->merged_data ?? null,
            $request->user()
        );

        return response()->json([
            'success' => true,
            'resolved' => $result
        ]);
    }

    public function getUpdates(Request $request): JsonResponse
    {
        $request->validate([
            'last_sync' => 'required|integer',
            'types' => 'sometimes|array'
        ]);

        $updates = $this->syncService->getUpdatesForUser(
            $request->user(),
            $request->last_sync,
            $request->types ?? []
        );

        return response()->json([
            'success' => true,
            'updates' => $updates,
            'timestamp' => now()->timestamp
        ]);
    }
}

// app/Services/SyncService.php
<?php

namespace App\Services;

use App\Models\User;
use App\Models\Field;
use App\Models\SatelliteData;
use App\Models\Alert;
use App\Models\SyncConflict;
use Illuminate\Support\Str;

class SyncService
{
    public function syncItem(
        string $type,
        array $payload,
        int $timestamp,
        string $clientId,
        User $user
    ): array {
        $modelClass = $this->getModelClass($type);

        if (!$modelClass) {
            throw new \InvalidArgumentException("Unknown sync type: {$type}");
        }

        // Check for existing record
        $existingRecord = $this->findExistingRecord($modelClass, $payload, $user);

        if ($existingRecord) {
            return $this->handleUpdate($existingRecord, $payload, $timestamp, $clientId);
        } else {
            return $this->handleCreate($modelClass, $payload, $clientId, $user);
        }
    }

    private function handleUpdate($existingRecord, array $payload, int $timestamp, string $clientId): array
    {
        // Check for conflicts
        if ($existingRecord->updated_at->timestamp > $timestamp) {
            return [
                'conflict' => true,
                'conflict_data' => [
                    'server_data' => $existingRecord->toArray(),
                    'client_data' => $payload,
                    'conflict_id' => Str::uuid()
                ]
            ];
        }

        // No conflict, update the record
        $existingRecord->update($payload);

        return [
            'conflict' => false,
            'server_id' => $existingRecord->id
        ];
    }

    private function handleCreate(string $modelClass, array $payload, string $clientId, User $user): array
    {
        // Add user ownership if applicable
        if (method_exists($modelClass, 'user')) {
            $payload['user_id'] = $user->id;
        }

        $record = $modelClass::create($payload);

        return [
            'conflict' => false,
            'server_id' => $record->id
        ];
    }

    public function resolveConflict(
        string $conflictId,
        string $resolution,
        ?array $mergedData,
        User $user
    ): bool {
        $conflict = SyncConflict::where('conflict_id', $conflictId)
            ->where('user_id', $user->id)
            ->firstOrFail();

        switch ($resolution) {
            case 'use_local':
                $this->applyLocalData($conflict);
                break;
            case 'use_server':
                // Server data is already applied, just mark as resolved
                break;
            case 'merge':
                $this->applyMergedData($conflict, $mergedData);
                break;
        }

        $conflict->update(['resolved' => true]);

        return true;
    }

    public function getUpdatesForUser(User $user, int $lastSync, array $types = []): array
    {
        $updates = [];

        $typesToSync = empty($types) ? ['fields', 'alerts', 'satellite_data'] : $types;

        foreach ($typesToSync as $type) {
            $modelClass = $this->getModelClass($type);
            if (!$modelClass) continue;

            $query = $modelClass::where('updated_at', '>', date('Y-m-d H:i:s', $lastSync));

            // Apply user-specific filters
            if ($type === 'fields') {
                $query->where('owner_id', $user->id);
            } elseif ($type === 'alerts') {
                $query->where('user_id', $user->id);
            }

            $updates[$type] = $query->get()->toArray();
        }

        return $updates;
    }

    private function getModelClass(string $type): ?string
    {
        $models = [
            'fields' => Field::class,
            'alerts' => Alert::class,
            'satellite_data' => SatelliteData::class,
        ];

        return $models[$type] ?? null;
    }

    private function findExistingRecord(string $modelClass, array $payload, User $user)
    {
        // Try to find by ID first
        if (isset($payload['id'])) {
            return $modelClass::find($payload['id']);
        }

        // Try to find by unique identifier
        if (isset($payload['field_id']) && $modelClass === Field::class) {
            return Field::where('field_id', $payload['field_id'])
                ->where('owner_id', $user->id)
                ->first();
        }

        return null;
    }
}
```

## Testing Strategy

### Backend Testing (Laravel)
```bash
# Install Pest
composer require pestphp/pest --dev
composer require pestphp/pest-plugin-laravel --dev

# Run tests
php artisan test
./vendor/bin/pest

# Run with coverage
php artisan test --coverage
```

### Frontend Testing (Vue)
```bash
# Install testing dependencies
npm install --save-dev vitest @vue/test-utils jsdom

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Production Setup
```bash
# Optimize Laravel for production
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Build frontend for production
npm run build

# Set proper permissions
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

### Docker Deployment
```dockerfile
# Dockerfile
FROM php:8.2-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application
COPY . .

# Install dependencies
RUN composer install --optimize-autoloader --no-dev

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 9000
CMD ["php-fpm"]
```

## Performance Optimization

### Backend Optimization
- Database indexing for frequently queried fields
- Redis caching for expensive operations
- Queue processing for background tasks
- API response caching
- Database query optimization

### Frontend Optimization
- Code splitting and lazy loading
- Image optimization and WebP format
- Service worker for caching
- Bundle size optimization
- Component lazy loading

## Security Checklist

### Backend Security
- [ ] Input validation on all endpoints
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Rate limiting
- [ ] Authentication and authorization
- [ ] Secure headers
- [ ] Environment variable protection

### Frontend Security
- [ ] Content Security Policy
- [ ] Secure HTTP headers
- [ ] Input sanitization
- [ ] Secure token storage
- [ ] HTTPS enforcement
- [ ] Dependency vulnerability scanning

This implementation guide provides a solid foundation for building the AgroVue platform with the Vuexy + Laravel 12 + MySQL stack.
