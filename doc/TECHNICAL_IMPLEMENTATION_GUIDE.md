# AgroVue Technical Implementation Guide
**Vuexy + Laravel 12 + MySQL Stack**

## Prerequisites

### Development Environment
- **PHP**: 8.2 or higher
- **Node.js**: 18.x or higher
- **MySQL**: 8.0 or higher
- **Redis**: 7.x or higher
- **Composer**: Latest version
- **Git**: Latest version

### Required Extensions
```bash
# PHP Extensions
php-mysql
php-redis
php-gd
php-curl
php-mbstring
php-xml
php-zip
php-intl
php-bcmath
```

## Project Setup

### 1. Laravel 12 Backend Setup

#### Initial Setup
```bash
# Create new Laravel project
composer create-project laravel/laravel agrovue-backend "12.*"
cd agrovue-backend

# Install additional packages
composer require laravel/sanctum
composer require spatie/laravel-permission
composer require maatwebsite/excel
composer require barryvdh/laravel-dompdf
composer require pusher/pusher-php-server

# Development packages
composer require --dev laravel/telescope
composer require --dev pestphp/pest
composer require --dev pestphp/pest-plugin-laravel
```

#### Environment Configuration
```env
# .env
APP_NAME=AgroVue
APP_ENV=local
APP_KEY=base64:generated_key
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=agrovue
DB_USERNAME=root
DB_PASSWORD=

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

# Satellite APIs
SENTINEL_API_URL=https://scihub.copernicus.eu/dhus/search
SENTINEL_USERNAME=your_username
SENTINEL_PASSWORD=your_password

# Weather API
OPENWEATHER_API_KEY=your_api_key

# SMS Gateway
TWILIO_SID=your_sid
TWILIO_TOKEN=your_token
TWILIO_FROM=your_phone_number
```

#### Database Migrations
```bash
# Create core migrations
php artisan make:migration create_organizations_table
php artisan make:migration create_fields_table
php artisan make:migration create_satellite_data_table
php artisan make:migration create_alerts_table
php artisan make:migration create_subsidy_claims_table

# Run migrations
php artisan migrate
```

#### Models and Relationships
```php
// app/Models/User.php
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'role', 'phone', 
        'language', 'organization_id', 'permissions', 'is_active'
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
        'email_verified_at' => 'datetime',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function fields()
    {
        return $this->hasMany(Field::class, 'owner_id');
    }
}

// app/Models/Field.php
class Field extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'field_id', 'owner_id', 'organization_id',
        'area_hectares', 'crop_type', 'crop_variety',
        'coordinates', 'center_latitude', 'center_longitude',
        'state', 'lga', 'ward', 'status', 'planting_date',
        'expected_harvest_date', 'expected_yield_tons',
        'soil_data', 'notes', 'is_mapped', 'health_status', 'latest_ndvi'
    ];

    protected $casts = [
        'coordinates' => 'array',
        'soil_data' => 'array',
        'planting_date' => 'date',
        'expected_harvest_date' => 'date',
        'is_mapped' => 'boolean',
        'last_satellite_update' => 'datetime',
    ];

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function satelliteData()
    {
        return $this->hasMany(SatelliteData::class);
    }

    public function alerts()
    {
        return $this->hasMany(Alert::class);
    }
}
```

### 2. Vuexy Frontend Setup

#### Project Initialization
```bash
# Clone Vuexy template (assuming you have access)
git clone https://github.com/themeselection/vuexy-vuejs-admin-template.git agrovue-frontend
cd agrovue-frontend

# Install dependencies
npm install

# Install additional packages
npm install @vueuse/core
npm install leaflet
npm install @types/leaflet
npm install axios
npm install pinia
npm install @pinia/nuxt
npm install vee-validate
npm install yup
npm install @vuelidate/core
npm install @vuelidate/validators
```

#### Environment Configuration
```env
# .env
VITE_APP_TITLE=AgroVue
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_ENV=development
VITE_OPENWEATHER_API_KEY=your_api_key
VITE_MAPBOX_ACCESS_TOKEN=your_token
```

#### Project Structure Setup
```typescript
// src/types/index.ts
export interface User {
  id: number
  name: string
  email: string
  role: 'farmer' | 'government_admin' | 'ngo_coordinator' | 'bank_analyst' | 'corporate'
  phone?: string
  organization?: Organization
  permissions: string[]
  is_active: boolean
}

export interface Field {
  id: number
  name: string
  field_id: string
  owner_id: number
  area_hectares: number
  crop_type: string
  crop_variety?: string
  coordinates: GeoJSON.Polygon
  center_latitude: number
  center_longitude: number
  state: string
  lga: string
  status: 'active' | 'inactive' | 'disputed' | 'verified'
  health_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
  latest_ndvi?: number
  owner: User
}

export interface SatelliteData {
  id: number
  field_id: number
  satellite_source: string
  capture_date: string
  ndvi_average: number
  vegetation_health: string
  anomalies_detected: string[]
}
```

#### API Service Setup
```typescript
// src/services/api.ts
import axios from 'axios'
import { useAuthStore } from '@/stores/auth'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use((config) => {
  const authStore = useAuthStore()
  if (authStore.token) {
    config.headers.Authorization = `Bearer ${authStore.token}`
  }
  return config
})

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
    }
    return Promise.reject(error)
  }
)

export default api
```

#### Pinia Store Setup
```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'
import type { User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  
  const isAuthenticated = computed(() => !!token.value)
  
  const login = async (credentials: { email: string; password: string }) => {
    try {
      const response = await api.post('/auth/login', credentials)
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', token.value)
      return response.data
    } catch (error) {
      throw error
    }
  }
  
  const logout = async () => {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      // Handle error
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
    }
  }
  
  const fetchUser = async () => {
    try {
      const response = await api.get('/auth/user')
      user.value = response.data
      return response.data
    } catch (error) {
      logout()
      throw error
    }
  }
  
  return {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    fetchUser,
  }
})
```

## Development Workflow

### 1. Backend Development
```bash
# Start Laravel development server
php artisan serve

# Run queue workers
php artisan queue:work

# Run scheduled tasks (in development)
php artisan schedule:work

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### 2. Frontend Development
```bash
# Start Vuexy development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### 3. Database Management
```bash
# Create new migration
php artisan make:migration create_table_name

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Seed database
php artisan db:seed

# Create model with migration and factory
php artisan make:model ModelName -mf
```

## Testing Strategy

### Backend Testing (Laravel)
```bash
# Install Pest
composer require pestphp/pest --dev
composer require pestphp/pest-plugin-laravel --dev

# Run tests
php artisan test
./vendor/bin/pest

# Run with coverage
php artisan test --coverage
```

### Frontend Testing (Vue)
```bash
# Install testing dependencies
npm install --save-dev vitest @vue/test-utils jsdom

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Production Setup
```bash
# Optimize Laravel for production
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Build frontend for production
npm run build

# Set proper permissions
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

### Docker Deployment
```dockerfile
# Dockerfile
FROM php:8.2-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application
COPY . .

# Install dependencies
RUN composer install --optimize-autoloader --no-dev

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 9000
CMD ["php-fpm"]
```

## Performance Optimization

### Backend Optimization
- Database indexing for frequently queried fields
- Redis caching for expensive operations
- Queue processing for background tasks
- API response caching
- Database query optimization

### Frontend Optimization
- Code splitting and lazy loading
- Image optimization and WebP format
- Service worker for caching
- Bundle size optimization
- Component lazy loading

## Security Checklist

### Backend Security
- [ ] Input validation on all endpoints
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Rate limiting
- [ ] Authentication and authorization
- [ ] Secure headers
- [ ] Environment variable protection

### Frontend Security
- [ ] Content Security Policy
- [ ] Secure HTTP headers
- [ ] Input sanitization
- [ ] Secure token storage
- [ ] HTTPS enforcement
- [ ] Dependency vulnerability scanning

This implementation guide provides a solid foundation for building the AgroVue platform with the Vuexy + Laravel 12 + MySQL stack.
