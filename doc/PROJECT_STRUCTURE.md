# AgroVue MVP - Project Structure

## Overview

This document outlines the complete project structure for AgroVue MVP using Laravel 12 with React Inertia.js and ShadCN UI. All implementation code will be organized within the `src/` directory to maintain clean separation and better organization.

## Root Directory Structure

```
agrovue/
├── src/                          # Main implementation directory
│   ├── app/                      # Laravel application code
│   ├── database/                 # Database migrations, seeders, factories
│   ├── resources/                # Frontend resources (React, CSS, etc.)
│   ├── routes/                   # Route definitions
│   ├── config/                   # Configuration files
│   ├── storage/                  # Storage directories
│   └── tests/                    # Test files
├── docker/                       # Docker configuration
├── docs/                         # Documentation (current folder)
├── scripts/                      # Deployment and utility scripts
├── .env.example                  # Environment configuration template
├── composer.json                 # PHP dependencies
├── package.json                  # Node.js dependencies
├── docker-compose.yml            # Docker services configuration
├── tailwind.config.js            # Tailwind CSS configuration
├── tsconfig.json                 # TypeScript configuration
├── vite.config.js                # Vite build configuration
└── README.md                     # Project documentation
```

## Detailed src/ Directory Structure

### Backend Structure (Laravel)

```
src/
├── app/
│   ├── Console/
│   │   ├── Commands/
│   │   │   ├── ProcessSatelliteData.php
│   │   │   ├── GenerateAlerts.php
│   │   │   └── SendNotifications.php
│   │   └── Kernel.php
│   ├── Events/
│   │   ├── FieldCreated.php
│   │   ├── AlertGenerated.php
│   │   ├── ClaimSubmitted.php
│   │   └── ReportGenerated.php
│   ├── Exceptions/
│   │   ├── Handler.php
│   │   ├── GeospatialException.php
│   │   └── VerificationException.php
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Api/
│   │   │   │   ├── AuthController.php
│   │   │   │   ├── FieldController.php
│   │   │   │   ├── AlertController.php
│   │   │   │   ├── SubsidyController.php
│   │   │   │   ├── ReportController.php
│   │   │   │   └── AIAdvisoryController.php
│   │   │   ├── Auth/
│   │   │   │   ├── LoginController.php
│   │   │   │   ├── RegisterController.php
│   │   │   │   └── OTPController.php
│   │   │   ├── Dashboard/
│   │   │   │   └── DashboardController.php
│   │   │   ├── Field/
│   │   │   │   ├── FieldController.php
│   │   │   │   ├── SatelliteDataController.php
│   │   │   │   └── FieldMapController.php
│   │   │   ├── Alert/
│   │   │   │   ├── AlertController.php
│   │   │   │   └── NotificationController.php
│   │   │   ├── Subsidy/
│   │   │   │   ├── ClaimController.php
│   │   │   │   └── VerificationController.php
│   │   │   ├── Report/
│   │   │   │   ├── ReportController.php
│   │   │   │   └── AnalyticsController.php
│   │   │   └── AI/
│   │   │       ├── ChatbotController.php
│   │   │       └── AdvisoryController.php
│   │   ├── Middleware/
│   │   │   ├── Authenticate.php
│   │   │   ├── HandleInertiaRequests.php
│   │   │   ├── RoleMiddleware.php
│   │   │   └── ThrottleRequests.php
│   │   ├── Requests/
│   │   │   ├── Auth/
│   │   │   │   ├── LoginRequest.php
│   │   │   │   └── RegisterRequest.php
│   │   │   ├── Field/
│   │   │   │   ├── CreateFieldRequest.php
│   │   │   │   └── UpdateFieldRequest.php
│   │   │   ├── Subsidy/
│   │   │   │   └── CreateClaimRequest.php
│   │   │   └── Report/
│   │   │       └── GenerateReportRequest.php
│   │   └── Resources/
│   │       ├── UserResource.php
│   │       ├── FieldResource.php
│   │       ├── AlertResource.php
│   │       ├── ClaimResource.php
│   │       └── ReportResource.php
│   ├── Jobs/
│   │   ├── ProcessSatelliteData.php
│   │   ├── GenerateFieldAlerts.php
│   │   ├── SendSMSNotification.php
│   │   ├── SendEmailNotification.php
│   │   ├── GenerateComplianceReport.php
│   │   └── SyncWeatherData.php
│   ├── Listeners/
│   │   ├── SendWelcomeEmail.php
│   │   ├── LogFieldActivity.php
│   │   ├── ProcessAlertNotification.php
│   │   └── UpdateClaimStatus.php
│   ├── Mail/
│   │   ├── WelcomeEmail.php
│   │   ├── AlertNotification.php
│   │   ├── ClaimStatusUpdate.php
│   │   └── ReportGenerated.php
│   ├── Models/
│   │   ├── User.php
│   │   ├── Organization.php
│   │   ├── Field.php
│   │   ├── SatelliteData.php
│   │   ├── SubsidyClaim.php
│   │   ├── Alert.php
│   │   ├── Report.php
│   │   ├── Notification.php
│   │   └── AuditLog.php
│   ├── Notifications/
│   │   ├── AlertNotification.php
│   │   ├── ClaimStatusNotification.php
│   │   └── ReportReadyNotification.php
│   ├── Policies/
│   │   ├── FieldPolicy.php
│   │   ├── ClaimPolicy.php
│   │   ├── ReportPolicy.php
│   │   └── AlertPolicy.php
│   ├── Providers/
│   │   ├── AppServiceProvider.php
│   │   ├── AuthServiceProvider.php
│   │   ├── EventServiceProvider.php
│   │   ├── RouteServiceProvider.php
│   │   └── GeospatialServiceProvider.php
│   ├── Services/
│   │   ├── Auth/
│   │   │   ├── AuthService.php
│   │   │   └── OTPService.php
│   │   ├── Geospatial/
│   │   │   ├── GeospatialService.php
│   │   │   ├── SatelliteService.php
│   │   │   └── NDVICalculationService.php
│   │   ├── Alert/
│   │   │   ├── AlertService.php
│   │   │   ├── DroughtDetectionService.php
│   │   │   └── StressAnalysisService.php
│   │   ├── Verification/
│   │   │   ├── VerificationService.php
│   │   │   ├── FraudDetectionService.php
│   │   │   └── ComplianceService.php
│   │   ├── AI/
│   │   │   ├── AIAdvisoryService.php
│   │   │   ├── ChatbotService.php
│   │   │   └── RecommendationEngine.php
│   │   ├── Notification/
│   │   │   ├── NotificationService.php
│   │   │   ├── SMSService.php
│   │   │   ├── EmailService.php
│   │   │   └── WhatsAppService.php
│   │   ├── Report/
│   │   │   ├── ReportService.php
│   │   │   ├── AnalyticsService.php
│   │   │   └── ExportService.php
│   │   └── External/
│   │       ├── WeatherAPIService.php
│   │       ├── SatelliteAPIService.php
│   │       └── MapService.php
│   └── Traits/
│       ├── HasUuid.php
│       ├── HasGeospatialData.php
│       └── Auditable.php
```

### Frontend Structure (React + TypeScript)

```
src/resources/
├── js/
│   ├── Components/
│   │   ├── UI/                   # ShadCN UI Components
│   │   │   ├── Alert.tsx
│   │   │   ├── Badge.tsx
│   │   │   ├── Button.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Checkbox.tsx
│   │   │   ├── Dialog.tsx
│   │   │   ├── DropdownMenu.tsx
│   │   │   ├── Form.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Label.tsx
│   │   │   ├── Select.tsx
│   │   │   ├── Sheet.tsx
│   │   │   ├── Table.tsx
│   │   │   ├── Tabs.tsx
│   │   │   ├── Textarea.tsx
│   │   │   └── Toast.tsx
│   │   ├── Layout/
│   │   │   ├── AppLayout.tsx
│   │   │   ├── AuthLayout.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   ├── Header.tsx
│   │   │   ├── Footer.tsx
│   │   │   ├── Navigation.tsx
│   │   │   └── MobileMenu.tsx
│   │   ├── Auth/
│   │   │   ├── LoginForm.tsx
│   │   │   ├── RegisterForm.tsx
│   │   │   ├── OTPForm.tsx
│   │   │   └── RoleSelector.tsx
│   │   ├── Dashboard/
│   │   │   ├── KPIWidget.tsx
│   │   │   ├── AlertPanel.tsx
│   │   │   ├── FieldSummary.tsx
│   │   │   ├── QuickActions.tsx
│   │   │   ├── RecentActivity.tsx
│   │   │   └── WeatherWidget.tsx
│   │   ├── Maps/
│   │   │   ├── FieldMap.tsx
│   │   │   ├── SatelliteLayer.tsx
│   │   │   ├── AlertLayer.tsx
│   │   │   ├── TimelineControl.tsx
│   │   │   ├── MapControls.tsx
│   │   │   ├── FieldPopup.tsx
│   │   │   └── DrawingTools.tsx
│   │   ├── Fields/
│   │   │   ├── FieldList.tsx
│   │   │   ├── FieldCard.tsx
│   │   │   ├── FieldForm.tsx
│   │   │   ├── FieldDetails.tsx
│   │   │   ├── SatelliteTimeline.tsx
│   │   │   ├── NDVIChart.tsx
│   │   │   └── FieldMetrics.tsx
│   │   ├── Alerts/
│   │   │   ├── AlertList.tsx
│   │   │   ├── AlertCard.tsx
│   │   │   ├── AlertDetails.tsx
│   │   │   ├── AlertFilters.tsx
│   │   │   └── AlertActions.tsx
│   │   ├── Subsidies/
│   │   │   ├── ClaimList.tsx
│   │   │   ├── ClaimCard.tsx
│   │   │   ├── ClaimForm.tsx
│   │   │   ├── VerificationPanel.tsx
│   │   │   ├── ComplianceReport.tsx
│   │   │   └── FraudIndicators.tsx
│   │   ├── Reports/
│   │   │   ├── ReportBuilder.tsx
│   │   │   ├── ReportList.tsx
│   │   │   ├── ReportViewer.tsx
│   │   │   ├── ExportOptions.tsx
│   │   │   └── AnalyticsDashboard.tsx
│   │   ├── AI/
│   │   │   ├── Chatbot.tsx
│   │   │   ├── ChatMessage.tsx
│   │   │   ├── AdvisoryPanel.tsx
│   │   │   ├── RecommendationCard.tsx
│   │   │   └── LanguageSelector.tsx
│   │   ├── Charts/
│   │   │   ├── NDVIChart.tsx
│   │   │   ├── YieldChart.tsx
│   │   │   ├── TrendChart.tsx
│   │   │   ├── BarChart.tsx
│   │   │   ├── LineChart.tsx
│   │   │   └── PieChart.tsx
│   │   ├── Forms/
│   │   │   ├── FieldForm.tsx
│   │   │   ├── ClaimForm.tsx
│   │   │   ├── ReportForm.tsx
│   │   │   ├── UserForm.tsx
│   │   │   └── OrganizationForm.tsx
│   │   └── Common/
│   │       ├── LoadingSpinner.tsx
│   │       ├── ErrorBoundary.tsx
│   │       ├── Pagination.tsx
│   │       ├── SearchBox.tsx
│   │       ├── DatePicker.tsx
│   │       ├── FileUpload.tsx
│   │       └── ConfirmDialog.tsx
│   ├── Pages/
│   │   ├── Auth/
│   │   │   ├── Login.tsx
│   │   │   ├── Register.tsx
│   │   │   ├── VerifyOTP.tsx
│   │   │   └── ForgotPassword.tsx
│   │   ├── Dashboard/
│   │   │   └── Index.tsx
│   │   ├── Fields/
│   │   │   ├── Index.tsx
│   │   │   ├── Show.tsx
│   │   │   ├── Create.tsx
│   │   │   └── Edit.tsx
│   │   ├── Alerts/
│   │   │   ├── Index.tsx
│   │   │   └── Show.tsx
│   │   ├── Subsidies/
│   │   │   ├── Index.tsx
│   │   │   ├── Create.tsx
│   │   │   ├── Show.tsx
│   │   │   └── Verify.tsx
│   │   ├── Reports/
│   │   │   ├── Index.tsx
│   │   │   ├── Create.tsx
│   │   │   └── Show.tsx
│   │   ├── AI/
│   │   │   └── Advisory.tsx
│   │   └── Settings/
│   │       ├── Profile.tsx
│   │       ├── Organization.tsx
│   │       ├── Notifications.tsx
│   │       └── Security.tsx
│   ├── Hooks/
│   │   ├── useAuth.ts
│   │   ├── useFields.ts
│   │   ├── useAlerts.ts
│   │   ├── useSubsidies.ts
│   │   ├── useReports.ts
│   │   ├── useGeolocation.ts
│   │   ├── useWebSocket.ts
│   │   └── useLocalStorage.ts
│   ├── Types/
│   │   ├── index.ts
│   │   ├── User.ts
│   │   ├── Organization.ts
│   │   ├── Field.ts
│   │   ├── SatelliteData.ts
│   │   ├── Alert.ts
│   │   ├── SubsidyClaim.ts
│   │   ├── Report.ts
│   │   ├── Geospatial.ts
│   │   └── API.ts
│   ├── Utils/
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   ├── geospatial.ts
│   │   ├── formatting.ts
│   │   ├── validation.ts
│   │   ├── constants.ts
│   │   ├── helpers.ts
│   │   └── storage.ts
│   ├── Stores/
│   │   ├── authStore.ts
│   │   ├── fieldStore.ts
│   │   ├── alertStore.ts
│   │   ├── uiStore.ts
│   │   └── settingsStore.ts
│   ├── Contexts/
│   │   ├── AuthContext.tsx
│   │   ├── ThemeContext.tsx
│   │   └── NotificationContext.tsx
│   └── app.tsx
├── css/
│   ├── app.css
│   ├── components.css
│   └── utilities.css
└── views/
    └── app.blade.php
```

### Database Structure

```
src/database/
├── migrations/
│   ├── 2024_01_01_000000_create_users_table.php
│   ├── 2024_01_01_000001_create_organizations_table.php
│   ├── 2024_01_01_000002_create_fields_table.php
│   ├── 2024_01_01_000003_create_satellite_data_table.php
│   ├── 2024_01_01_000004_create_subsidy_claims_table.php
│   ├── 2024_01_01_000005_create_alerts_table.php
│   ├── 2024_01_01_000006_create_reports_table.php
│   ├── 2024_01_01_000007_create_notifications_table.php
│   └── 2024_01_01_000008_create_audit_logs_table.php
├── seeders/
│   ├── DatabaseSeeder.php
│   ├── UserSeeder.php
│   ├── OrganizationSeeder.php
│   ├── FieldSeeder.php
│   ├── RolePermissionSeeder.php
│   └── SampleDataSeeder.php
└── factories/
    ├── UserFactory.php
    ├── OrganizationFactory.php
    ├── FieldFactory.php
    ├── SatelliteDataFactory.php
    ├── SubsidyClaimFactory.php
    ├── AlertFactory.php
    └── ReportFactory.php
```

### Configuration Structure

```
src/config/
├── app.php
├── auth.php
├── cache.php
├── database.php
├── filesystems.php
├── mail.php
├── queue.php
├── services.php
├── session.php
├── geospatial.php
├── satellite.php
├── notifications.php
└── ai.php
```

### Testing Structure

```
src/tests/
├── Feature/
│   ├── Auth/
│   │   ├── LoginTest.php
│   │   ├── RegisterTest.php
│   │   └── OTPTest.php
│   ├── Field/
│   │   ├── FieldManagementTest.php
│   │   ├── SatelliteDataTest.php
│   │   └── GeospatialTest.php
│   ├── Alert/
│   │   ├── AlertGenerationTest.php
│   │   └── NotificationTest.php
│   ├── Subsidy/
│   │   ├── ClaimSubmissionTest.php
│   │   └── VerificationTest.php
│   └── Report/
│       ├── ReportGenerationTest.php
│       └── AnalyticsTest.php
├── Unit/
│   ├── Services/
│   │   ├── GeospatialServiceTest.php
│   │   ├── AlertServiceTest.php
│   │   ├── VerificationServiceTest.php
│   │   └── AIAdvisoryServiceTest.php
│   ├── Models/
│   │   ├── UserTest.php
│   │   ├── FieldTest.php
│   │   ├── AlertTest.php
│   │   └── ClaimTest.php
│   └── Utils/
│       ├── GeospatialUtilsTest.php
│       └── ValidationUtilsTest.php
├── Browser/
│   ├── AuthTest.php
│   ├── DashboardTest.php
│   ├── FieldManagementTest.php
│   └── ReportingTest.php
├── TestCase.php
└── CreatesApplication.php
```

This structure provides a comprehensive organization for the AgroVue MVP project, ensuring clean separation of concerns, maintainability, and scalability while following Laravel and React best practices.
