# AgroVue System Architecture
**Vuexy + Laravel 12 + MySQL Implementation**

## Project Overview

AgroVue is a satellite-powered agricultural intelligence platform built with Vuexy admin template, Laravel 12, and MySQL to provide real-time monitoring, fraud detection, and analytics for agricultural stakeholders across Africa.

## Technology Stack

### Frontend (Vuexy)
- **Framework**: Vue.js 3 with Composition API
- **Language**: TypeScript
- **UI Template**: Vuexy Admin Template v9.x
- **State Management**: Pinia
- **HTTP Client**: Axios
- **Build Tool**: Vite 5.x
- **CSS Framework**: Tailwind CSS (integrated with Vuexy)
- **Charts**: ApexCharts (integrated with Vuexy)
- **Icons**: Tabler Icons, Heroicons
- **Maps**: Leaflet.js with OpenStreetMap/Satellite layers
- **Forms**: VeeValidate with Yup validation

### Backend (Laravel 12)
- **Framework**: Lara<PERSON> 12
- **Language**: PHP 8.2+
- **Database**: MySQL 8.0+ with spatial data support
- **Authentication**: Laravel Sanctum
- **API**: RESTful API with JSON responses
- **Queue System**: Redis/Database queues for background processing
- **Cache**: Redis for application caching
- **Search**: MySQL Full-Text Search
- **File Storage**: Local filesystem (development), AWS S3 (production)
- **Testing**: PHPUnit + Pest

### Infrastructure
- **Web Server**: Nginx/Apache
- **Application Server**: PHP-FPM
- **Database**: MySQL 8.0+ with spatial extensions
- **Cache/Queue**: Redis 7.x
- **Process Manager**: Supervisor for queue workers
- **Monitoring**: Laravel Telescope (development), Laravel Horizon (queue monitoring)
- **Deployment**: Docker containers (optional)

## System Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        A[Vuexy Frontend<br/>Vue.js 3 + TypeScript]
        B[Mobile PWA<br/>Responsive Design]
    end
    
    subgraph "API Gateway"
        C[Laravel 12 API<br/>RESTful Endpoints]
        D[Authentication<br/>Laravel Sanctum]
    end
    
    subgraph "Application Layer"
        E[Controllers<br/>Request Handling]
        F[Services<br/>Business Logic]
        G[Jobs<br/>Background Processing]
        H[Events<br/>Real-time Updates]
    end
    
    subgraph "Data Layer"
        I[MySQL Database<br/>Spatial Data Support]
        J[Redis Cache<br/>Session & Queue]
        K[File Storage<br/>S3/Local]
    end
    
    subgraph "External Services"
        L[Satellite APIs<br/>Sentinel-2, Landsat]
        M[Weather APIs<br/>OpenWeatherMap]
        N[SMS Gateway<br/>Twilio/Local]
        O[Email Service<br/>SMTP/SES]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    G --> I
    G --> J
    H --> J
    I --> K
    F --> L
    F --> M
    F --> N
    F --> O
```

## Database Schema (MySQL)

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('farmer', 'government_admin', 'ngo_coordinator', 'bank_analyst', 'corporate') NOT NULL,
    phone VARCHAR(20),
    language VARCHAR(5) DEFAULT 'en',
    organization_id BIGINT UNSIGNED NULL,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    remember_token VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_role (role),
    INDEX idx_organization (organization_id),
    INDEX idx_active (is_active)
);
```

#### Fields Table
```sql
CREATE TABLE fields (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    field_id VARCHAR(50) UNIQUE NOT NULL,
    owner_id BIGINT UNSIGNED NOT NULL,
    organization_id BIGINT UNSIGNED NULL,
    area_hectares DECIMAL(10,2) NOT NULL,
    crop_type VARCHAR(100) NOT NULL,
    crop_variety VARCHAR(100),
    coordinates GEOMETRY NOT NULL,
    center_latitude DECIMAL(10,8) NOT NULL,
    center_longitude DECIMAL(11,8) NOT NULL,
    state VARCHAR(100) NOT NULL,
    lga VARCHAR(100) NOT NULL,
    ward VARCHAR(100),
    status ENUM('active', 'inactive', 'disputed', 'verified') DEFAULT 'active',
    planting_date DATE,
    expected_harvest_date DATE,
    expected_yield_tons DECIMAL(8,2),
    soil_data JSON,
    notes TEXT,
    is_mapped BOOLEAN DEFAULT FALSE,
    last_satellite_update TIMESTAMP NULL,
    health_status ENUM('excellent', 'good', 'fair', 'poor', 'critical'),
    latest_ndvi DECIMAL(6,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE SET NULL,
    SPATIAL INDEX idx_coordinates (coordinates),
    INDEX idx_owner (owner_id),
    INDEX idx_crop_type (crop_type),
    INDEX idx_status (status),
    INDEX idx_health (health_status)
);
```

#### Satellite Data Table
```sql
CREATE TABLE satellite_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    field_id BIGINT UNSIGNED NOT NULL,
    satellite_source ENUM('sentinel-2', 'landsat-8', 'landsat-9', 'modis') NOT NULL,
    capture_date DATE NOT NULL,
    cloud_coverage DECIMAL(5,2),
    ndvi_average DECIMAL(6,4) NOT NULL,
    ndvi_min DECIMAL(6,4),
    ndvi_max DECIMAL(6,4),
    ndvi_std DECIMAL(6,4),
    evi_average DECIMAL(6,4),
    savi_average DECIMAL(6,4),
    vegetation_health ENUM('excellent', 'good', 'fair', 'poor', 'critical') NOT NULL,
    change_detection JSON,
    anomalies_detected JSON,
    image_url VARCHAR(500),
    metadata JSON,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (field_id) REFERENCES fields(id) ON DELETE CASCADE,
    INDEX idx_field_date (field_id, capture_date),
    INDEX idx_capture_date (capture_date),
    INDEX idx_vegetation_health (vegetation_health)
);
```

## Frontend Architecture (Vuexy)

### Project Structure
```
src/
├── assets/                 # Static assets
├── components/            # Reusable Vue components
│   ├── common/           # Common UI components
│   ├── charts/           # Chart components
│   └── forms/            # Form components
├── layouts/              # Layout components
│   ├── DefaultLayout.vue
│   ├── AuthLayout.vue
│   └── MobileLayout.vue
├── pages/                # Page components
│   ├── auth/            # Authentication pages
│   ├── dashboard/       # Dashboard pages
│   ├── fields/          # Field management
│   ├── alerts/          # Alert management
│   └── reports/         # Reporting pages
├── stores/               # Pinia stores
│   ├── auth.ts          # Authentication store
│   ├── fields.ts        # Fields store
│   └── alerts.ts        # Alerts store
├── services/             # API services
│   ├── api.ts           # Base API configuration
│   ├── auth.ts          # Auth API calls
│   └── fields.ts        # Fields API calls
├── utils/                # Utility functions
├── types/                # TypeScript type definitions
└── router/               # Vue Router configuration
```

### State Management (Pinia)
```typescript
// stores/auth.ts
export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isAuthenticated = computed(() => !!token.value)
  
  const login = async (credentials: LoginCredentials) => {
    // Login logic
  }
  
  const logout = async () => {
    // Logout logic
  }
  
  return { user, token, isAuthenticated, login, logout }
})
```

## Backend Architecture (Laravel 12)

### Project Structure
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Api/
│   │   │   ├── AuthController.php
│   │   │   ├── FieldController.php
│   │   │   ├── SatelliteDataController.php
│   │   │   └── AlertController.php
│   │   └── Web/
│   ├── Middleware/
│   ├── Requests/
│   └── Resources/
├── Models/
│   ├── User.php
│   ├── Field.php
│   ├── SatelliteData.php
│   └── Alert.php
├── Services/
│   ├── SatelliteDataService.php
│   ├── FraudDetectionService.php
│   └── AlertService.php
├── Jobs/
│   ├── ProcessSatelliteData.php
│   └── SendAlertNotifications.php
├── Events/
│   ├── FieldCreated.php
│   └── AlertGenerated.php
└── Listeners/
    ├── UpdateFieldStatistics.php
    └── SendAlertNotification.php
```

### API Routes Structure
```php
// routes/api.php
Route::prefix('v1')->group(function () {
    // Authentication
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    
    Route::middleware('auth:sanctum')->group(function () {
        // Fields
        Route::apiResource('fields', FieldController::class);
        Route::post('fields/{field}/satellite-data', [SatelliteDataController::class, 'store']);
        
        // Alerts
        Route::apiResource('alerts', AlertController::class);
        Route::patch('alerts/{alert}/acknowledge', [AlertController::class, 'acknowledge']);
        
        // Reports
        Route::get('reports/dashboard', [ReportController::class, 'dashboard']);
        Route::get('reports/export', [ReportController::class, 'export']);
    });
});
```

## Security Architecture

### Authentication & Authorization
- **Laravel Sanctum**: Token-based authentication
- **Role-Based Access Control**: User roles with specific permissions
- **API Rate Limiting**: Prevent abuse and ensure fair usage
- **CORS Configuration**: Secure cross-origin requests

### Data Security
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Eloquent ORM and prepared statements
- **XSS Protection**: Output escaping and CSP headers
- **CSRF Protection**: Token-based CSRF protection
- **Encryption**: Sensitive data encryption at rest

## Performance Optimization

### Frontend (Vuexy)
- **Code Splitting**: Route-based code splitting
- **Lazy Loading**: Component lazy loading
- **Caching**: HTTP caching and service workers
- **Bundle Optimization**: Tree shaking and minification
- **Image Optimization**: WebP format and responsive images

### Backend (Laravel)
- **Database Indexing**: Optimized database indexes
- **Query Optimization**: Eager loading and query optimization
- **Caching**: Redis caching for frequently accessed data
- **Queue Processing**: Background job processing
- **Response Caching**: API response caching

## Deployment Architecture

### Development Environment
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: agrovue
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
  
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
```

### Production Environment
- **Load Balancer**: Nginx with SSL termination
- **Application Servers**: Multiple PHP-FPM instances
- **Database**: MySQL with read replicas
- **Cache**: Redis cluster
- **File Storage**: AWS S3 or DigitalOcean Spaces
- **Monitoring**: Application and infrastructure monitoring

## Integration Points

### External APIs
- **Satellite Data**: Sentinel Hub, NASA APIs
- **Weather Data**: OpenWeatherMap, AccuWeather
- **SMS Gateway**: Twilio, Africa's Talking
- **Email Service**: AWS SES, SendGrid
- **Maps**: OpenStreetMap, Google Maps
- **Payment**: Stripe, Paystack (if needed)

### Data Flow
1. **User Registration**: Frontend → Laravel API → MySQL
2. **Field Creation**: Frontend → Laravel API → MySQL → Background Job
3. **Satellite Data**: External API → Laravel Job → MySQL → Frontend
4. **Alert Generation**: Background Job → MySQL → Real-time Notification → Frontend
5. **Report Generation**: Frontend Request → Laravel Service → MySQL → PDF/Excel Export

This architecture provides a scalable, maintainable, and secure foundation for the AgroVue platform using modern web technologies.
