version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: agrovue_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - agrovue
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    container_name: agrovue_nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - agrovue
    depends_on:
      - app

  db:
    image: postgis/postgis:15-3.3
    container_name: agrovue_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: agrovue_mvp
      POSTGRES_USER: agrovue
      POSTGRES_PASSWORD: password
      POSTGRES_ROOT_PASSWORD: password
    volumes:
      - dbdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - agrovue

  redis:
    image: redis:7-alpine
    container_name: agrovue_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - agrovue

  node:
    image: node:18-alpine
    container_name: agrovue_node
    working_dir: /var/www
    volumes:
      - ./:/var/www
    command: sh -c "npm install && npm run dev"
    ports:
      - "5173:5173"
    networks:
      - agrovue

  mailhog:
    image: mailhog/mailhog
    container_name: agrovue_mailhog
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - agrovue

networks:
  agrovue:
    driver: bridge

volumes:
  dbdata:
    driver: local
