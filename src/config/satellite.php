<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Satellite Data Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for satellite data sources and processing
    |
    */

    'sources' => [
        'sentinel' => [
            'api_url' => env('SENTINEL_API_URL', 'https://scihub.copernicus.eu/dhus'),
            'username' => env('SENTINEL_USERNAME'),
            'password' => env('SENTINEL_PASSWORD'),
            'enabled' => env('SENTINEL_ENABLED', true),
        ],
        
        'landsat' => [
            'api_url' => env('LANDSAT_API_URL', 'https://m2m.cr.usgs.gov/api/api/json/stable'),
            'username' => env('LANDSAT_USERNAME'),
            'password' => env('LANDSAT_PASSWORD'),
            'enabled' => env('LANDSAT_ENABLED', true),
        ],
        
        'google_earth_engine' => [
            'service_account_key' => env('GOOGLE_EARTH_ENGINE_KEY'),
            'enabled' => env('GOOGLE_EARTH_ENGINE_ENABLED', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | NDVI Thresholds
    |--------------------------------------------------------------------------
    |
    | NDVI value thresholds for different vegetation health levels
    |
    */

    'ndvi_thresholds' => [
        'critical' => 0.2,
        'stressed' => 0.4,
        'moderate' => 0.6,
        'healthy' => 0.8,
    ],

    /*
    |--------------------------------------------------------------------------
    | Processing Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for satellite data processing
    |
    */

    'processing' => [
        'cloud_cover_threshold' => 20, // Maximum acceptable cloud cover percentage
        'data_retention_days' => 365, // How long to keep satellite data
        'processing_queue' => 'satellite-processing',
        'batch_size' => 50, // Number of fields to process in one batch
    ],

    /*
    |--------------------------------------------------------------------------
    | Alert Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for generating alerts from satellite data
    |
    */

    'alerts' => [
        'drought_threshold' => 0.3, // NDVI threshold for drought alerts
        'stress_threshold' => 0.4, // NDVI threshold for stress alerts
        'consecutive_days' => 7, // Days of low NDVI before generating alert
        'enabled_types' => [
            'drought',
            'stress',
            'flood',
            'pest',
            'disease',
        ],
    ],

];
