<?php

return [

    /*
    |--------------------------------------------------------------------------
    | AgroVue Application Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration specific to the AgroVue application
    |
    */

    'version' => '1.0.0',

    /*
    |--------------------------------------------------------------------------
    | Supported Languages
    |--------------------------------------------------------------------------
    |
    | Languages supported by the application for multilingual features
    |
    */

    'languages' => [
        'en' => 'English',
        'ha' => 'Hausa',
        'sw' => 'Swahili',
        'fr' => 'French',
    ],

    /*
    |--------------------------------------------------------------------------
    | User Roles Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for user roles and their permissions
    |
    */

    'roles' => [
        'government' => [
            'name' => 'Government Official',
            'permissions' => [
                'view_all_fields',
                'verify_claims',
                'generate_compliance_reports',
                'manage_subsidies',
            ],
        ],
        'ngo' => [
            'name' => 'NGO Coordinator',
            'permissions' => [
                'view_disaster_alerts',
                'export_field_data',
                'send_advisories',
                'generate_impact_reports',
            ],
        ],
        'corporate' => [
            'name' => 'Corporate User',
            'permissions' => [
                'view_supply_chain',
                'monitor_contracts',
                'generate_yield_reports',
            ],
        ],
        'coop' => [
            'name' => 'Cooperative Member',
            'permissions' => [
                'manage_member_fields',
                'view_yield_forecasts',
                'access_advisories',
                'export_member_data',
            ],
        ],
        'bank' => [
            'name' => 'Bank Analyst',
            'permissions' => [
                'assess_loan_risk',
                'verify_field_activity',
                'generate_risk_reports',
                'monitor_borrowers',
            ],
        ],
        'admin' => [
            'name' => 'System Administrator',
            'permissions' => [
                'manage_users',
                'manage_organizations',
                'system_configuration',
                'view_system_logs',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Channels
    |--------------------------------------------------------------------------
    |
    | Available notification channels and their configuration
    |
    */

    'notifications' => [
        'channels' => [
            'email' => [
                'enabled' => true,
                'driver' => 'mail',
            ],
            'sms' => [
                'enabled' => env('SMS_ENABLED', true),
                'driver' => 'twilio',
                'from' => env('TWILIO_FROM'),
            ],
            'whatsapp' => [
                'enabled' => env('WHATSAPP_ENABLED', false),
                'token' => env('WHATSAPP_TOKEN'),
                'phone_number_id' => env('WHATSAPP_PHONE_NUMBER_ID'),
            ],
            'push' => [
                'enabled' => env('PUSH_NOTIFICATIONS_ENABLED', true),
                'driver' => 'pusher',
            ],
        ],
        
        'templates' => [
            'alert_notification' => [
                'subject' => 'AgroVue Alert: {alert_type}',
                'channels' => ['email', 'sms', 'push'],
            ],
            'claim_status_update' => [
                'subject' => 'Subsidy Claim Update: {claim_number}',
                'channels' => ['email', 'sms'],
            ],
            'report_ready' => [
                'subject' => 'Report Ready: {report_title}',
                'channels' => ['email', 'push'],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | AI Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for AI-powered features
    |
    */

    'ai' => [
        'chatbot' => [
            'enabled' => env('AI_CHATBOT_ENABLED', true),
            'model' => env('AI_MODEL', 'gpt-4'),
            'api_key' => env('OPENAI_API_KEY'),
            'max_tokens' => 500,
            'temperature' => 0.7,
        ],
        
        'recommendations' => [
            'enabled' => true,
            'update_frequency' => 'daily', // daily, weekly, monthly
            'confidence_threshold' => 0.8,
        ],
        
        'fraud_detection' => [
            'enabled' => true,
            'threshold' => 70, // Fraud score threshold (0-100)
            'factors' => [
                'ndvi_inconsistency',
                'area_mismatch',
                'temporal_anomalies',
                'historical_patterns',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Geospatial Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for geospatial features and mapping
    |
    */

    'geospatial' => [
        'default_bounds' => [
            'north' => 13.9,
            'south' => 4.3,
            'east' => 14.7,
            'west' => 2.7,
        ], // Nigeria bounds
        
        'map_providers' => [
            'default' => 'openstreetmap',
            'satellite' => 'mapbox',
            'terrain' => 'opentopomap',
        ],
        
        'coordinate_system' => 'EPSG:4326', // WGS84
        'precision' => 6, // Decimal places for coordinates
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for file uploads and storage
    |
    */

    'uploads' => [
        'max_file_size' => 10240, // KB
        'allowed_types' => [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'documents' => ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
            'geospatial' => ['shp', 'kml', 'geojson', 'gpx'],
        ],
        'storage_disk' => env('FILESYSTEM_DISK', 'local'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Report Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for report generation
    |
    */

    'reports' => [
        'formats' => ['pdf', 'csv', 'excel', 'json'],
        'retention_days' => 90,
        'max_concurrent_generations' => 5,
        'queue' => 'report-generation',
    ],

];
