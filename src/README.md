# AgroVue MVP - Source Code Directory

## Overview

This `src/` directory contains all the implementation code for the AgroVue MVP project using Laravel 12 with React Inertia.js and ShadCN UI components.

## Technology Stack

- **Backend**: Laravel 12 with PHP 8.3+
- **Frontend**: React 18 with TypeScript
- **Integration**: Laravel Inertia.js for seamless SPA experience
- **UI Framework**: ShadCN UI components with Tailwind CSS
- **Database**: PostgreSQL with PostGIS extension
- **Cache/Queue**: Redis
- **Maps**: Leaflet.js with satellite imagery layers

## Directory Structure

```
src/
├── app/                    # Laravel application logic
├── database/              # Migrations, seeders, factories
├── resources/             # Frontend React components and assets
├── routes/                # API and web routes
├── config/                # Configuration files
├── storage/               # File storage and logs
└── tests/                 # Test files
```

## Getting Started

### Prerequisites

- PHP 8.3 or higher
- Node.js 18 or higher
- PostgreSQL 14+ with PostGIS extension
- Redis 6+
- Composer
- npm or yarn

### Installation

1. **Install PHP dependencies**:
   ```bash
   composer install
   ```

2. **Install Node.js dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment**:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Configure database**:
   - Update `.env` with PostgreSQL credentials
   - Enable PostGIS extension in PostgreSQL

5. **Run migrations**:
   ```bash
   php artisan migrate --seed
   ```

6. **Build frontend assets**:
   ```bash
   npm run dev
   ```

7. **Start development server**:
   ```bash
   php artisan serve
   ```

## Development Workflow

### Backend Development (Laravel)

- **Models**: Located in `app/Models/`
- **Controllers**: Organized by feature in `app/Http/Controllers/`
- **Services**: Business logic in `app/Services/`
- **Jobs**: Background tasks in `app/Jobs/`
- **Tests**: Feature and unit tests in `tests/`

### Frontend Development (React)

- **Components**: Reusable UI components in `resources/js/Components/`
- **Pages**: Inertia.js pages in `resources/js/Pages/`
- **Types**: TypeScript definitions in `resources/js/Types/`
- **Hooks**: Custom React hooks in `resources/js/Hooks/`
- **Utils**: Helper functions in `resources/js/Utils/`

### Key Features Implementation

1. **Authentication**: Multi-role authentication with OTP support
2. **Field Management**: GPS-based field mapping with satellite data
3. **Alert System**: Real-time notifications for agricultural events
4. **Subsidy Verification**: Satellite-based fraud detection
5. **AI Advisory**: Multilingual chatbot for farming recommendations
6. **Reporting**: Customizable reports with export capabilities

## API Documentation

The API follows RESTful conventions with the following base endpoints:

- `/api/auth/*` - Authentication endpoints
- `/api/fields/*` - Field management
- `/api/alerts/*` - Alert system
- `/api/subsidies/*` - Subsidy verification
- `/api/reports/*` - Report generation
- `/api/ai/*` - AI advisory services

## Testing

Run the test suite:

```bash
# Backend tests
php artisan test

# Frontend tests
npm run test

# End-to-end tests
npm run test:e2e
```

## Deployment

The application is containerized using Docker for easy deployment:

```bash
# Build and start containers
docker-compose up -d

# Run migrations in production
docker-compose exec app php artisan migrate --force
```

## Contributing

1. Follow PSR-12 coding standards for PHP
2. Use TypeScript for all React components
3. Write tests for new features
4. Update documentation as needed
5. Follow conventional commit messages

## Architecture Decisions

- **Inertia.js**: Chosen for seamless SPA experience without API complexity
- **ShadCN UI**: Provides consistent, accessible UI components
- **PostGIS**: Essential for geospatial data handling
- **Redis**: Used for caching and queue management
- **TypeScript**: Ensures type safety in frontend development

## Performance Considerations

- Database queries are optimized with proper indexing
- Satellite imagery is cached and served via CDN
- Frontend code is split and lazy-loaded
- API responses are cached where appropriate
- Background jobs handle heavy processing

## Security Measures

- All inputs are validated and sanitized
- Authentication uses Laravel Sanctum
- Role-based access control is implemented
- Data is encrypted at rest and in transit
- Regular security audits are conducted

## Monitoring & Logging

- Application logs are structured and centralized
- Performance metrics are tracked
- Error tracking is implemented with Sentry
- Health checks monitor system status

For detailed documentation, see the `/doc` directory in the project root.
