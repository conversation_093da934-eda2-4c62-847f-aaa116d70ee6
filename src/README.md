# AgroVue MVP - Satellite-Powered Agricultural Intelligence Platform

AgroVue is a comprehensive agricultural intelligence platform that leverages satellite data, AI, and modern web technologies to provide real-time insights for farmers, government agencies, NGOs, cooperatives, and financial institutions across Africa.

## 🌟 Features

### Core Functionality
- **Field Management**: GPS-based field mapping with satellite imagery integration
- **Real-time Monitoring**: NDVI analysis and vegetation health tracking
- **Alert System**: Automated drought, stress, and fraud detection
- **Subsidy Verification**: Satellite-based claim verification and fraud prevention
- **AI Advisory**: Multilingual chatbot for farming recommendations
- **Comprehensive Reporting**: Customizable reports with export capabilities

### User Roles
- **Government Officials**: Subsidy management and compliance monitoring
- **NGO Coordinators**: Disaster response and agricultural aid coordination
- **Farmer Cooperatives**: Member field monitoring and yield optimization
- **Bank Analysts**: Agricultural loan risk assessment
- **Corporate Users**: Supply chain monitoring and contract farming

## 🛠️ Technology Stack

### Backend
- **Laravel 12** with PHP 8.3+
- **PostgreSQL** with PostGIS extension for geospatial data
- **Redis** for caching and queue management
- **Laravel Sanctum** for API authentication

### Frontend
- **React 18** with TypeScript
- **Inertia.js** for seamless SPA experience
- **ShadCN UI** components with Tailwind CSS
- **Leaflet.js** for interactive mapping
- **React Query** for state management

### Infrastructure
- **Docker** for containerization
- **Vite** for fast development builds
- **Laravel Horizon** for queue monitoring
- **Laravel Telescope** for debugging

## 🚀 Quick Start

### Prerequisites
- PHP 8.3 or higher
- Node.js 18 or higher
- PostgreSQL 14+ with PostGIS extension
- Redis 6+
- Composer
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd agrovue/src
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure database**
   Update `.env` with your PostgreSQL credentials:
   ```env
   DB_CONNECTION=pgsql
   DB_HOST=127.0.0.1
   DB_PORT=5432
   DB_DATABASE=agrovue_mvp
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. **Enable PostGIS extension**
   ```sql
   CREATE EXTENSION IF NOT EXISTS postgis;
   ```

7. **Run migrations and seeders**
   ```bash
   php artisan migrate --seed
   ```

8. **Build frontend assets**
   ```bash
   npm run dev
   ```

9. **Start the development server**
   ```bash
   php artisan serve
   ```

Visit `http://localhost:8000` to access the application.

## 👥 Default Users

The seeder creates several test users for different roles:

### Admin
- **Email**: <EMAIL>
- **Password**: password

### Government
- **Email**: <EMAIL>
- **Password**: password

### NGO
- **Email**: <EMAIL>
- **Password**: password

### Corporate
- **Email**: <EMAIL>
- **Password**: password

### Cooperative
- **Email**: <EMAIL>
- **Password**: password

### Bank
- **Email**: <EMAIL>
- **Password**: password

## 📁 Project Structure

```
src/
├── app/
│   ├── Http/
│   │   ├── Controllers/     # Web and API controllers
│   │   ├── Middleware/      # Custom middleware
│   │   └── Requests/        # Form request validation
│   ├── Models/              # Eloquent models
│   └── Services/            # Business logic services
├── database/
│   ├── migrations/          # Database migrations
│   └── seeders/            # Database seeders
├── resources/
│   ├── js/
│   │   ├── Components/      # React components
│   │   ├── Pages/          # Inertia.js pages
│   │   ├── Types/          # TypeScript definitions
│   │   └── Utils/          # Utility functions
│   └── views/              # Blade templates
├── routes/
│   ├── web.php             # Web routes
│   └── api.php             # API routes
└── config/                 # Configuration files
```

## 🔧 Configuration

### Satellite Data APIs
Configure satellite data sources in `.env`:
```env
SENTINEL_API_URL=https://scihub.copernicus.eu/dhus
SENTINEL_USERNAME=your_username
SENTINEL_PASSWORD=your_password

LANDSAT_API_URL=https://m2m.cr.usgs.gov/api/api/json/stable
LANDSAT_USERNAME=your_username
LANDSAT_PASSWORD=your_password
```

### Notification Services
Configure notification channels:
```env
# SMS (Twilio)
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
TWILIO_FROM=your_phone_number

# WhatsApp Business API
WHATSAPP_TOKEN=your_whatsapp_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id

# AI Services
OPENAI_API_KEY=your_openai_api_key
AI_MODEL=gpt-4
```

## 🧪 Testing

Run the test suite:

```bash
# Backend tests
php artisan test

# Frontend tests
npm run test

# End-to-end tests
npm run test:e2e
```

## 📊 API Documentation

The API follows RESTful conventions. Key endpoints:

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Fields
- `GET /api/fields` - List fields
- `POST /api/fields` - Create field
- `GET /api/fields/{id}` - Get field details
- `GET /api/fields/{id}/satellite-data` - Get satellite data

### Alerts
- `GET /api/alerts` - List alerts
- `POST /api/alerts/{id}/acknowledge` - Acknowledge alert
- `POST /api/alerts/{id}/resolve` - Resolve alert

### Reports
- `GET /api/reports` - List reports
- `POST /api/reports/generate` - Generate report
- `GET /api/reports/{id}/download` - Download report

## 🌍 Internationalization

The platform supports multiple languages:
- English (en)
- Hausa (ha)
- Swahili (sw)
- French (fr)

Language files are located in `resources/lang/`.

## 🔒 Security

- All API endpoints require authentication
- Role-based access control (RBAC)
- Input validation and sanitization
- CSRF protection
- Rate limiting
- SQL injection prevention

## 📱 Mobile Support

- Progressive Web App (PWA) capabilities
- Responsive design for all screen sizes
- Offline functionality for critical features
- Touch-friendly interface

## 🚀 Deployment

### Docker Deployment

1. **Build containers**
   ```bash
   docker-compose up -d
   ```

2. **Run migrations**
   ```bash
   docker-compose exec app php artisan migrate --force
   ```

### Production Considerations

- Use environment-specific `.env` files
- Configure proper database connections
- Set up SSL certificates
- Configure CDN for static assets
- Set up monitoring and logging
- Configure backup procedures

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Coding Standards

- Follow PSR-12 for PHP code
- Use TypeScript for all React components
- Write comprehensive tests
- Update documentation as needed
- Follow conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation in `/doc` directory

## 🗺️ Roadmap

### Phase 1 (Current)
- ✅ Core platform functionality
- ✅ Multi-role authentication
- ✅ Field management and monitoring
- ✅ Basic alert system

### Phase 2 (Next)
- 🔄 Advanced AI recommendations
- 🔄 Enhanced fraud detection
- 🔄 Mobile app development
- 🔄 API for third-party integrations

### Phase 3 (Future)
- 📋 Multi-country expansion
- 📋 Blockchain integration
- 📋 Advanced analytics and ML
- 📋 Enterprise features

## 🙏 Acknowledgments

- Sentinel-2 and Landsat satellite data providers
- OpenStreetMap for mapping data
- The Laravel and React communities
- Agricultural experts and stakeholders who provided requirements

---

**AgroVue MVP** - Empowering African agriculture through satellite intelligence and modern technology.
