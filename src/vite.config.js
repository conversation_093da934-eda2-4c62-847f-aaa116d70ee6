import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: 'resources/js/app.tsx',
            refresh: true,
        }),
        react(),
    ],
    resolve: {
        alias: {
            '@': resolve(__dirname, 'resources/js'),
            '@/components': resolve(__dirname, 'resources/js/Components'),
            '@/pages': resolve(__dirname, 'resources/js/Pages'),
            '@/types': resolve(__dirname, 'resources/js/Types'),
            '@/utils': resolve(__dirname, 'resources/js/Utils'),
            '@/hooks': resolve(__dirname, 'resources/js/Hooks'),
            '@/stores': resolve(__dirname, 'resources/js/Stores'),
        },
    },
    define: {
        global: 'globalThis',
    },
    server: {
        hmr: {
            host: 'localhost',
        },
    },
});
