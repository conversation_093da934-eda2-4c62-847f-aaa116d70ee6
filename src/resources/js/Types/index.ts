export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  phone?: string;
  organization?: Organization;
  permissions: string[];
  avatar?: string;
  preferences: UserPreferences;
  email_verified_at?: string;
  phone_verified_at?: string;
  last_login_at?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'government' | 'ngo' | 'corporate' | 'coop' | 'bank' | 'admin';

export interface UserPreferences {
  language: string;
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    whatsapp: boolean;
  };
  dashboard: {
    theme: 'light' | 'dark';
    widgets: string[];
    layout: string;
  };
}

export interface Organization {
  id: string;
  name: string;
  type: OrganizationType;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  country: string;
  region?: string;
  settings: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type OrganizationType = 'government' | 'ngo' | 'corporate' | 'cooperative' | 'bank' | 'research';

export interface Field {
  id: string;
  user_id: string;
  organization_id?: string;
  name: string;
  boundary: GeoJSON.Polygon;
  area_hectares: number;
  crop_type: CropType;
  planting_date?: string;
  harvest_date?: string;
  metadata: Record<string, any>;
  is_active: boolean;
  verification_status: VerificationStatus;
  user?: User;
  organization?: Organization;
  satellite_data?: SatelliteData[];
  latest_satellite_data?: SatelliteData;
  alerts?: Alert[];
  subsidy_claims?: SubsidyClaim[];
  created_at: string;
  updated_at: string;
}

export type CropType = 'rice' | 'maize' | 'wheat' | 'cassava' | 'yam' | 'millet' | 'sorghum' | 'beans' | 'groundnut' | 'cotton' | 'cocoa' | 'palm_oil' | 'vegetables' | 'fruits' | 'other';

export type VerificationStatus = 'pending' | 'verified' | 'flagged' | 'rejected';

export interface SatelliteData {
  id: string;
  field_id: string;
  capture_date: string;
  ndvi_value?: number;
  precipitation?: number;
  temperature?: number;
  humidity?: number;
  image_url?: string;
  data_source: DataSource;
  analysis_data?: Record<string, any>;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export type DataSource = 'sentinel-2' | 'landsat-8' | 'landsat-9' | 'modis' | 'planet';

export interface SubsidyClaim {
  id: string;
  field_id: string;
  user_id: string;
  claim_number: string;
  amount: number;
  subsidy_type: SubsidyType;
  status: ClaimStatus;
  description?: string;
  verification_data?: Record<string, any>;
  documents?: string[];
  fraud_score?: number;
  verification_notes?: string;
  verified_by?: string;
  submitted_at: string;
  verified_at?: string;
  field?: Field;
  user?: User;
  verifier?: User;
  created_at: string;
  updated_at: string;
}

export type SubsidyType = 'fertilizer' | 'seeds' | 'equipment' | 'irrigation' | 'insurance' | 'credit' | 'training' | 'other';

export type ClaimStatus = 'pending' | 'under_review' | 'approved' | 'rejected' | 'flagged';

export interface Alert {
  id: string;
  field_id?: string;
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  description: string;
  metadata?: Record<string, any>;
  affected_area?: GeoJSON.Polygon;
  acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolved: boolean;
  resolved_by?: string;
  resolved_at?: string;
  field?: Field;
  acknowledged_by_user?: User;
  resolved_by_user?: User;
  created_at: string;
  updated_at: string;
}

export type AlertType = 'drought' | 'flood' | 'pest' | 'disease' | 'stress' | 'fraud' | 'weather';

export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface Report {
  id: string;
  user_id: string;
  title: string;
  type: ReportType;
  description?: string;
  parameters?: Record<string, any>;
  data?: Record<string, any>;
  file_path?: string;
  format: ReportFormat;
  status: ReportStatus;
  generated_at?: string;
  user?: User;
  created_at: string;
  updated_at: string;
}

export type ReportType = 'compliance' | 'yield_forecast' | 'field_analysis' | 'disaster_impact' | 'fraud_detection' | 'custom';

export type ReportFormat = 'pdf' | 'csv' | 'excel' | 'json';

export type ReportStatus = 'generating' | 'completed' | 'failed';

export interface PageProps<T extends Record<string, unknown> = Record<string, unknown>> {
  auth: {
    user: User | null;
  };
  flash: {
    message?: string;
    error?: string;
    success?: string;
    warning?: string;
  };
  app: {
    name: string;
    url: string;
    locale: string;
    timezone: string;
  };
  ziggy: {
    location: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface PaginatedData<T> {
  data: T[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface DashboardStats {
  total_fields: number;
  active_alerts: number;
  pending_claims: number;
  verified_fields: number;
  total_area: number;
  avg_ndvi: number;
  recent_activity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: string;
  description: string;
  user?: User;
  created_at: string;
}

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  whatsapp: boolean;
}

export interface FilterOptions {
  crop_type?: CropType[];
  verification_status?: VerificationStatus[];
  alert_type?: AlertType[];
  severity?: AlertSeverity[];
  date_range?: {
    start: string;
    end: string;
  };
  bounds?: MapBounds;
}
