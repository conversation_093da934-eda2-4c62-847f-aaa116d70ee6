import React, { ReactNode } from 'react';
import { Head } from '@inertiajs/react';
import { User } from '@/Types';
import Sidebar from './Sidebar';
import Header from './Header';

interface AppLayoutProps {
  children: ReactNode;
  title?: string;
  user: User;
}

export default function AppLayout({ children, title, user }: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className="min-h-screen bg-background">
      <Head title={title} />
      
      {/* Sidebar */}
      <Sidebar 
        user={user} 
        open={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <Header 
          user={user} 
          onMenuClick={() => setSidebarOpen(true)} 
        />

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
