import React from 'react';
import { PageProps, DashboardStats } from '@/Types';
import AppLayout from '@/Components/Layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/UI/Card';
import { 
  Map, 
  AlertTriangle, 
  DollarSign, 
  CheckCircle,
  TrendingUp,
  Activity
} from 'lucide-react';

interface DashboardPageProps extends PageProps {
  stats: DashboardStats;
}

export default function Dashboard({ auth, stats }: DashboardPageProps) {
  const statCards = [
    {
      title: 'Total Fields',
      value: stats.total_fields.toLocaleString(),
      icon: Map,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Active Alerts',
      value: stats.active_alerts.toLocaleString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      title: 'Pending Claims',
      value: stats.pending_claims.toLocaleString(),
      icon: DollarSign,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Verified Fields',
      value: stats.verified_fields.toLocaleString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
  ];

  return (
    <AppLayout user={auth.user!} title="Dashboard">
      <div className="space-y-6">
        {/* Welcome section */}
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            Welcome back, {auth.user?.name}
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your agricultural data today.
          </p>
        </div>

        {/* Stats grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main content grid */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Recent Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recent_activity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="h-2 w-2 bg-agrovue-500 rounded-full mt-2"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-foreground">
                        {activity.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Quick Stats</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Area</span>
                  <span className="text-sm font-medium">
                    {stats.total_area.toLocaleString()} hectares
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Average NDVI</span>
                  <span className="text-sm font-medium">
                    {stats.avg_ndvi.toFixed(3)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Verification Rate</span>
                  <span className="text-sm font-medium">
                    {((stats.verified_fields / stats.total_fields) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Role-specific sections */}
        {auth.user?.role === 'government' && (
          <Card>
            <CardHeader>
              <CardTitle>Subsidy Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Government-specific subsidy management tools and compliance reports.
              </p>
            </CardContent>
          </Card>
        )}

        {auth.user?.role === 'ngo' && (
          <Card>
            <CardHeader>
              <CardTitle>Disaster Response</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                NGO-specific disaster monitoring and response coordination tools.
              </p>
            </CardContent>
          </Card>
        )}

        {auth.user?.role === 'coop' && (
          <Card>
            <CardHeader>
              <CardTitle>Cooperative Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Cooperative-specific member field monitoring and yield optimization tools.
              </p>
            </CardContent>
          </Card>
        )}

        {auth.user?.role === 'bank' && (
          <Card>
            <CardHeader>
              <CardTitle>Loan Risk Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Bank-specific agricultural loan risk assessment and monitoring tools.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
