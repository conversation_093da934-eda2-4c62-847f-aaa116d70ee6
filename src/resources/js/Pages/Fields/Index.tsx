import React from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import { PageProps, Field, PaginatedData } from '@/Types';
import AppLayout from '@/Components/Layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/UI/Card';
import { Button } from '@/Components/UI/Button';
import { 
  Plus, 
  Search, 
  Filter, 
  Map as MapIcon, 
  Eye,
  Edit,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface FieldsPageProps extends PageProps {
  fields: PaginatedData<Field>;
  filters: {
    crop_type?: string;
    verification_status?: string;
    search?: string;
  };
  cropTypes: Record<string, string>;
  verificationStatuses: Record<string, string>;
}

export default function FieldsIndex({ 
  auth, 
  fields, 
  filters, 
  cropTypes, 
  verificationStatuses 
}: FieldsPageProps) {
  const [searchTerm, setSearchTerm] = React.useState(filters.search || '');
  const [selectedCropType, setSelectedCropType] = React.useState(filters.crop_type || '');
  const [selectedStatus, setSelectedStatus] = React.useState(filters.verification_status || '');

  const handleSearch = () => {
    router.get('/fields', {
      search: searchTerm,
      crop_type: selectedCropType,
      verification_status: selectedStatus,
    }, {
      preserveState: true,
      replace: true,
    });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCropType('');
    setSelectedStatus('');
    router.get('/fields', {}, {
      preserveState: true,
      replace: true,
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'flagged':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'flagged':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getNDVIColor = (ndvi?: number) => {
    if (!ndvi) return 'bg-gray-100';
    if (ndvi < 0.2) return 'bg-red-500';
    if (ndvi < 0.4) return 'bg-orange-500';
    if (ndvi < 0.6) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <AppLayout user={auth.user!} title="Fields">
      <Head title="Fields" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Fields</h1>
            <p className="text-muted-foreground">
              Manage and monitor your agricultural fields
            </p>
          </div>
          <Link href="/fields/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search fields..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-agrovue-500 focus:border-agrovue-500"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
              </div>

              {/* Crop Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Crop Type
                </label>
                <select
                  value={selectedCropType}
                  onChange={(e) => setSelectedCropType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-agrovue-500 focus:border-agrovue-500"
                >
                  <option value="">All Crops</option>
                  {Object.entries(cropTypes).map(([key, label]) => (
                    <option key={key} value={key}>{label}</option>
                  ))}
                </select>
              </div>

              {/* Verification Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-agrovue-500 focus:border-agrovue-500"
                >
                  <option value="">All Statuses</option>
                  {Object.entries(verificationStatuses).map(([key, label]) => (
                    <option key={key} value={key}>{label}</option>
                  ))}
                </select>
              </div>

              {/* Actions */}
              <div className="flex items-end space-x-2">
                <Button onClick={handleSearch} className="flex-1">
                  Apply
                </Button>
                <Button variant="outline" onClick={clearFilters}>
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fields Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {fields.data.map((field) => (
            <Card key={field.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{field.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {cropTypes[field.crop_type]} • {field.area_hectares} ha
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(field.verification_status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Status Badge */}
                <div className="flex justify-between items-center">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(field.verification_status)}`}>
                    {verificationStatuses[field.verification_status]}
                  </span>
                  {field.latest_satellite_data?.ndvi_value && (
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-muted-foreground">NDVI:</span>
                      <div className="flex items-center space-x-1">
                        <div className={`w-3 h-3 rounded-full ${getNDVIColor(field.latest_satellite_data.ndvi_value)}`}></div>
                        <span className="text-xs font-medium">
                          {field.latest_satellite_data.ndvi_value.toFixed(3)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Field Info */}
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Owner: {field.user?.name}</div>
                  {field.organization && (
                    <div>Organization: {field.organization.name}</div>
                  )}
                  <div>Created: {new Date(field.created_at).toLocaleDateString()}</div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Link href={`/fields/${field.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </Link>
                  {(auth.user?.id === field.user_id || auth.user?.role === 'government') && (
                    <Link href={`/fields/${field.id}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {fields.data.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <MapIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No fields found</h3>
              <p className="text-gray-500 mb-4">
                {Object.keys(filters).length > 0 
                  ? "No fields match your current filters."
                  : "Get started by adding your first field."
                }
              </p>
              {Object.keys(filters).length > 0 ? (
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              ) : (
                <Link href="/fields/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Field
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {fields.meta.last_page > 1 && (
          <div className="flex justify-center">
            <div className="flex space-x-2">
              {fields.links.prev && (
                <Link href={fields.links.prev}>
                  <Button variant="outline">Previous</Button>
                </Link>
              )}
              <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                Page {fields.meta.current_page} of {fields.meta.last_page}
              </span>
              {fields.links.next && (
                <Link href={fields.links.next}>
                  <Button variant="outline">Next</Button>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
