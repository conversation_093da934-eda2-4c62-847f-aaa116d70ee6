@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142 76% 36%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground;
}

/* Leaflet map styles */
.leaflet-container {
  @apply rounded-lg;
}

.leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-lg;
}

.leaflet-popup-content {
  @apply text-sm;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

/* Custom animations */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* NDVI color scale */
.ndvi-critical {
  @apply bg-red-500;
}

.ndvi-stressed {
  @apply bg-orange-500;
}

.ndvi-moderate {
  @apply bg-yellow-500;
}

.ndvi-healthy {
  @apply bg-green-500;
}

/* Alert severity colors */
.alert-low {
  @apply border-l-4 border-green-500 bg-green-50 text-green-800;
}

.alert-medium {
  @apply border-l-4 border-yellow-500 bg-yellow-50 text-yellow-800;
}

.alert-high {
  @apply border-l-4 border-orange-500 bg-orange-50 text-orange-800;
}

.alert-critical {
  @apply border-l-4 border-red-500 bg-red-50 text-red-800;
}

/* Dark mode alert colors */
.dark .alert-low {
  @apply bg-green-900/20 text-green-300;
}

.dark .alert-medium {
  @apply bg-yellow-900/20 text-yellow-300;
}

.dark .alert-high {
  @apply bg-orange-900/20 text-orange-300;
}

.dark .alert-critical {
  @apply bg-red-900/20 text-red-300;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
