import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import {
    MapPin,
    AlertTriangle,
    TrendingUp,
    FileText,
    Satellite
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface DashboardStats {
    total_fields: number;
    active_fields: number;
    total_alerts: number;
    critical_alerts: number;
    pending_claims: number;
    total_area: number;
}

interface Alert {
    id: number;
    type: string;
    severity: string;
    title: string;
    message: string;
    created_at: string;
    field?: {
        name: string;
    };
}

interface Field {
    id: number;
    name: string;
    area_hectares: number;
    crop_type: string;
    status: string;
    owner: {
        name: string;
    };
}

interface SatelliteData {
    id: number;
    source: string;
    acquisition_date: string;
    ndvi_average: number;
    field: {
        name: string;
    };
}

interface DashboardProps {
    stats?: DashboardStats;
    recentAlerts?: Alert[];
    recentFields?: Field[];
    recentSatelliteData?: SatelliteData[];
    roleSpecificData?: any;
    user?: {
        name: string;
        role: string;
        organization?: {
            name: string;
            type: string;
        };
    };
}

export default function Dashboard({
    stats,
    recentAlerts,
    recentFields,
    recentSatelliteData,
    roleSpecificData,
    user
}: DashboardProps) {
    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'critical': return 'bg-red-500 text-white';
            case 'high': return 'bg-orange-500 text-white';
            case 'medium': return 'bg-yellow-500 text-white';
            case 'low': return 'bg-blue-500 text-white';
            default: return 'bg-gray-500 text-white';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'bg-green-500 text-white';
            case 'inactive': return 'bg-gray-500 text-white';
            case 'harvested': return 'bg-blue-500 text-white';
            case 'fallow': return 'bg-yellow-500 text-white';
            default: return 'bg-gray-500 text-white';
        }
    };

    const formatRole = (role: string) => {
        return role.charAt(0).toUpperCase() + role.slice(1).replace('_', ' ');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="AgroVue Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            AgroVue Dashboard
                        </h1>
                        <p className="text-sm text-gray-600 mt-1">
                            Welcome back, {user?.name || 'User'} ({formatRole(user?.role || 'user')})
                            {user?.organization && ` - ${user.organization.name}`}
                        </p>
                    </div>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Fields</p>
                                <p className="text-2xl font-bold text-gray-900">{stats?.total_fields || 0}</p>
                                <p className="text-xs text-gray-500 mt-1">
                                    {stats?.active_fields || 0} active
                                </p>
                            </div>
                            <MapPin className="h-8 w-8 text-blue-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                                <p className="text-2xl font-bold text-gray-900">{stats?.total_alerts || 0}</p>
                                <p className="text-xs text-gray-500 mt-1">
                                    {stats?.critical_alerts || 0} critical
                                </p>
                            </div>
                            <AlertTriangle className="h-8 w-8 text-red-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Area</p>
                                <p className="text-2xl font-bold text-gray-900">{stats?.total_area?.toFixed(1) || '0.0'}</p>
                                <p className="text-xs text-gray-500 mt-1">hectares</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-green-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Pending Claims</p>
                                <p className="text-2xl font-bold text-gray-900">{stats?.pending_claims || 0}</p>
                                <p className="text-xs text-gray-500 mt-1">awaiting review</p>
                            </div>
                            <FileText className="h-8 w-8 text-purple-500" />
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Alerts */}
                    <div className="bg-white rounded-lg border p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Alerts</h3>
                        <div className="space-y-4">
                            {recentAlerts?.length > 0 ? (
                                recentAlerts.map((alert) => (
                                    <div key={alert.id} className="flex items-center space-x-4">
                                        <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                                            {alert.severity}
                                        </span>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {alert.title}
                                            </p>
                                            <p className="text-sm text-gray-500 truncate">
                                                {alert.field?.name || 'System Alert'}
                                            </p>
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {new Date(alert.created_at).toLocaleDateString()}
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-gray-500">No recent alerts</p>
                            )}
                        </div>
                    </div>

                    {/* Recent Fields */}
                    <div className="bg-white rounded-lg border p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Fields</h3>
                        <div className="space-y-4">
                            {recentFields?.length > 0 ? (
                                recentFields.map((field) => (
                                    <div key={field.id} className="flex items-center space-x-4">
                                        <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(field.status)}`}>
                                            {field.status}
                                        </span>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {field.name}
                                            </p>
                                            <p className="text-sm text-gray-500 truncate">
                                                {field.area_hectares} ha • {field.crop_type || 'No crop'}
                                            </p>
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {field.owner.name}
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-gray-500">No recent fields</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Recent Satellite Data */}
                <div className="bg-white rounded-lg border p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Satellite Data</h3>
                    <div className="space-y-4">
                        {recentSatelliteData?.length > 0 ? (
                            recentSatelliteData.map((data) => (
                                <div key={data.id} className="flex items-center space-x-4">
                                    <Satellite className="h-5 w-5 text-blue-500" />
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {data.field.name}
                                        </p>
                                        <p className="text-sm text-gray-500 truncate">
                                            {data.source} • NDVI: {data.ndvi_average?.toFixed(3) || 'N/A'}
                                        </p>
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {new Date(data.acquisition_date).toLocaleDateString()}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <p className="text-sm text-gray-500">No recent satellite data</p>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
