[2025-06-30 11:51:07] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  role "agrovue" does not exist (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f4743aa-0d76-46d5-9b53-3a39ba03185c, {"command":"vendor:publish","exit_code":0,"arguments":{"command":"vendor:publish"},"options":{"existing":false,"force":true,"all":false,"provider":null,"tag":["laravel-assets"],"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":true,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:51:07, ?, command, 9f4743aa-0bb7-4011-8962-e6f027176a70)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f4743aa-0d76-46d5-9b53-3a39ba03185c, {\"command\":\"vendor:publish\",\"exit_code\":0,\"arguments\":{\"command\":\"vendor:publish\"},\"options\":{\"existing\":false,\"force\":true,\"all\":false,\"provider\":null,\"tag\":[\"laravel-assets\"],\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":true,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:51:07, ?, command, 9f4743aa-0bb7-4011-8962-e6f027176a70)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(562): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#16 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}
"} 
[2025-06-30 11:52:58] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  role "agrovue" does not exist (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f474453-66c5-4fee-9b3a-80066b4808d4, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_organizations_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:52:58, ?, command, 9f474453-649e-49ef-8200-10cc10905301)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f474453-66c5-4fee-9b3a-80066b4808d4, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_organizations_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:52:58, ?, command, 9f474453-649e-49ef-8200-10cc10905301)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(562): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#16 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}
"} 
[2025-06-30 11:53:56] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  role "agrovue" does not exist (Connection: pgsql, SQL: select exists (select 1 from pg_class c, pg_namespace n where n.nspname = current_schema() and c.relname = 'migrations' and c.relkind in ('r', 'p') and n.oid = c.relnamespace)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist (Connection: pgsql, SQL: select exists (select 1 from pg_class c, pg_namespace n where n.nspname = current_schema() and c.relname = 'migrations' and c.relkind in ('r', 'p') and n.oid = c.relnamespace)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#39 {main}
"} 
[2025-06-30 11:53:56] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  role "agrovue" does not exist (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 7a22af0ced930e3a26f6ace85911aed0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 7a22af0ced930e3a26f6ace85911aed0) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 1)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 1)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#41 {main}
"} 
[2025-06-30 11:54:29] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  role "agrovue" does not exist (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f4744de-e180-4fc7-8d5d-a11effdc2fef, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_fields_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:54:29, ?, command, 9f4744de-df1b-4fcf-af27-8c21935aa5ac)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f4744de-e180-4fc7-8d5d-a11effdc2fef, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_fields_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:54:29, ?, command, 9f4744de-df1b-4fcf-af27-8c21935aa5ac)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  role \"agrovue\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'agrovue', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(562): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#16 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}
"} 
[2025-06-30 11:56:31] local.ERROR: SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' (Connection: mysql, SQL: alter table `users` add constraint `users_organization_id_foreign` foreign key (`organization_id`) references `organizations` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' (Connection: mysql, SQL: alter table `users` add constraint `users_organization_id_foreign` foreign key (`organization_id`) references `organizations` (`id`) on delete set null) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `us...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `us...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `us...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `us...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `us...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `us...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `us...')
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 11:56:31] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 7a22af0ced930e3a26f6ace85911aed0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 7a22af0ced930e3a26f6ace85911aed0) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 9)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(*)...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 9)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-06-30 11:56:59] local.ERROR: SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' (Connection: mysql, SQL: alter table `users` add constraint `users_organization_id_foreign` foreign key (`organization_id`) references `organizations` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' (Connection: mysql, SQL: alter table `users` add constraint `users_organization_id_foreign` foreign key (`organization_id`) references `organizations` (`id`) on delete set null) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `us...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `us...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `us...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `us...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `us...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `us...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `us...')
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 11:56:59] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 7a22af0ced930e3a26f6ace85911aed0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 7a22af0ced930e3a26f6ace85911aed0) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 9)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(*)...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 9)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-06-30 11:57:13] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4745d8-5397-4aa6-8104-865d5b0f36ef, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_satellite_data_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:57:12, ?, command, 9f4745d8-51c2-4047-ab57-c58b662092a3)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4745d8-5397-4aa6-8104-865d5b0f36ef, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_satellite_data_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:57:12, ?, command, 9f4745d8-51c2-4047-ab57-c58b662092a3)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 11:58:24] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474645-c4be-40b0-b0e2-0073c237e894, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_alerts_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:58:24, ?, command, 9f474645-c281-4d0e-95cb-fcfee8f655b2)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474645-c4be-40b0-b0e2-0073c237e894, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_alerts_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:58:24, ?, command, 9f474645-c281-4d0e-95cb-fcfee8f655b2)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 11:58:53] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474671-add9-4f2b-bfdf-b9f6fcc19d2a, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_subsidy_claims_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:58:53, ?, command, 9f474671-abc7-460c-b047-36f2e8a79165)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474671-add9-4f2b-bfdf-b9f6fcc19d2a, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_subsidy_claims_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:58:53, ?, command, 9f474671-abc7-460c-b047-36f2e8a79165)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 11:59:43] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4746bd-d923-480d-9fbb-ef4eba48026a, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_reports_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:59:43, ?, command, 9f4746bd-d735-4183-bd99-59e40810d0b2)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4746bd-d923-480d-9fbb-ef4eba48026a, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_reports_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:59:43, ?, command, 9f4746bd-d735-4183-bd99-59e40810d0b2)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:00:19] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4746f4-97c6-4dad-ba69-5b928bfade83, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"create_activity_logs_table"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:00:19, ?, command, 9f4746f4-9595-4f55-a69e-57626d9545d5)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4746f4-97c6-4dad-ba69-5b928bfade83, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"create_activity_logs_table\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:00:19, ?, command, 9f4746f4-9595-4f55-a69e-57626d9545d5)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:00:54] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474729-d03d-41a0-9258-162093f7f732, {"command":"make:model","exit_code":0,"arguments":{"command":"make:model","name":"Organization"},"options":{"all":false,"controller":false,"factory":false,"force":false,"migration":false,"morph-pivot":false,"policy":false,"seed":false,"pivot":false,"resource":false,"api":false,"requests":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:00:54, ?, command, 9f474729-ce0b-4fbd-a7cf-717849e45f79)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474729-d03d-41a0-9258-162093f7f732, {\"command\":\"make:model\",\"exit_code\":0,\"arguments\":{\"command\":\"make:model\",\"name\":\"Organization\"},\"options\":{\"all\":false,\"controller\":false,\"factory\":false,\"force\":false,\"migration\":false,\"morph-pivot\":false,\"policy\":false,\"seed\":false,\"pivot\":false,\"resource\":false,\"api\":false,\"requests\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:00:54, ?, command, 9f474729-ce0b-4fbd-a7cf-717849e45f79)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:03:06] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4747f4-3fd4-43d7-a982-ca2d56762b5b, {"command":"make:model","exit_code":0,"arguments":{"command":"make:model","name":"Field"},"options":{"all":false,"controller":false,"factory":false,"force":false,"migration":false,"morph-pivot":false,"policy":false,"seed":false,"pivot":false,"resource":false,"api":false,"requests":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:03:06, ?, command, 9f4747f4-3daf-44e4-a690-e387e7eefcfc)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4747f4-3fd4-43d7-a982-ca2d56762b5b, {\"command\":\"make:model\",\"exit_code\":0,\"arguments\":{\"command\":\"make:model\",\"name\":\"Field\"},\"options\":{\"all\":false,\"controller\":false,\"factory\":false,\"force\":false,\"migration\":false,\"morph-pivot\":false,\"policy\":false,\"seed\":false,\"pivot\":false,\"resource\":false,\"api\":false,\"requests\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:03:06, ?, command, 9f4747f4-3daf-44e4-a690-e387e7eefcfc)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:03:36] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474821-d671-439e-87b9-bd2a1d83e207, {"command":"make:model","exit_code":0,"arguments":{"command":"make:model","name":"SatelliteData"},"options":{"all":false,"controller":false,"factory":false,"force":false,"migration":false,"morph-pivot":false,"policy":false,"seed":false,"pivot":false,"resource":false,"api":false,"requests":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:03:36, ?, command, 9f474821-d3fe-44fe-a0c1-f3ba954cb8fe)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474821-d671-439e-87b9-bd2a1d83e207, {\"command\":\"make:model\",\"exit_code\":0,\"arguments\":{\"command\":\"make:model\",\"name\":\"SatelliteData\"},\"options\":{\"all\":false,\"controller\":false,\"factory\":false,\"force\":false,\"migration\":false,\"morph-pivot\":false,\"policy\":false,\"seed\":false,\"pivot\":false,\"resource\":false,\"api\":false,\"requests\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:03:36, ?, command, 9f474821-d3fe-44fe-a0c1-f3ba954cb8fe)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:19:59] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474dfd-0874-43e8-b359-0e8eb1c7529e, {"command":"make:model","exit_code":0,"arguments":{"command":"make:model","name":"Alert"},"options":{"all":false,"controller":false,"factory":false,"force":false,"migration":false,"morph-pivot":false,"policy":false,"seed":false,"pivot":false,"resource":false,"api":false,"requests":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:19:59, ?, command, 9f474dfd-05c4-4b74-af94-83adcd50e130)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474dfd-0874-43e8-b359-0e8eb1c7529e, {\"command\":\"make:model\",\"exit_code\":0,\"arguments\":{\"command\":\"make:model\",\"name\":\"Alert\"},\"options\":{\"all\":false,\"controller\":false,\"factory\":false,\"force\":false,\"migration\":false,\"morph-pivot\":false,\"policy\":false,\"seed\":false,\"pivot\":false,\"resource\":false,\"api\":false,\"requests\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:19:59, ?, command, 9f474dfd-05c4-4b74-af94-83adcd50e130)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:20:34] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474e32-9186-4cb3-ac4a-5f106302589c, {"command":"make:model","exit_code":0,"arguments":{"command":"make:model","name":"SubsidyClaim"},"options":{"all":false,"controller":false,"factory":false,"force":false,"migration":false,"morph-pivot":false,"policy":false,"seed":false,"pivot":false,"resource":false,"api":false,"requests":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:20:34, ?, command, 9f474e32-8f6a-4533-ba37-9f0ff2edccc9)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474e32-9186-4cb3-ac4a-5f106302589c, {\"command\":\"make:model\",\"exit_code\":0,\"arguments\":{\"command\":\"make:model\",\"name\":\"SubsidyClaim\"},\"options\":{\"all\":false,\"controller\":false,\"factory\":false,\"force\":false,\"migration\":false,\"morph-pivot\":false,\"policy\":false,\"seed\":false,\"pivot\":false,\"resource\":false,\"api\":false,\"requests\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:20:34, ?, command, 9f474e32-8f6a-4533-ba37-9f0ff2edccc9)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:21:12] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474e6d-20dc-497e-8790-e71b401b187d, {"command":"make:model","exit_code":0,"arguments":{"command":"make:model","name":"Report"},"options":{"all":false,"controller":false,"factory":false,"force":false,"migration":false,"morph-pivot":false,"policy":false,"seed":false,"pivot":false,"resource":false,"api":false,"requests":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:21:12, ?, command, 9f474e6d-1ee2-4711-9a1e-31775c15dd71)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474e6d-20dc-497e-8790-e71b401b187d, {\"command\":\"make:model\",\"exit_code\":0,\"arguments\":{\"command\":\"make:model\",\"name\":\"Report\"},\"options\":{\"all\":false,\"controller\":false,\"factory\":false,\"force\":false,\"migration\":false,\"morph-pivot\":false,\"policy\":false,\"seed\":false,\"pivot\":false,\"resource\":false,\"api\":false,\"requests\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:21:12, ?, command, 9f474e6d-1ee2-4711-9a1e-31775c15dd71)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:22:01] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474eb8-395c-4ee3-a494-27b8f10df1b2, {"command":"vendor:publish","exit_code":0,"arguments":{"command":"vendor:publish"},"options":{"existing":false,"force":false,"all":false,"provider":"Laravel\\Sanctum\\SanctumServiceProvider","tag":[],"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:22:01, ?, command, 9f474eb8-3743-4e0a-b097-5b3620356700)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474eb8-395c-4ee3-a494-27b8f10df1b2, {\"command\":\"vendor:publish\",\"exit_code\":0,\"arguments\":{\"command\":\"vendor:publish\"},\"options\":{\"existing\":false,\"force\":false,\"all\":false,\"provider\":\"Laravel\\\\Sanctum\\\\SanctumServiceProvider\",\"tag\":[],\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:22:01, ?, command, 9f474eb8-3743-4e0a-b097-5b3620356700)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:22:42] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474ef6-3c28-44ea-9fb7-5cf7c9701ce2, {"command":"make:controller","exit_code":0,"arguments":{"command":"make:controller","name":"Auth\/AuthController"},"options":{"api":false,"type":null,"force":false,"invokable":false,"model":null,"parent":null,"resource":false,"requests":false,"singleton":false,"creatable":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:22:42, ?, command, 9f474ef6-39f5-4a3c-a2d9-909dffa05942)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474ef6-3c28-44ea-9fb7-5cf7c9701ce2, {\"command\":\"make:controller\",\"exit_code\":0,\"arguments\":{\"command\":\"make:controller\",\"name\":\"Auth\\/AuthController\"},\"options\":{\"api\":false,\"type\":null,\"force\":false,\"invokable\":false,\"model\":null,\"parent\":null,\"resource\":false,\"requests\":false,\"singleton\":false,\"creatable\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:22:42, ?, command, 9f474ef6-39f5-4a3c-a2d9-909dffa05942)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:23:28] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474f3b-95e7-489b-8a3e-be0c202c64a6, {"command":"make:controller","exit_code":0,"arguments":{"command":"make:controller","name":"Api\/FieldController"},"options":{"api":true,"type":null,"force":false,"invokable":false,"model":null,"parent":null,"resource":false,"requests":false,"singleton":false,"creatable":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:23:27, ?, command, 9f474f3b-938a-415f-8f32-f2f93ae315b6)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474f3b-95e7-489b-8a3e-be0c202c64a6, {\"command\":\"make:controller\",\"exit_code\":0,\"arguments\":{\"command\":\"make:controller\",\"name\":\"Api\\/FieldController\"},\"options\":{\"api\":true,\"type\":null,\"force\":false,\"invokable\":false,\"model\":null,\"parent\":null,\"resource\":false,\"requests\":false,\"singleton\":false,\"creatable\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:23:27, ?, command, 9f474f3b-938a-415f-8f32-f2f93ae315b6)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:24:10] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474f7b-f640-425c-9688-ff354a415fcb, {"command":"make:controller","exit_code":0,"arguments":{"command":"make:controller","name":"DashboardController"},"options":{"api":false,"type":null,"force":false,"invokable":false,"model":null,"parent":null,"resource":false,"requests":false,"singleton":false,"creatable":false,"test":false,"pest":false,"phpunit":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:24:10, ?, command, 9f474f7b-f428-4ae5-8836-588472d50c73)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f474f7b-f640-425c-9688-ff354a415fcb, {\"command\":\"make:controller\",\"exit_code\":0,\"arguments\":{\"command\":\"make:controller\",\"name\":\"DashboardController\"},\"options\":{\"api\":false,\"type\":null,\"force\":false,\"invokable\":false,\"model\":null,\"parent\":null,\"resource\":false,\"requests\":false,\"singleton\":false,\"creatable\":false,\"test\":false,\"pest\":false,\"phpunit\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:24:10, ?, command, 9f474f7b-f428-4ae5-8836-588472d50c73)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:30:16] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4751aa-4592-4277-90a1-f67de4b95c2a, {"command":"make:seeder","exit_code":0,"arguments":{"command":"make:seeder","name":"OrganizationSeeder"},"options":{"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:30:16, ?, command, 9f4751aa-4341-4b5b-9947-0e612543b324)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f4751aa-4592-4277-90a1-f67de4b95c2a, {\"command\":\"make:seeder\",\"exit_code\":0,\"arguments\":{\"command\":\"make:seeder\",\"name\":\"OrganizationSeeder\"},\"options\":{\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:30:16, ?, command, 9f4751aa-4341-4b5b-9947-0e612543b324)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:31:13] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f475202-23b2-414b-91ff-d0beaa14c3db, {"command":"make:seeder","exit_code":0,"arguments":{"command":"make:seeder","name":"UserSeeder"},"options":{"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:31:13, ?, command, 9f475202-21cd-4d20-8604-************)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f475202-23b2-414b-91ff-d0beaa14c3db, {\"command\":\"make:seeder\",\"exit_code\":0,\"arguments\":{\"command\":\"make:seeder\",\"name\":\"UserSeeder\"},\"options\":{\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:31:13, ?, command, 9f475202-21cd-4d20-8604-************)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:32:17] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f475263-497e-4c0a-b904-89ddfd698b39, {"connection":"mysql","bindings":[],"sql":"select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`","time":"2.70","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/agrovue-app\/artisan","line":16,"hash":"472343cbf78736393ab995dc1735b75f","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:32:17, ?, query, 9f475263-44bc-4c7c-a74a-c22f9c0221c6), (9f475263-497e-4c0a-b904-89ddfd698b39, {"connection":"mysql","bindings":[],"sql":"select `migration` from `migrations` order by `batch` asc, `migration` asc","time":"0.76","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/agrovue-app\/artisan","line":16,"hash":"ed08a59c7f0b8851f0fd2291ca94d5c7","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:32:17, ?, query, 9f475263-4676-42da-b3e8-485c41185924), (9f475263-497e-4c0a-b904-89ddfd698b39, {"connection":"mysql","bindings":[],"sql":"select `batch`, `migration` from `migrations` order by `batch` asc, `migration` asc","time":"0.28","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/agrovue-app\/artisan","line":16,"hash":"ef468451cf896db9aa75036f7d9b8b06","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:32:17, ?, query, 9f475263-469f-4304-9265-bf677a1d3187), (9f475263-497e-4c0a-b904-89ddfd698b39, {"command":"migrate:status","exit_code":0,"arguments":{"command":"migrate:status"},"options":{"database":null,"pending":false,"path":[],"realpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:32:17, ?, command, 9f475263-4907-4968-a08d-d4fe054fdcf7)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f475263-497e-4c0a-b904-89ddfd698b39, {\"connection\":\"mysql\",\"bindings\":[],\"sql\":\"select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`\",\"time\":\"2.70\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/agrovue-app\\/artisan\",\"line\":16,\"hash\":\"472343cbf78736393ab995dc1735b75f\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:32:17, ?, query, 9f475263-44bc-4c7c-a74a-c22f9c0221c6), (9f475263-497e-4c0a-b904-89ddfd698b39, {\"connection\":\"mysql\",\"bindings\":[],\"sql\":\"select `migration` from `migrations` order by `batch` asc, `migration` asc\",\"time\":\"0.76\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/agrovue-app\\/artisan\",\"line\":16,\"hash\":\"ed08a59c7f0b8851f0fd2291ca94d5c7\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:32:17, ?, query, 9f475263-4676-42da-b3e8-485c41185924), (9f475263-497e-4c0a-b904-89ddfd698b39, {\"connection\":\"mysql\",\"bindings\":[],\"sql\":\"select `batch`, `migration` from `migrations` order by `batch` asc, `migration` asc\",\"time\":\"0.28\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/agrovue-app\\/artisan\",\"line\":16,\"hash\":\"ef468451cf896db9aa75036f7d9b8b06\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:32:17, ?, query, 9f475263-469f-4304-9265-bf677a1d3187), (9f475263-497e-4c0a-b904-89ddfd698b39, {\"command\":\"migrate:status\",\"exit_code\":0,\"arguments\":{\"command\":\"migrate:status\"},\"options\":{\"database\":null,\"pending\":false,\"path\":[],\"realpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:32:17, ?, command, 9f475263-4907-4968-a08d-d4fe054fdcf7)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:47)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(47): PDO->prepare('insert into `te...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3747): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 12:32:27] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: mysql, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `role` enum('admin', 'government', 'ngo', 'corporate', 'cooperative', 'bank') not null, `organization_id` bigint unsigned null, `phone` varchar(255) null, `position` varchar(255) null, `permissions` json null, `is_active` tinyint(1) not null default '1', `last_login_at` timestamp null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: mysql, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `role` enum('admin', 'government', 'ngo', 'corporate', 'cooperative', 'bank') not null, `organization_id` bigint unsigned null, `phone` varchar(255) null, `position` varchar(255) null, `permissions` json null, `is_active` tinyint(1) not null default '1', `last_login_at` timestamp null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `u...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `u...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `u...')
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 12:32:27] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 7a22af0ced930e3a26f6ace85911aed0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 7a22af0ced930e3a26f6ace85911aed0) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 6)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'agrovue.telescope_entries' doesn't exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(*)...')
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 6)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(749): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(809): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-06-30 12:32:46] local.ERROR: SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' (Connection: mysql, SQL: alter table `users` add constraint `users_organization_id_foreign` foreign key (`organization_id`) references `organizations` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' (Connection: mysql, SQL: alter table `users` add constraint `users_organization_id_foreign` foreign key (`organization_id`) references `organizations` (`id`) on delete set null) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `us...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `us...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `us...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'organizations' at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `us...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `us...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `us...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `us...')
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-06-30 12:34:26] local.ERROR: Method Illuminate\Database\Schema\Blueprint::point does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Database\\Schema\\Blueprint::point does not exist. at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Macroable/Traits/Macroable.php:115)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/2025_06_30_115429_create_fields_table.php(24): Illuminate\\Database\\Schema\\Blueprint->__call('point', Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(475): Illuminate\\Database\\Migrations\\Migration@anonymous->{closure}(Object(Illuminate\\Database\\Schema\\Blueprint))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Database\\Schema\\Builder->Illuminate\\Database\\Schema\\{closure}(Object(Illuminate\\Database\\Schema\\Blueprint))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): tap(Object(Illuminate\\Database\\Schema\\Blueprint), Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('fields', Object(Closure))
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/database/migrations/2025_06_30_115429_create_fields_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_30_1154...', Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_30_1154...', Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/agrovue-app/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#44 {main}
"} 
