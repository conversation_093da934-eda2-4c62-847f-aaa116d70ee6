<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Organization extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'registration_number',
        'contact_email',
        'contact_phone',
        'address',
        'country',
        'state',
        'city',
        'settings',
        'is_active',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the users for the organization.
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the fields for the organization.
     */
    public function fields(): Has<PERSON>any
    {
        return $this->hasMany(Field::class);
    }

    /**
     * Get the subsidy claims for the organization.
     */
    public function subsidyClaims(): Has<PERSON><PERSON>
    {
        return $this->hasMany(SubsidyClaim::class);
    }

    /**
     * Get the alerts for the organization.
     */
    public function alerts(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Alert::class);
    }

    /**
     * Get the reports for the organization.
     */
    public function reports(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Report::class);
    }

    /**
     * Scope a query to only include active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by organization type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}
