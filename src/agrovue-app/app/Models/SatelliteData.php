<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SatelliteData extends Model
{
    use HasFactory;

    protected $fillable = [
        'field_id',
        'source',
        'scene_id',
        'acquisition_date',
        'cloud_coverage',
        'ndvi_average',
        'ndvi_min',
        'ndvi_max',
        'evi_average',
        'moisture_index',
        'image_url',
        'thumbnail_url',
        'band_data',
        'analysis_results',
        'processing_status',
        'processing_notes',
    ];

    protected $casts = [
        'acquisition_date' => 'date',
        'cloud_coverage' => 'decimal:2',
        'ndvi_average' => 'decimal:4',
        'ndvi_min' => 'decimal:4',
        'ndvi_max' => 'decimal:4',
        'evi_average' => 'decimal:4',
        'moisture_index' => 'decimal:4',
        'band_data' => 'array',
        'analysis_results' => 'array',
    ];

    /**
     * Get the field that owns the satellite data.
     */
    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Scope a query to only include completed processing.
     */
    public function scopeCompleted($query)
    {
        return $query->where('processing_status', 'completed');
    }

    /**
     * Scope a query to filter by source.
     */
    public function scopeFromSource($query, $source)
    {
        return $query->where('source', $source);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('acquisition_date', [$startDate, $endDate]);
    }

    /**
     * Get NDVI health status based on average value.
     */
    public function getNdviHealthStatusAttribute()
    {
        if (!$this->ndvi_average) {
            return 'unknown';
        }

        if ($this->ndvi_average >= 0.7) {
            return 'excellent';
        } elseif ($this->ndvi_average >= 0.5) {
            return 'good';
        } elseif ($this->ndvi_average >= 0.3) {
            return 'moderate';
        } else {
            return 'poor';
        }
    }

    /**
     * Check if the data indicates potential drought stress.
     */
    public function getIsDroughtStressAttribute()
    {
        return $this->ndvi_average && $this->ndvi_average < 0.3;
    }
}
