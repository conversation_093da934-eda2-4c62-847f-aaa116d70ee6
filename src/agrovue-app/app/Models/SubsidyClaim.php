<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class SubsidyClaim extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'claim_number',
        'claimant_id',
        'field_id',
        'organization_id',
        'subsidy_type',
        'claimed_amount',
        'approved_amount',
        'claimed_area_hectares',
        'verified_area_hectares',
        'crop_type',
        'planting_date',
        'harvest_date',
        'status',
        'description',
        'supporting_documents',
        'verification_data',
        'fraud_risk_score',
        'fraud_indicators',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
        'approved_at',
        'paid_at',
    ];

    protected $casts = [
        'claimed_amount' => 'decimal:2',
        'approved_amount' => 'decimal:2',
        'claimed_area_hectares' => 'decimal:4',
        'verified_area_hectares' => 'decimal:4',
        'fraud_risk_score' => 'decimal:4',
        'planting_date' => 'date',
        'harvest_date' => 'date',
        'supporting_documents' => 'array',
        'verification_data' => 'array',
        'reviewed_at' => 'datetime',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the claimant (user) who made the claim.
     */
    public function claimant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'claimant_id');
    }

    /**
     * Get the field for the claim.
     */
    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the organization that owns the claim.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the user who reviewed the claim.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeOfStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include pending claims.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include high fraud risk claims.
     */
    public function scopeHighFraudRisk($query)
    {
        return $query->where('fraud_risk_score', '>', 0.7);
    }

    /**
     * Approve the claim.
     */
    public function approve(User $reviewer, float $approvedAmount = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_amount' => $approvedAmount ?? $this->claimed_amount,
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'approved_at' => now(),
        ]);
    }

    /**
     * Reject the claim.
     */
    public function reject(User $reviewer, string $notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Mark claim as paid.
     */
    public function markAsPaid()
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }

    /**
     * Get fraud risk level.
     */
    public function getFraudRiskLevelAttribute()
    {
        if (!$this->fraud_risk_score) {
            return 'unknown';
        }

        if ($this->fraud_risk_score >= 0.8) {
            return 'very_high';
        } elseif ($this->fraud_risk_score >= 0.6) {
            return 'high';
        } elseif ($this->fraud_risk_score >= 0.4) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'claimed_amount', 'approved_amount', 'fraud_risk_score'])
            ->logOnlyDirty();
    }
}
