<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Field extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'name',
        'description',
        'owner_id',
        'organization_id',
        'area_hectares',
        'crop_type',
        'status',
        'boundary_coordinates',
        'center_point',
        'location_address',
        'state',
        'country',
        'planting_date',
        'expected_harvest_date',
        'metadata',
    ];

    protected $casts = [
        'boundary_coordinates' => 'array',
        'planting_date' => 'date',
        'expected_harvest_date' => 'date',
        'metadata' => 'array',
        'area_hectares' => 'decimal:4',
    ];

    /**
     * Get the owner of the field.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the organization that owns the field.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the satellite data for the field.
     */
    public function satelliteData(): HasMany
    {
        return $this->hasMany(SatelliteData::class);
    }

    /**
     * Get the subsidy claims for the field.
     */
    public function subsidyClaims(): HasMany
    {
        return $this->hasMany(SubsidyClaim::class);
    }

    /**
     * Get the alerts for the field.
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class);
    }

    /**
     * Scope a query to only include active fields.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by crop type.
     */
    public function scopeOfCropType($query, $cropType)
    {
        return $query->where('crop_type', $cropType);
    }

    /**
     * Get the latest NDVI value for the field.
     */
    public function getLatestNdviAttribute()
    {
        return $this->satelliteData()
            ->whereNotNull('ndvi_average')
            ->latest('acquisition_date')
            ->value('ndvi_average');
    }

    /**
     * Activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'area_hectares', 'crop_type', 'status', 'owner_id'])
            ->logOnlyDirty();
    }
}
