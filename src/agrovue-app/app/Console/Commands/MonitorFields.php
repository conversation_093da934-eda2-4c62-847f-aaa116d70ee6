<?php

namespace App\Console\Commands;

use App\Services\AlertService;
use Illuminate\Console\Command;

class MonitorFields extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agrovue:monitor-fields {--type=all : Type of monitoring (drought|fraud|weather|all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor fields for various conditions and generate alerts';

    protected $alertService;

    public function __construct(AlertService $alertService)
    {
        parent::__construct();
        $this->alertService = $alertService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        $this->info("Starting field monitoring ({$type})...");

        $results = [];

        switch ($type) {
            case 'drought':
                $results['drought'] = $this->alertService->monitorDroughtConditions();
                break;

            case 'fraud':
                $results['fraud'] = $this->alertService->monitorFraudIndicators();
                break;

            case 'weather':
                $results['weather'] = $this->alertService->monitorWeatherAlerts();
                break;

            case 'all':
            default:
                $results = $this->alertService->runAutomatedMonitoring();
                break;
        }

        // Display results
        foreach ($results as $monitorType => $alerts) {
            $count = count($alerts);
            if ($count > 0) {
                $this->info("Created {$count} {$monitorType} alerts");

                foreach ($alerts as $alert) {
                    $this->line("  - {$alert->title} (Severity: {$alert->severity})");
                }
            } else {
                $this->line("No {$monitorType} alerts created");
            }
        }

        $totalAlerts = array_sum(array_map('count', $results));
        $this->info("Monitoring completed. Total alerts created: {$totalAlerts}");

        return Command::SUCCESS;
    }
}
