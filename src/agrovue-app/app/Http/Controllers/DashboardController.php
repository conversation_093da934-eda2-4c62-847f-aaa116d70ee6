<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Alert;
use App\Models\SubsidyClaim;
use App\Models\SatelliteData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        $organizationId = $user->organization_id;

        // Base queries based on user role
        $fieldsQuery = Field::query();
        $alertsQuery = Alert::query();
        $claimsQuery = SubsidyClaim::query();

        // Apply role-based filtering
        if ($user->role !== 'admin') {
            switch ($user->role) {
                case 'government':
                case 'ngo':
                case 'corporate':
                case 'bank':
                    // Organization-level access
                    $fieldsQuery->where('organization_id', $organizationId);
                    $alertsQuery->where('organization_id', $organizationId);
                    $claimsQuery->where('organization_id', $organizationId);
                    break;

                case 'cooperative':
                    // Cooperative members can see their own and organization fields
                    $fieldsQuery->where(function($q) use ($user, $organizationId) {
                        $q->where('owner_id', $user->id)
                          ->orWhere('organization_id', $organizationId);
                    });
                    $alertsQuery->where(function($q) use ($organizationId) {
                        $q->where('organization_id', $organizationId)
                          ->orWhereHas('field', function($fq) use ($user) {
                              $fq->where('owner_id', $user->id);
                          });
                    });
                    $claimsQuery->where(function($q) use ($user, $organizationId) {
                        $q->where('claimant_id', $user->id)
                          ->orWhere('organization_id', $organizationId);
                    });
                    break;
            }
        }

        // Dashboard statistics
        $stats = [
            'total_fields' => $fieldsQuery->count(),
            'active_fields' => $fieldsQuery->where('status', 'active')->count(),
            'total_alerts' => $alertsQuery->active()->count(),
            'critical_alerts' => $alertsQuery->active()->where('severity', 'critical')->count(),
            'pending_claims' => $claimsQuery->where('status', 'pending')->count(),
            'total_area' => $fieldsQuery->sum('area_hectares'),
        ];

        // Recent alerts
        $recentAlerts = $alertsQuery->active()
            ->with(['field', 'organization'])
            ->latest()
            ->limit(5)
            ->get();

        // Recent fields
        $recentFields = $fieldsQuery->with(['owner', 'organization'])
            ->latest()
            ->limit(5)
            ->get();

        // Recent satellite data
        $recentSatelliteData = SatelliteData::whereHas('field', function($q) use ($fieldsQuery) {
            $q->whereIn('id', $fieldsQuery->pluck('id'));
        })
        ->with('field')
        ->completed()
        ->latest('acquisition_date')
        ->limit(5)
        ->get();

        // Role-specific data
        $roleSpecificData = $this->getRoleSpecificData($user, $fieldsQuery, $alertsQuery, $claimsQuery);

        return Inertia::render('Dashboard', [
            'stats' => $stats,
            'recentAlerts' => $recentAlerts,
            'recentFields' => $recentFields,
            'recentSatelliteData' => $recentSatelliteData,
            'roleSpecificData' => $roleSpecificData,
            'user' => $user->load('organization'),
        ]);
    }

    /**
     * Get role-specific dashboard data.
     */
    private function getRoleSpecificData($user, $fieldsQuery, $alertsQuery, $claimsQuery)
    {
        $data = [];

        switch ($user->role) {
            case 'government':
                $data['subsidyStats'] = [
                    'total_claims' => $claimsQuery->count(),
                    'pending_review' => $claimsQuery->where('status', 'pending')->count(),
                    'approved_amount' => $claimsQuery->where('status', 'approved')->sum('approved_amount'),
                    'fraud_alerts' => $claimsQuery->where('fraud_risk_score', '>', 0.7)->count(),
                ];
                break;

            case 'ngo':
                $data['disasterStats'] = [
                    'drought_alerts' => $alertsQuery->where('type', 'drought')->active()->count(),
                    'flood_alerts' => $alertsQuery->where('type', 'flood')->active()->count(),
                    'affected_fields' => $fieldsQuery->whereHas('alerts', function($q) {
                        $q->active()->whereIn('type', ['drought', 'flood']);
                    })->count(),
                ];
                break;

            case 'corporate':
                $data['supplyChainStats'] = [
                    'contracted_fields' => $fieldsQuery->count(),
                    'harvest_ready' => $fieldsQuery->where('status', 'harvested')->count(),
                    'quality_alerts' => $alertsQuery->whereIn('type', ['pest', 'disease'])->active()->count(),
                ];
                break;

            case 'bank':
                $data['riskStats'] = [
                    'loan_applications' => $claimsQuery->count(),
                    'high_risk_fields' => $fieldsQuery->whereHas('satelliteData', function($q) {
                        $q->where('ndvi_average', '<', 0.3);
                    })->count(),
                    'default_risk' => $claimsQuery->where('fraud_risk_score', '>', 0.5)->count(),
                ];
                break;

            case 'cooperative':
                $data['memberStats'] = [
                    'member_fields' => $fieldsQuery->where('owner_id', $user->id)->count(),
                    'cooperative_fields' => $fieldsQuery->where('organization_id', $user->organization_id)->count(),
                    'yield_alerts' => $alertsQuery->where('type', 'harvest')->active()->count(),
                ];
                break;
        }

        return $data;
    }
}
