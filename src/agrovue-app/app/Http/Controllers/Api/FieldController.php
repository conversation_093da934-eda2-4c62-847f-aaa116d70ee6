<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Field;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Field::with(['owner', 'organization', 'satelliteData' => function($q) {
            $q->latest('acquisition_date')->limit(1);
        }]);

        // Filter by user's organization if not admin
        if (Auth::user()->role !== 'admin') {
            $query->where(function($q) {
                $q->where('owner_id', Auth::id())
                  ->orWhere('organization_id', Auth::user()->organization_id);
            });
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('crop_type')) {
            $query->where('crop_type', $request->crop_type);
        }

        if ($request->has('state')) {
            $query->where('state', $request->state);
        }

        $fields = $query->paginate($request->get('per_page', 15));

        return response()->json($fields);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'area_hectares' => 'required|numeric|min:0.01',
            'crop_type' => 'nullable|string|max:255',
            'boundary_coordinates' => 'required|array',
            'center_point' => 'required|array',
            'location_address' => 'nullable|string',
            'state' => 'required|string|max:255',
            'country' => 'nullable|string|size:2',
            'planting_date' => 'nullable|date',
            'expected_harvest_date' => 'nullable|date|after:planting_date',
        ]);

        $field = Field::create([
            'name' => $request->name,
            'description' => $request->description,
            'owner_id' => Auth::id(),
            'organization_id' => Auth::user()->organization_id,
            'area_hectares' => $request->area_hectares,
            'crop_type' => $request->crop_type,
            'boundary_coordinates' => $request->boundary_coordinates,
            'center_point' => $request->center_point,
            'location_address' => $request->location_address,
            'state' => $request->state,
            'country' => $request->country ?? 'NG',
            'planting_date' => $request->planting_date,
            'expected_harvest_date' => $request->expected_harvest_date,
            'status' => 'active',
        ]);

        return response()->json($field->load(['owner', 'organization']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Field $field)
    {
        // Check authorization
        if (Auth::user()->role !== 'admin' &&
            $field->owner_id !== Auth::id() &&
            $field->organization_id !== Auth::user()->organization_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($field->load([
            'owner',
            'organization',
            'satelliteData' => function($q) {
                $q->latest('acquisition_date')->limit(10);
            },
            'alerts' => function($q) {
                $q->active()->latest();
            }
        ]));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Field $field)
    {
        // Check authorization
        if (Auth::user()->role !== 'admin' && $field->owner_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'area_hectares' => 'sometimes|numeric|min:0.01',
            'crop_type' => 'nullable|string|max:255',
            'status' => 'sometimes|in:active,inactive,harvested,fallow',
            'boundary_coordinates' => 'sometimes|array',
            'center_point' => 'sometimes|array',
            'location_address' => 'nullable|string',
            'planting_date' => 'nullable|date',
            'expected_harvest_date' => 'nullable|date|after:planting_date',
        ]);

        $field->update($request->only([
            'name', 'description', 'area_hectares', 'crop_type', 'status',
            'boundary_coordinates', 'center_point', 'location_address',
            'planting_date', 'expected_harvest_date'
        ]));

        return response()->json($field->load(['owner', 'organization']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Field $field)
    {
        // Check authorization
        if (Auth::user()->role !== 'admin' && $field->owner_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $field->delete();

        return response()->json(['message' => 'Field deleted successfully']);
    }

    /**
     * Get satellite data for a field.
     */
    public function satelliteData(Field $field)
    {
        // Check authorization
        if (Auth::user()->role !== 'admin' &&
            $field->owner_id !== Auth::id() &&
            $field->organization_id !== Auth::user()->organization_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $satelliteData = $field->satelliteData()
            ->completed()
            ->latest('acquisition_date')
            ->paginate(20);

        return response()->json($satelliteData);
    }
}
