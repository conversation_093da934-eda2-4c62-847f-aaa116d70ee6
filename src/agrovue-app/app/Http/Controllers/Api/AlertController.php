<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Alert;
use App\Services\AlertService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AlertController extends Controller
{
    protected $alertService;

    public function __construct(AlertService $alertService)
    {
        $this->alertService = $alertService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $filters = $request->only(['type', 'severity', 'status']);
        $alerts = $this->alertService->getAlertsForUser(Auth::user(), $filters);

        return response()->json([
            'data' => $alerts,
            'meta' => [
                'total' => $alerts->count(),
                'active' => $alerts->where('status', 'active')->count(),
                'critical' => $alerts->where('severity', 'critical')->count(),
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:drought,flood,pest,disease,harvest,fraud,weather,general',
            'severity' => 'required|in:low,medium,high,critical',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'field_id' => 'nullable|exists:fields,id',
            'affected_regions' => 'nullable|array',
        ]);

        $alert = $this->alertService->createAlert([
            'type' => $request->type,
            'severity' => $request->severity,
            'title' => $request->title,
            'message' => $request->message,
            'field_id' => $request->field_id,
            'organization_id' => Auth::user()->organization_id,
            'affected_regions' => $request->affected_regions,
            'is_automated' => false,
            'source' => 'manual',
        ]);

        return response()->json($alert->load(['field', 'organization']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Alert $alert)
    {
        // Check authorization
        $user = Auth::user();

        if ($user->role !== 'admin') {
            $hasAccess = false;

            switch ($user->role) {
                case 'government':
                case 'ngo':
                case 'corporate':
                case 'bank':
                    $hasAccess = $alert->organization_id === $user->organization_id;
                    break;

                case 'cooperative':
                    $hasAccess = $alert->organization_id === $user->organization_id ||
                               ($alert->field && $alert->field->owner_id === $user->id);
                    break;
            }

            if (!$hasAccess) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        return response()->json($alert->load(['field', 'organization', 'acknowledgedBy', 'resolvedBy']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Alert $alert)
    {
        // Only allow updating certain fields
        $request->validate([
            'status' => 'sometimes|in:active,acknowledged,resolved,dismissed',
            'resolution_notes' => 'nullable|string',
        ]);

        // Check authorization (similar to show method)
        $user = Auth::user();

        if ($user->role !== 'admin') {
            $hasAccess = false;

            switch ($user->role) {
                case 'government':
                case 'ngo':
                case 'corporate':
                case 'bank':
                    $hasAccess = $alert->organization_id === $user->organization_id;
                    break;

                case 'cooperative':
                    $hasAccess = $alert->organization_id === $user->organization_id ||
                               ($alert->field && $alert->field->owner_id === $user->id);
                    break;
            }

            if (!$hasAccess) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        if ($request->has('status')) {
            switch ($request->status) {
                case 'acknowledged':
                    $this->alertService->acknowledgeAlert($alert, $user);
                    break;
                case 'resolved':
                    $this->alertService->resolveAlert($alert, $user, $request->resolution_notes);
                    break;
                default:
                    $alert->update(['status' => $request->status]);
            }
        }

        return response()->json($alert->fresh()->load(['field', 'organization', 'acknowledgedBy', 'resolvedBy']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Alert $alert)
    {
        // Only admins can delete alerts
        if (Auth::user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $alert->delete();

        return response()->json(['message' => 'Alert deleted successfully']);
    }

    /**
     * Acknowledge an alert.
     */
    public function acknowledge(Alert $alert)
    {
        $success = $this->alertService->acknowledgeAlert($alert, Auth::user());

        if ($success) {
            return response()->json([
                'message' => 'Alert acknowledged successfully',
                'alert' => $alert->fresh()->load(['acknowledgedBy'])
            ]);
        }

        return response()->json(['message' => 'Failed to acknowledge alert'], 500);
    }

    /**
     * Resolve an alert.
     */
    public function resolve(Request $request, Alert $alert)
    {
        $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        $success = $this->alertService->resolveAlert($alert, Auth::user(), $request->notes);

        if ($success) {
            return response()->json([
                'message' => 'Alert resolved successfully',
                'alert' => $alert->fresh()->load(['resolvedBy'])
            ]);
        }

        return response()->json(['message' => 'Failed to resolve alert'], 500);
    }

    /**
     * Get alert statistics.
     */
    public function statistics()
    {
        $user = Auth::user();
        $alerts = $this->alertService->getAlertsForUser($user);

        $stats = [
            'total' => $alerts->count(),
            'by_status' => [
                'active' => $alerts->where('status', 'active')->count(),
                'acknowledged' => $alerts->where('status', 'acknowledged')->count(),
                'resolved' => $alerts->where('status', 'resolved')->count(),
            ],
            'by_severity' => [
                'critical' => $alerts->where('severity', 'critical')->count(),
                'high' => $alerts->where('severity', 'high')->count(),
                'medium' => $alerts->where('severity', 'medium')->count(),
                'low' => $alerts->where('severity', 'low')->count(),
            ],
            'by_type' => $alerts->groupBy('type')->map->count(),
        ];

        return response()->json($stats);
    }
}
