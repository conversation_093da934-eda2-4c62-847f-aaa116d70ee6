<?php

namespace App\Services;

use App\Models\Field;
use App\Models\SatelliteData;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class GeospatialService
{
    /**
     * Calculate NDVI from satellite data.
     */
    public function calculateNDVI(array $redBand, array $nirBand): float
    {
        if (empty($redBand) || empty($nirBand)) {
            return 0.0;
        }

        $redAvg = array_sum($redBand) / count($redBand);
        $nirAvg = array_sum($nirBand) / count($nirBand);

        if (($nirAvg + $redAvg) == 0) {
            return 0.0;
        }

        return ($nirAvg - $redAvg) / ($nirAvg + $redAvg);
    }

    /**
     * Calculate Enhanced Vegetation Index (EVI).
     */
    public function calculateEVI(array $redBand, array $nirBand, array $blueBand): float
    {
        if (empty($redBand) || empty($nirBand) || empty($blueBand)) {
            return 0.0;
        }

        $redAvg = array_sum($redBand) / count($redBand);
        $nirAvg = array_sum($nirBand) / count($nirBand);
        $blueAvg = array_sum($blueBand) / count($blueBand);

        $denominator = $nirAvg + 6 * $redAvg - 7.5 * $blueAvg + 1;

        if ($denominator == 0) {
            return 0.0;
        }

        return 2.5 * (($nirAvg - $redAvg) / $denominator);
    }

    /**
     * Calculate moisture index.
     */
    public function calculateMoistureIndex(array $nirBand, array $swirBand): float
    {
        if (empty($nirBand) || empty($swirBand)) {
            return 0.0;
        }

        $nirAvg = array_sum($nirBand) / count($nirBand);
        $swirAvg = array_sum($swirBand) / count($swirBand);

        if (($nirAvg + $swirAvg) == 0) {
            return 0.0;
        }

        return ($nirAvg - $swirAvg) / ($nirAvg + $swirAvg);
    }

    /**
     * Analyze field health based on NDVI values.
     */
    public function analyzeFieldHealth(Field $field): array
    {
        $latestData = $field->satelliteData()
            ->completed()
            ->latest('acquisition_date')
            ->first();

        if (!$latestData) {
            return [
                'status' => 'no_data',
                'health_score' => 0,
                'recommendations' => ['No satellite data available for analysis'],
            ];
        }

        $ndvi = $latestData->ndvi_average;
        $healthScore = $this->calculateHealthScore($ndvi);
        $status = $this->getHealthStatus($ndvi);
        $recommendations = $this->generateRecommendations($ndvi, $latestData);

        return [
            'status' => $status,
            'health_score' => $healthScore,
            'ndvi_value' => $ndvi,
            'last_updated' => $latestData->acquisition_date,
            'recommendations' => $recommendations,
        ];
    }

    /**
     * Calculate health score from NDVI (0-100).
     */
    private function calculateHealthScore(float $ndvi): int
    {
        if ($ndvi < 0) return 0;
        if ($ndvi > 0.9) return 100;
        
        return (int) round($ndvi * 100);
    }

    /**
     * Get health status from NDVI value.
     */
    private function getHealthStatus(float $ndvi): string
    {
        if ($ndvi >= 0.7) return 'excellent';
        if ($ndvi >= 0.5) return 'good';
        if ($ndvi >= 0.3) return 'moderate';
        if ($ndvi >= 0.1) return 'poor';
        return 'critical';
    }

    /**
     * Generate recommendations based on satellite data.
     */
    private function generateRecommendations(float $ndvi, SatelliteData $data): array
    {
        $recommendations = [];

        if ($ndvi < 0.3) {
            $recommendations[] = 'Immediate attention required - vegetation stress detected';
            $recommendations[] = 'Check irrigation systems and water availability';
            $recommendations[] = 'Consider soil testing for nutrient deficiencies';
        } elseif ($ndvi < 0.5) {
            $recommendations[] = 'Monitor field closely for potential issues';
            $recommendations[] = 'Consider fertilizer application if appropriate';
        } elseif ($ndvi >= 0.7) {
            $recommendations[] = 'Field showing excellent health';
            $recommendations[] = 'Continue current management practices';
        }

        if ($data->moisture_index && $data->moisture_index < 0.2) {
            $recommendations[] = 'Low moisture detected - increase irrigation';
        }

        if ($data->cloud_coverage && $data->cloud_coverage > 50) {
            $recommendations[] = 'High cloud coverage may affect data accuracy';
        }

        return $recommendations;
    }

    /**
     * Detect potential fraud in field claims.
     */
    public function detectFraud(Field $field, array $claimData): array
    {
        $fraudIndicators = [];
        $riskScore = 0.0;

        // Check if claimed area matches satellite-derived area
        $satelliteArea = $this->calculateFieldArea($field->boundary_coordinates);
        $claimedArea = $claimData['claimed_area'] ?? 0;

        if ($satelliteArea > 0 && $claimedArea > 0) {
            $areaDifference = abs($satelliteArea - $claimedArea) / $satelliteArea;
            
            if ($areaDifference > 0.2) { // 20% difference threshold
                $fraudIndicators[] = 'Significant discrepancy between claimed and satellite-measured area';
                $riskScore += 0.4;
            }
        }

        // Check vegetation patterns
        $recentData = $field->satelliteData()
            ->completed()
            ->where('acquisition_date', '>=', Carbon::now()->subMonths(3))
            ->get();

        if ($recentData->count() > 0) {
            $avgNdvi = $recentData->avg('ndvi_average');
            
            if ($avgNdvi < 0.2 && isset($claimData['crop_type'])) {
                $fraudIndicators[] = 'Low vegetation index inconsistent with claimed crop type';
                $riskScore += 0.3;
            }
        }

        // Check temporal consistency
        if (isset($claimData['planting_date'])) {
            $plantingDate = Carbon::parse($claimData['planting_date']);
            $dataAfterPlanting = $field->satelliteData()
                ->completed()
                ->where('acquisition_date', '>=', $plantingDate)
                ->orderBy('acquisition_date')
                ->get();

            if ($dataAfterPlanting->count() >= 2) {
                $firstNdvi = $dataAfterPlanting->first()->ndvi_average;
                $lastNdvi = $dataAfterPlanting->last()->ndvi_average;
                
                if ($lastNdvi <= $firstNdvi) {
                    $fraudIndicators[] = 'No vegetation growth detected after claimed planting date';
                    $riskScore += 0.3;
                }
            }
        }

        return [
            'risk_score' => min($riskScore, 1.0),
            'risk_level' => $this->getRiskLevel($riskScore),
            'indicators' => $fraudIndicators,
        ];
    }

    /**
     * Calculate field area from boundary coordinates.
     */
    public function calculateFieldArea(array $coordinates): float
    {
        if (empty($coordinates) || !isset($coordinates['coordinates'])) {
            return 0.0;
        }

        // Simple polygon area calculation using shoelace formula
        $coords = $coordinates['coordinates'][0] ?? $coordinates;
        $n = count($coords);
        
        if ($n < 3) return 0.0;

        $area = 0.0;
        for ($i = 0; $i < $n; $i++) {
            $j = ($i + 1) % $n;
            $area += $coords[$i][0] * $coords[$j][1];
            $area -= $coords[$j][0] * $coords[$i][1];
        }
        
        $area = abs($area) / 2.0;
        
        // Convert from degrees to hectares (approximate)
        // This is a rough approximation - in production, use proper geodetic calculations
        return $area * 111320 * 111320 / 10000; // Convert to hectares
    }

    /**
     * Get risk level from score.
     */
    private function getRiskLevel(float $score): string
    {
        if ($score >= 0.8) return 'very_high';
        if ($score >= 0.6) return 'high';
        if ($score >= 0.4) return 'medium';
        if ($score >= 0.2) return 'low';
        return 'very_low';
    }

    /**
     * Process satellite imagery for a field.
     */
    public function processSatelliteImagery(Field $field, array $imageData): SatelliteData
    {
        $bandData = $imageData['bands'] ?? [];
        
        $ndviAvg = null;
        $ndviMin = null;
        $ndviMax = null;
        $eviAvg = null;
        $moistureIndex = null;

        if (isset($bandData['red']) && isset($bandData['nir'])) {
            $ndviValues = [];
            for ($i = 0; $i < count($bandData['red']); $i++) {
                $ndvi = $this->calculateNDVI([$bandData['red'][$i]], [$bandData['nir'][$i]]);
                $ndviValues[] = $ndvi;
            }
            
            if (!empty($ndviValues)) {
                $ndviAvg = array_sum($ndviValues) / count($ndviValues);
                $ndviMin = min($ndviValues);
                $ndviMax = max($ndviValues);
            }
        }

        if (isset($bandData['red']) && isset($bandData['nir']) && isset($bandData['blue'])) {
            $eviAvg = $this->calculateEVI($bandData['red'], $bandData['nir'], $bandData['blue']);
        }

        if (isset($bandData['nir']) && isset($bandData['swir'])) {
            $moistureIndex = $this->calculateMoistureIndex($bandData['nir'], $bandData['swir']);
        }

        return SatelliteData::create([
            'field_id' => $field->id,
            'source' => $imageData['source'] ?? 'unknown',
            'scene_id' => $imageData['scene_id'] ?? uniqid(),
            'acquisition_date' => $imageData['acquisition_date'] ?? now(),
            'cloud_coverage' => $imageData['cloud_coverage'] ?? null,
            'ndvi_average' => $ndviAvg,
            'ndvi_min' => $ndviMin,
            'ndvi_max' => $ndviMax,
            'evi_average' => $eviAvg,
            'moisture_index' => $moistureIndex,
            'image_url' => $imageData['image_url'] ?? null,
            'thumbnail_url' => $imageData['thumbnail_url'] ?? null,
            'band_data' => $bandData,
            'analysis_results' => [
                'processed_at' => now(),
                'processing_version' => '1.0',
            ],
            'processing_status' => 'completed',
        ]);
    }
}
