<?php

namespace App\Services;

use App\Models\User;
use App\Models\Alert;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Http;

class NotificationService
{
    /**
     * Send alert notification to user.
     */
    public function sendAlert(User $user, Alert $alert): bool
    {
        try {
            $channels = $this->getNotificationChannels($user, $alert);
            
            foreach ($channels as $channel) {
                switch ($channel) {
                    case 'email':
                        $this->sendEmailAlert($user, $alert);
                        break;
                    case 'sms':
                        $this->sendSMSAlert($user, $alert);
                        break;
                    case 'whatsapp':
                        $this->sendWhatsAppAlert($user, $alert);
                        break;
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send alert notification', [
                'user_id' => $user->id,
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Send email alert.
     */
    protected function sendEmailAlert(User $user, Alert $alert): void
    {
        try {
            // In production, create a proper Mailable class
            $subject = "AgroVue Alert: {$alert->title}";
            $message = $this->formatAlertMessage($alert);
            
            // For now, just log the email (replace with actual email sending)
            Log::info('Email alert sent', [
                'to' => $user->email,
                'subject' => $subject,
                'alert_id' => $alert->id,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send email alert', [
                'user_id' => $user->id,
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send SMS alert.
     */
    protected function sendSMSAlert(User $user, Alert $alert): void
    {
        if (!$user->phone || !config('services.twilio.enabled', false)) {
            return;
        }

        try {
            $message = $this->formatSMSMessage($alert);
            
            // Twilio SMS implementation would go here
            // For now, just log the SMS
            Log::info('SMS alert sent', [
                'to' => $user->phone,
                'message' => $message,
                'alert_id' => $alert->id,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send SMS alert', [
                'user_id' => $user->id,
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send WhatsApp alert.
     */
    protected function sendWhatsAppAlert(User $user, Alert $alert): void
    {
        if (!$user->phone || !config('services.whatsapp.enabled', false)) {
            return;
        }

        try {
            $message = $this->formatWhatsAppMessage($alert);
            
            // WhatsApp Business API implementation would go here
            // For now, just log the WhatsApp message
            Log::info('WhatsApp alert sent', [
                'to' => $user->phone,
                'message' => $message,
                'alert_id' => $alert->id,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp alert', [
                'user_id' => $user->id,
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get notification channels for user and alert.
     */
    protected function getNotificationChannels(User $user, Alert $alert): array
    {
        $channels = ['email']; // Always send email

        // Add SMS for critical alerts
        if ($alert->severity === 'critical' && $user->phone) {
            $channels[] = 'sms';
        }

        // Add WhatsApp for high priority alerts if enabled
        if (in_array($alert->severity, ['critical', 'high']) && $user->phone) {
            $channels[] = 'whatsapp';
        }

        return $channels;
    }

    /**
     * Format alert message for email.
     */
    protected function formatAlertMessage(Alert $alert): string
    {
        $message = "Alert Type: {$alert->type}\n";
        $message .= "Severity: {$alert->severity}\n";
        $message .= "Title: {$alert->title}\n\n";
        $message .= "Message: {$alert->message}\n\n";
        
        if ($alert->field) {
            $message .= "Field: {$alert->field->name}\n";
            $message .= "Location: {$alert->field->location_address}\n";
        }
        
        $message .= "Time: {$alert->created_at->format('Y-m-d H:i:s')}\n";
        $message .= "\nPlease log into AgroVue to view more details and take action.";
        
        return $message;
    }

    /**
     * Format alert message for SMS.
     */
    protected function formatSMSMessage(Alert $alert): string
    {
        $message = "AgroVue Alert: {$alert->title}";
        
        if ($alert->field) {
            $message .= " - {$alert->field->name}";
        }
        
        $message .= ". Severity: {$alert->severity}. Check AgroVue app for details.";
        
        return substr($message, 0, 160); // SMS character limit
    }

    /**
     * Format alert message for WhatsApp.
     */
    protected function formatWhatsAppMessage(Alert $alert): string
    {
        $message = "🚨 *AgroVue Alert*\n\n";
        $message .= "*{$alert->title}*\n";
        $message .= "Severity: {$alert->severity}\n\n";
        $message .= $alert->message . "\n\n";
        
        if ($alert->field) {
            $message .= "📍 Field: {$alert->field->name}\n";
        }
        
        $message .= "⏰ Time: {$alert->created_at->format('Y-m-d H:i')}\n\n";
        $message .= "Please check the AgroVue platform for more details.";
        
        return $message;
    }

    /**
     * Send bulk notification to multiple users.
     */
    public function sendBulkNotification(array $userIds, string $subject, string $message, string $type = 'general'): array
    {
        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        $users = User::whereIn('id', $userIds)->where('is_active', true)->get();

        foreach ($users as $user) {
            try {
                // Create a temporary alert for notification formatting
                $tempAlert = new Alert([
                    'type' => $type,
                    'severity' => 'medium',
                    'title' => $subject,
                    'message' => $message,
                ]);

                $this->sendEmailAlert($user, $tempAlert);
                $results['sent']++;
                
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        Log::info('Bulk notification completed', $results);

        return $results;
    }

    /**
     * Send welcome notification to new user.
     */
    public function sendWelcomeNotification(User $user): void
    {
        try {
            $subject = 'Welcome to AgroVue';
            $message = "Welcome to AgroVue, {$user->name}!\n\n";
            $message .= "Your account has been created successfully. You can now access the platform to:\n";
            $message .= "- Monitor your agricultural fields\n";
            $message .= "- Receive satellite-based insights\n";
            $message .= "- Get real-time alerts\n";
            $message .= "- Access agricultural reports\n\n";
            $message .= "If you have any questions, please contact our support team.";

            // Create temporary alert for formatting
            $tempAlert = new Alert([
                'type' => 'general',
                'severity' => 'low',
                'title' => $subject,
                'message' => $message,
            ]);

            $this->sendEmailAlert($user, $tempAlert);
            
        } catch (\Exception $e) {
            Log::error('Failed to send welcome notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
