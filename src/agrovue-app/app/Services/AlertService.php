<?php

namespace App\Services;

use App\Models\Alert;
use App\Models\Field;
use App\Models\User;
use App\Models\Organization;
use App\Models\SatelliteData;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AlertService
{
    protected $notificationService;
    protected $geospatialService;

    public function __construct(NotificationService $notificationService, GeospatialService $geospatialService)
    {
        $this->notificationService = $notificationService;
        $this->geospatialService = $geospatialService;
    }

    /**
     * Create a new alert.
     */
    public function createAlert(array $data): Alert
    {
        $alert = Alert::create([
            'type' => $data['type'],
            'severity' => $data['severity'],
            'title' => $data['title'],
            'message' => $data['message'],
            'field_id' => $data['field_id'] ?? null,
            'organization_id' => $data['organization_id'] ?? null,
            'affected_users' => $data['affected_users'] ?? [],
            'affected_regions' => $data['affected_regions'] ?? [],
            'metadata' => $data['metadata'] ?? [],
            'is_automated' => $data['is_automated'] ?? false,
            'source' => $data['source'] ?? 'system',
        ]);

        // Send notifications
        $this->sendAlertNotifications($alert);

        Log::info('Alert created', [
            'alert_id' => $alert->id,
            'type' => $alert->type,
            'severity' => $alert->severity,
        ]);

        return $alert;
    }

    /**
     * Monitor fields for drought conditions.
     */
    public function monitorDroughtConditions(): array
    {
        $alerts = [];
        $droughtThreshold = 0.3; // NDVI threshold for drought detection

        $fields = Field::with(['satelliteData' => function($query) {
            $query->completed()
                  ->latest('acquisition_date')
                  ->limit(1);
        }])->get();

        foreach ($fields as $field) {
            $latestData = $field->satelliteData->first();
            
            if (!$latestData || !$latestData->ndvi_average) {
                continue;
            }

            if ($latestData->ndvi_average < $droughtThreshold) {
                $severity = $this->calculateDroughtSeverity($latestData->ndvi_average);
                
                // Check if alert already exists for this field
                $existingAlert = Alert::where('field_id', $field->id)
                    ->where('type', 'drought')
                    ->where('status', 'active')
                    ->first();

                if (!$existingAlert) {
                    $alert = $this->createAlert([
                        'type' => 'drought',
                        'severity' => $severity,
                        'title' => "Drought Stress Detected - {$field->name}",
                        'message' => "Field {$field->name} shows signs of drought stress with NDVI value of {$latestData->ndvi_average}. Immediate attention may be required.",
                        'field_id' => $field->id,
                        'organization_id' => $field->organization_id,
                        'affected_users' => [$field->owner_id],
                        'metadata' => [
                            'ndvi_value' => $latestData->ndvi_average,
                            'acquisition_date' => $latestData->acquisition_date,
                            'threshold' => $droughtThreshold,
                        ],
                        'is_automated' => true,
                        'source' => 'satellite_monitoring',
                    ]);

                    $alerts[] = $alert;
                }
            }
        }

        return $alerts;
    }

    /**
     * Monitor for potential fraud in subsidy claims.
     */
    public function monitorFraudIndicators(): array
    {
        $alerts = [];
        $fraudThreshold = 0.7; // Risk score threshold for fraud alerts

        $fields = Field::with(['subsidyClaims' => function($query) {
            $query->whereIn('status', ['pending', 'under_review']);
        }])->get();

        foreach ($fields as $field) {
            foreach ($field->subsidyClaims as $claim) {
                $fraudAnalysis = $this->geospatialService->detectFraud($field, [
                    'claimed_area' => $claim->claimed_area_hectares,
                    'crop_type' => $claim->crop_type,
                    'planting_date' => $claim->planting_date,
                ]);

                if ($fraudAnalysis['risk_score'] >= $fraudThreshold) {
                    // Check if alert already exists for this claim
                    $existingAlert = Alert::where('type', 'fraud')
                        ->where('status', 'active')
                        ->whereJsonContains('metadata->claim_id', $claim->id)
                        ->first();

                    if (!$existingAlert) {
                        $alert = $this->createAlert([
                            'type' => 'fraud',
                            'severity' => 'high',
                            'title' => "Potential Fraud Detected - Claim #{$claim->claim_number}",
                            'message' => "Subsidy claim #{$claim->claim_number} for field {$field->name} shows potential fraud indicators. Risk score: {$fraudAnalysis['risk_score']}",
                            'field_id' => $field->id,
                            'organization_id' => $field->organization_id,
                            'affected_users' => [$claim->claimant_id],
                            'metadata' => [
                                'claim_id' => $claim->id,
                                'claim_number' => $claim->claim_number,
                                'risk_score' => $fraudAnalysis['risk_score'],
                                'indicators' => $fraudAnalysis['indicators'],
                            ],
                            'is_automated' => true,
                            'source' => 'fraud_detection',
                        ]);

                        $alerts[] = $alert;
                    }
                }
            }
        }

        return $alerts;
    }

    /**
     * Monitor weather-related alerts.
     */
    public function monitorWeatherAlerts(): array
    {
        $alerts = [];
        
        // This would integrate with weather APIs in production
        // For now, we'll create sample weather alerts based on satellite data patterns
        
        $fields = Field::with(['satelliteData' => function($query) {
            $query->completed()
                  ->where('acquisition_date', '>=', Carbon::now()->subDays(7))
                  ->orderBy('acquisition_date', 'desc');
        }])->get();

        foreach ($fields as $field) {
            $recentData = $field->satelliteData;
            
            if ($recentData->count() >= 2) {
                $latest = $recentData->first();
                $previous = $recentData->skip(1)->first();

                // Check for rapid vegetation decline (potential flood/storm damage)
                if ($latest->ndvi_average && $previous->ndvi_average) {
                    $ndviChange = $latest->ndvi_average - $previous->ndvi_average;
                    
                    if ($ndviChange < -0.3) { // Significant decline
                        $alert = $this->createAlert([
                            'type' => 'weather',
                            'severity' => 'high',
                            'title' => "Rapid Vegetation Decline - {$field->name}",
                            'message' => "Field {$field->name} shows rapid vegetation decline, possibly due to severe weather events.",
                            'field_id' => $field->id,
                            'organization_id' => $field->organization_id,
                            'affected_users' => [$field->owner_id],
                            'metadata' => [
                                'ndvi_change' => $ndviChange,
                                'current_ndvi' => $latest->ndvi_average,
                                'previous_ndvi' => $previous->ndvi_average,
                            ],
                            'is_automated' => true,
                            'source' => 'satellite_monitoring',
                        ]);

                        $alerts[] = $alert;
                    }
                }
            }
        }

        return $alerts;
    }

    /**
     * Send notifications for an alert.
     */
    protected function sendAlertNotifications(Alert $alert): void
    {
        try {
            // Get affected users
            $userIds = $alert->affected_users ?? [];
            
            if ($alert->field_id) {
                $field = Field::find($alert->field_id);
                if ($field) {
                    $userIds[] = $field->owner_id;
                }
            }

            if ($alert->organization_id) {
                $orgUsers = User::where('organization_id', $alert->organization_id)
                    ->where('is_active', true)
                    ->pluck('id')
                    ->toArray();
                $userIds = array_merge($userIds, $orgUsers);
            }

            $userIds = array_unique($userIds);
            $users = User::whereIn('id', $userIds)->get();

            foreach ($users as $user) {
                $this->notificationService->sendAlert($user, $alert);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send alert notifications', [
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Calculate drought severity based on NDVI.
     */
    protected function calculateDroughtSeverity(float $ndvi): string
    {
        if ($ndvi < 0.1) return 'critical';
        if ($ndvi < 0.2) return 'high';
        if ($ndvi < 0.3) return 'medium';
        return 'low';
    }

    /**
     * Acknowledge an alert.
     */
    public function acknowledgeAlert(Alert $alert, User $user): bool
    {
        try {
            $alert->acknowledge($user);
            
            Log::info('Alert acknowledged', [
                'alert_id' => $alert->id,
                'user_id' => $user->id,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to acknowledge alert', [
                'alert_id' => $alert->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Resolve an alert.
     */
    public function resolveAlert(Alert $alert, User $user, string $notes = null): bool
    {
        try {
            $alert->resolve($user, $notes);
            
            Log::info('Alert resolved', [
                'alert_id' => $alert->id,
                'user_id' => $user->id,
                'notes' => $notes,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to resolve alert', [
                'alert_id' => $alert->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get alerts for a user based on their role and organization.
     */
    public function getAlertsForUser(User $user, array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = Alert::with(['field', 'organization']);

        // Apply role-based filtering
        switch ($user->role) {
            case 'admin':
                // Admin can see all alerts
                break;
                
            case 'government':
            case 'ngo':
            case 'corporate':
            case 'bank':
                // Organization-level access
                $query->where('organization_id', $user->organization_id);
                break;
                
            case 'cooperative':
                // Can see own fields and organization alerts
                $query->where(function($q) use ($user) {
                    $q->where('organization_id', $user->organization_id)
                      ->orWhereHas('field', function($fq) use ($user) {
                          $fq->where('owner_id', $user->id);
                      });
                });
                break;
        }

        // Apply additional filters
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['severity'])) {
            $query->where('severity', $filters['severity']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        } else {
            $query->where('status', 'active'); // Default to active alerts
        }

        return $query->latest()->get();
    }

    /**
     * Run all automated monitoring tasks.
     */
    public function runAutomatedMonitoring(): array
    {
        $results = [
            'drought_alerts' => [],
            'fraud_alerts' => [],
            'weather_alerts' => [],
        ];

        try {
            $results['drought_alerts'] = $this->monitorDroughtConditions();
            $results['fraud_alerts'] = $this->monitorFraudIndicators();
            $results['weather_alerts'] = $this->monitorWeatherAlerts();

            Log::info('Automated monitoring completed', [
                'drought_alerts_created' => count($results['drought_alerts']),
                'fraud_alerts_created' => count($results['fraud_alerts']),
                'weather_alerts_created' => count($results['weather_alerts']),
            ]);
        } catch (\Exception $e) {
            Log::error('Automated monitoring failed', [
                'error' => $e->getMessage(),
            ]);
        }

        return $results;
    }
}
