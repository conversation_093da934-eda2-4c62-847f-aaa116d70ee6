<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get organizations for reference
        $fmard = Organization::where('registration_number', 'FMARD-001')->first();
        $ksadp = Organization::where('registration_number', 'KSADP-001')->first();
        $actionaid = Organization::where('registration_number', 'NGO-AA-001')->first();
        $ifad = Organization::where('registration_number', 'NGO-IFAD-001')->first();
        $dangote = Organization::where('registration_number', 'RC-DANG-AGR-001')->first();
        $olam = Organization::where('registration_number', 'RC-OLAM-001')->first();
        $kebbiRice = Organization::where('registration_number', 'COOP-KRFA-001')->first();
        $ogunCassava = Organization::where('registration_number', 'COOP-OSCGC-001')->first();
        $boa = Organization::where('registration_number', 'CBN-BOA-001')->first();
        $firstBank = Organization::where('registration_number', 'CBN-FBN-AGR-001')->first();

        $users = [
            // Admin User
            [
                'name' => 'AgroVue Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'organization_id' => null,
                'phone' => '+234-800-AGROVUE',
                'position' => 'System Administrator',
                'permissions' => ['all'],
                'is_active' => true,
            ],

            // Government Users
            [
                'name' => 'Amina Hassan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'government',
                'organization_id' => $fmard?->id,
                'phone' => '+234-************',
                'position' => 'Director of Agricultural Monitoring',
                'permissions' => ['view_all_fields', 'manage_subsidies', 'generate_reports'],
                'is_active' => true,
            ],
            [
                'name' => 'Ibrahim Musa',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'government',
                'organization_id' => $ksadp?->id,
                'phone' => '+234-************',
                'position' => 'State Coordinator',
                'permissions' => ['view_state_fields', 'manage_local_subsidies'],
                'is_active' => true,
            ],

            // NGO Users
            [
                'name' => 'Sarah Okafor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'ngo',
                'organization_id' => $actionaid?->id,
                'phone' => '+234-************',
                'position' => 'Program Manager - Food Security',
                'permissions' => ['view_beneficiary_fields', 'create_alerts', 'generate_reports'],
                'is_active' => true,
            ],
            [
                'name' => 'Michael Adebayo',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'ngo',
                'organization_id' => $ifad?->id,
                'phone' => '+234-************',
                'position' => 'Country Program Officer',
                'permissions' => ['view_project_fields', 'monitor_progress'],
                'is_active' => true,
            ],

            // Corporate Users
            [
                'name' => 'Fatima Dangote',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'corporate',
                'organization_id' => $dangote?->id,
                'phone' => '+234-************',
                'position' => 'Head of Agricultural Operations',
                'permissions' => ['view_contract_fields', 'manage_supply_chain'],
                'is_active' => true,
            ],
            [
                'name' => 'James Okonkwo',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'corporate',
                'organization_id' => $olam?->id,
                'phone' => '+234-************',
                'position' => 'Commodity Manager',
                'permissions' => ['view_supplier_fields', 'quality_monitoring'],
                'is_active' => true,
            ],

            // Cooperative Users
            [
                'name' => 'Usman Kebbi',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'cooperative',
                'organization_id' => $kebbiRice?->id,
                'phone' => '+234-************',
                'position' => 'Chairman',
                'permissions' => ['manage_member_fields', 'coordinate_activities'],
                'is_active' => true,
            ],
            [
                'name' => 'Folake Adeyemi',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'cooperative',
                'organization_id' => $ogunCassava?->id,
                'phone' => '+234-************',
                'position' => 'Secretary',
                'permissions' => ['manage_member_fields', 'record_keeping'],
                'is_active' => true,
            ],

            // Bank Users
            [
                'name' => 'Kemi Adeyemi',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'bank',
                'organization_id' => $boa?->id,
                'phone' => '+234-************',
                'position' => 'Agricultural Credit Analyst',
                'permissions' => ['assess_loan_applications', 'monitor_collateral'],
                'is_active' => true,
            ],
            [
                'name' => 'David Okoro',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'bank',
                'organization_id' => $firstBank?->id,
                'phone' => '+234-************',
                'position' => 'Agribusiness Relationship Manager',
                'permissions' => ['manage_agri_portfolio', 'risk_assessment'],
                'is_active' => true,
            ],

            // Sample Farmers (Cooperative Members)
            [
                'name' => 'Aliyu Tanko',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'cooperative',
                'organization_id' => $kebbiRice?->id,
                'phone' => '+234-************',
                'position' => 'Rice Farmer',
                'permissions' => ['manage_own_fields'],
                'is_active' => true,
            ],
            [
                'name' => 'Blessing Okafor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'cooperative',
                'organization_id' => $ogunCassava?->id,
                'phone' => '+234-************',
                'position' => 'Cassava Farmer',
                'permissions' => ['manage_own_fields'],
                'is_active' => true,
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }
    }
}
