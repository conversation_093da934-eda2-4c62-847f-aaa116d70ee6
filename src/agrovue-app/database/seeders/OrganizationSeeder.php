<?php

namespace Database\Seeders;

use App\Models\Organization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $organizations = [
            // Government Organizations
            [
                'name' => 'Federal Ministry of Agriculture and Rural Development',
                'type' => 'government',
                'registration_number' => 'FMARD-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-9-523-0000',
                'address' => 'Area 11, Garki, Abuja',
                'country' => 'NG',
                'state' => 'FCT',
                'city' => 'Abuja',
                'settings' => [
                    'subsidy_programs' => ['fertilizer', 'seeds', 'equipment'],
                    'monitoring_regions' => ['north', 'middle_belt', 'south'],
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Kebbi State Agricultural Development Program',
                'type' => 'government',
                'registration_number' => 'KSADP-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-68-320-000',
                'address' => 'Birnin Kebbi, Kebbi State',
                'country' => 'NG',
                'state' => 'Kebbi',
                'city' => 'Birnin Kebbi',
                'settings' => [
                    'focus_crops' => ['rice', 'wheat', 'maize'],
                    'irrigation_projects' => true,
                ],
                'is_active' => true,
            ],

            // NGO Organizations
            [
                'name' => 'ActionAid Nigeria',
                'type' => 'ngo',
                'registration_number' => 'NGO-AA-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-270-0000',
                'address' => 'Plot 590, Zone AO, Central Business District, Abuja',
                'country' => 'NG',
                'state' => 'FCT',
                'city' => 'Abuja',
                'settings' => [
                    'focus_areas' => ['food_security', 'climate_adaptation', 'women_empowerment'],
                    'target_regions' => ['northern_nigeria', 'middle_belt'],
                ],
                'is_active' => true,
            ],
            [
                'name' => 'International Fund for Agricultural Development Nigeria',
                'type' => 'ngo',
                'registration_number' => 'NGO-IFAD-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-9-461-0000',
                'address' => 'UN House, Plot 617/618, Central Business District, Abuja',
                'country' => 'NG',
                'state' => 'FCT',
                'city' => 'Abuja',
                'settings' => [
                    'programs' => ['rural_development', 'smallholder_support', 'value_chain'],
                    'funding_available' => true,
                ],
                'is_active' => true,
            ],

            // Corporate Organizations
            [
                'name' => 'Dangote Group Agricultural Division',
                'type' => 'corporate',
                'registration_number' => 'RC-DANG-AGR-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-448-0000',
                'address' => '1 Alfred Rewane Road, Ikoyi, Lagos',
                'country' => 'NG',
                'state' => 'Lagos',
                'city' => 'Lagos',
                'settings' => [
                    'business_type' => 'agribusiness',
                    'supply_chain' => ['fertilizer', 'food_processing', 'distribution'],
                    'contract_farming' => true,
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Olam Nigeria Limited',
                'type' => 'corporate',
                'registration_number' => 'RC-OLAM-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-270-1000',
                'address' => '3 Idowu Taylor Street, Victoria Island, Lagos',
                'country' => 'NG',
                'state' => 'Lagos',
                'city' => 'Lagos',
                'settings' => [
                    'commodities' => ['cocoa', 'cashew', 'sesame', 'rice'],
                    'processing_facilities' => true,
                    'export_operations' => true,
                ],
                'is_active' => true,
            ],

            // Cooperative Organizations
            [
                'name' => 'Kebbi Rice Farmers Association',
                'type' => 'cooperative',
                'registration_number' => 'COOP-KRFA-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-68-321-000',
                'address' => 'Argungu Road, Birnin Kebbi, Kebbi State',
                'country' => 'NG',
                'state' => 'Kebbi',
                'city' => 'Birnin Kebbi',
                'settings' => [
                    'member_count' => 15000,
                    'primary_crop' => 'rice',
                    'processing_center' => true,
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Ogun State Cassava Growers Cooperative',
                'type' => 'cooperative',
                'registration_number' => 'COOP-OSCGC-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-39-240-000',
                'address' => 'Oke-Mosan, Abeokuta, Ogun State',
                'country' => 'NG',
                'state' => 'Ogun',
                'city' => 'Abeokuta',
                'settings' => [
                    'member_count' => 8500,
                    'primary_crop' => 'cassava',
                    'value_addition' => ['garri', 'flour', 'starch'],
                ],
                'is_active' => true,
            ],

            // Bank Organizations
            [
                'name' => 'Bank of Agriculture Nigeria',
                'type' => 'bank',
                'registration_number' => 'CBN-BOA-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-9-523-1000',
                'address' => 'Plot 446, Zambezi Crescent, Maitama, Abuja',
                'country' => 'NG',
                'state' => 'FCT',
                'city' => 'Abuja',
                'settings' => [
                    'loan_products' => ['crop_financing', 'equipment_loans', 'processing_loans'],
                    'interest_rates' => ['single_digit', 'subsidized'],
                    'collateral_requirements' => 'flexible',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'First Bank Agricultural Finance',
                'type' => 'bank',
                'registration_number' => 'CBN-FBN-AGR-001',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-448-2000',
                'address' => '35 Marina, Lagos Island, Lagos',
                'country' => 'NG',
                'state' => 'Lagos',
                'city' => 'Lagos',
                'settings' => [
                    'digital_banking' => true,
                    'agri_products' => ['value_chain_financing', 'warehouse_receipts'],
                    'partnerships' => ['cooperatives', 'agribusiness'],
                ],
                'is_active' => true,
            ],
        ];

        foreach ($organizations as $orgData) {
            Organization::create($orgData);
        }
    }
}
