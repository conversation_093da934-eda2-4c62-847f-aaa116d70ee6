<?php

namespace Database\Seeders;

use App\Models\Field;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users to assign as field owners
        $farmers = User::where('role', 'cooperative')->get();
        $kebbiRice = Organization::where('registration_number', 'COOP-KRFA-001')->first();
        $ogunCassava = Organization::where('registration_number', 'COOP-OSCGC-001')->first();

        $fields = [
            [
                'name' => 'Argungu Rice Farm Block A',
                'description' => 'Primary rice cultivation area with irrigation system',
                'owner_id' => $farmers->where('email', '<EMAIL>')->first()?->id,
                'organization_id' => $kebbiRice?->id,
                'area_hectares' => 25.5,
                'crop_type' => 'Rice',
                'status' => 'active',
                'boundary_coordinates' => [
                    'type' => 'Polygon',
                    'coordinates' => [[
                        [12.6186, 4.2006],
                        [12.6206, 4.2006],
                        [12.6206, 4.2026],
                        [12.6186, 4.2026],
                        [12.6186, 4.2006]
                    ]]
                ],
                'center_latitude' => 12.6196,
                'center_longitude' => 4.2016,
                'location_address' => 'Argungu, Kebbi State',
                'state' => 'Kebbi',
                'country' => 'NG',
                'planting_date' => '2024-06-15',
                'expected_harvest_date' => '2024-11-15',
                'metadata' => [
                    'irrigation_type' => 'canal',
                    'soil_type' => 'clay_loam',
                    'previous_yield' => '4.2 tons/ha'
                ],
            ],
            [
                'name' => 'Argungu Rice Farm Block B',
                'description' => 'Secondary rice cultivation area',
                'owner_id' => $farmers->where('email', '<EMAIL>')->first()?->id,
                'organization_id' => $kebbiRice?->id,
                'area_hectares' => 18.3,
                'crop_type' => 'Rice',
                'status' => 'active',
                'boundary_coordinates' => [
                    'type' => 'Polygon',
                    'coordinates' => [[
                        [12.6206, 4.2006],
                        [12.6226, 4.2006],
                        [12.6226, 4.2026],
                        [12.6206, 4.2026],
                        [12.6206, 4.2006]
                    ]]
                ],
                'center_latitude' => 12.6216,
                'center_longitude' => 4.2016,
                'location_address' => 'Argungu, Kebbi State',
                'state' => 'Kebbi',
                'country' => 'NG',
                'planting_date' => '2024-06-20',
                'expected_harvest_date' => '2024-11-20',
                'metadata' => [
                    'irrigation_type' => 'canal',
                    'soil_type' => 'clay_loam',
                    'previous_yield' => '3.8 tons/ha'
                ],
            ],
            [
                'name' => 'Abeokuta Cassava Farm North',
                'description' => 'Large cassava plantation for processing',
                'owner_id' => $farmers->where('email', '<EMAIL>')->first()?->id,
                'organization_id' => $ogunCassava?->id,
                'area_hectares' => 32.7,
                'crop_type' => 'Cassava',
                'status' => 'active',
                'boundary_coordinates' => [
                    'type' => 'Polygon',
                    'coordinates' => [[
                        [7.1557, 3.3478],
                        [7.1577, 3.3478],
                        [7.1577, 3.3498],
                        [7.1557, 3.3498],
                        [7.1557, 3.3478]
                    ]]
                ],
                'center_latitude' => 7.1567,
                'center_longitude' => 3.3488,
                'location_address' => 'Abeokuta, Ogun State',
                'state' => 'Ogun',
                'country' => 'NG',
                'planting_date' => '2024-03-10',
                'expected_harvest_date' => '2025-03-10',
                'metadata' => [
                    'variety' => 'TMS 30572',
                    'soil_type' => 'sandy_loam',
                    'processing_contract' => true
                ],
            ],
            [
                'name' => 'Abeokuta Cassava Farm South',
                'description' => 'Cassava farm with intercropping system',
                'owner_id' => $farmers->where('email', '<EMAIL>')->first()?->id,
                'organization_id' => $ogunCassava?->id,
                'area_hectares' => 15.2,
                'crop_type' => 'Cassava',
                'status' => 'active',
                'boundary_coordinates' => [
                    'type' => 'Polygon',
                    'coordinates' => [[
                        [7.1557, 3.3458],
                        [7.1577, 3.3458],
                        [7.1577, 3.3478],
                        [7.1557, 3.3478],
                        [7.1557, 3.3458]
                    ]]
                ],
                'center_latitude' => 7.1567,
                'center_longitude' => 3.3468,
                'location_address' => 'Abeokuta, Ogun State',
                'state' => 'Ogun',
                'country' => 'NG',
                'planting_date' => '2024-04-05',
                'expected_harvest_date' => '2025-04-05',
                'metadata' => [
                    'variety' => 'TMS 98/0505',
                    'soil_type' => 'sandy_loam',
                    'intercrop' => 'maize'
                ],
            ],
            [
                'name' => 'Kebbi Wheat Demonstration Farm',
                'description' => 'Demonstration farm for wheat cultivation',
                'owner_id' => $farmers->where('email', '<EMAIL>')->first()?->id,
                'organization_id' => $kebbiRice?->id,
                'area_hectares' => 12.8,
                'crop_type' => 'Wheat',
                'status' => 'active',
                'boundary_coordinates' => [
                    'type' => 'Polygon',
                    'coordinates' => [[
                        [12.6226, 4.2006],
                        [12.6246, 4.2006],
                        [12.6246, 4.2026],
                        [12.6226, 4.2026],
                        [12.6226, 4.2006]
                    ]]
                ],
                'center_latitude' => 12.6236,
                'center_longitude' => 4.2016,
                'location_address' => 'Argungu, Kebbi State',
                'state' => 'Kebbi',
                'country' => 'NG',
                'planting_date' => '2024-11-01',
                'expected_harvest_date' => '2025-03-15',
                'metadata' => [
                    'variety' => 'FARO 66',
                    'irrigation_type' => 'sprinkler',
                    'demonstration_project' => true
                ],
            ],
        ];

        foreach ($fields as $fieldData) {
            if ($fieldData['owner_id']) { // Only create if owner exists
                Field::create($fieldData);
            }
        }
    }
}
