<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->enum('type', ['field_analysis', 'subsidy_summary', 'fraud_detection', 'yield_prediction', 'custom']);
            $table->text('description')->nullable();
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->json('filters')->nullable(); // Report generation filters
            $table->json('data')->nullable(); // Report data/results
            $table->enum('format', ['pdf', 'excel', 'csv', 'json'])->default('pdf');
            $table->string('file_path')->nullable();
            $table->integer('file_size')->nullable(); // in bytes
            $table->enum('status', ['generating', 'completed', 'failed'])->default('generating');
            $table->text('error_message')->nullable();
            $table->date('period_start')->nullable();
            $table->date('period_end')->nullable();
            $table->boolean('is_scheduled')->default(false);
            $table->string('schedule_frequency')->nullable(); // daily, weekly, monthly
            $table->timestamp('next_generation')->nullable();
            $table->json('recipients')->nullable(); // Email recipients
            $table->boolean('is_public')->default(false);
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['generated_by', 'status']);
            $table->index(['organization_id', 'type']);
            $table->index(['type', 'status']);
            $table->index(['is_scheduled', 'next_generation']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
