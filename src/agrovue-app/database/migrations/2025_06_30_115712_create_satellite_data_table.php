<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('satellite_data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('field_id')->constrained()->onDelete('cascade');
            $table->enum('source', ['sentinel-2', 'landsat-8', 'landsat-9', 'modis']);
            $table->string('scene_id')->unique();
            $table->date('acquisition_date');
            $table->decimal('cloud_coverage', 5, 2)->nullable();
            $table->decimal('ndvi_average', 5, 4)->nullable();
            $table->decimal('ndvi_min', 5, 4)->nullable();
            $table->decimal('ndvi_max', 5, 4)->nullable();
            $table->decimal('evi_average', 5, 4)->nullable();
            $table->decimal('moisture_index', 5, 4)->nullable();
            $table->string('image_url')->nullable();
            $table->string('thumbnail_url')->nullable();
            $table->json('band_data')->nullable(); // Raw band values
            $table->json('analysis_results')->nullable(); // Processed analysis
            $table->enum('processing_status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->text('processing_notes')->nullable();
            $table->timestamps();

            $table->index(['field_id', 'acquisition_date']);
            $table->index(['source', 'acquisition_date']);
            $table->index(['processing_status']);
            $table->index('acquisition_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('satellite_data');
    }
};
