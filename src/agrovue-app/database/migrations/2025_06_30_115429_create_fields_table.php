<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('area_hectares', 10, 4);
            $table->string('crop_type')->nullable();
            $table->enum('status', ['active', 'inactive', 'harvested', 'fallow'])->default('active');
            $table->json('boundary_coordinates'); // GeoJSON polygon
            $table->point('center_point'); // PostGIS point
            $table->string('location_address')->nullable();
            $table->string('state');
            $table->string('country', 2)->default('NG');
            $table->date('planting_date')->nullable();
            $table->date('expected_harvest_date')->nullable();
            $table->json('metadata')->nullable(); // Additional field data
            $table->timestamps();

            $table->index(['owner_id', 'status']);
            $table->index(['organization_id', 'status']);
            $table->index(['crop_type', 'status']);
            $table->index(['state', 'country']);
            $table->spatialIndex('center_point');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
