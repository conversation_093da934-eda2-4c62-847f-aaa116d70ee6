# AgroVue - Agricultural Monitoring Platform

AgroVue is a comprehensive agricultural monitoring platform that leverages satellite data, AI-powered analytics, and multi-stakeholder collaboration to enhance agricultural productivity, ensure food security, and prevent subsidy fraud in Nigeria.

## 🌟 Features

### Core Functionality
- **Satellite-based Field Monitoring**: Real-time crop health analysis using NDVI, EVI, and moisture indices
- **Multi-role Access Control**: Support for government agencies, NGOs, corporates, banks, and cooperatives
- **Automated Alert System**: Drought detection, fraud prevention, and weather-related notifications
- **Subsidy Management**: Transparent tracking and verification of agricultural subsidies
- **Geospatial Analytics**: Advanced field boundary detection and area calculations

### User Roles & Permissions
- **Admin**: Full system access and management
- **Government**: Policy oversight, subsidy management, regional monitoring
- **NGO**: Beneficiary tracking, disaster response, program monitoring
- **Corporate**: Supply chain management, contract farming oversight
- **Bank**: Risk assessment, loan collateral monitoring, credit analysis
- **Cooperative**: Member field management, collective resource coordination

### Technology Stack
- **Backend**: Laravel 12 with PHP 8.3
- **Frontend**: React 18 with TypeScript and Inertia.js
- **UI Components**: ShadCN UI with Tailwind CSS
- **Database**: MySQL with spatial data support
- **Authentication**: Laravel Sanctum for API security
- **Monitoring**: Automated field analysis and alert generation

## 🚀 Quick Start

### Prerequisites
- PHP 8.3+
- Node.js 18+
- MySQL 8.0+
- Composer
- NPM/Yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd agrovue/src/agrovue-app
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure database**
   Update `.env` with your database credentials:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=agrovue
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. **Run migrations and seeders**
   ```bash
   php artisan migrate:fresh --seed
   ```

7. **Start development servers**
   ```bash
   # Terminal 1: Laravel server
   php artisan serve

   # Terminal 2: Vite development server
   npm run dev
   ```

8. **Access the application**
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/api

## 👥 Default User Accounts

The system comes with pre-configured user accounts for testing:

### Admin
- **Email**: <EMAIL>
- **Password**: password
- **Role**: System Administrator

### Government Users
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Federal Ministry of Agriculture

- **Email**: <EMAIL>
- **Password**: password
- **Role**: Kebbi State Agricultural Development

### NGO Users
- **Email**: <EMAIL>
- **Password**: password
- **Role**: ActionAid Nigeria

### Corporate Users
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Dangote Group Agricultural Division

### Bank Users
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Bank of Agriculture

### Farmers/Cooperative Members
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Rice Farmer (Kebbi Rice Farmers Association)

- **Email**: <EMAIL>
- **Password**: password
- **Role**: Cassava Farmer (Ogun State Cassava Growers Cooperative)

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/user` - Get current user

### Fields Management
- `GET /api/fields` - List fields (role-based access)
- `POST /api/fields` - Create new field
- `GET /api/fields/{id}` - Get field details
- `PUT /api/fields/{id}` - Update field
- `DELETE /api/fields/{id}` - Delete field
- `GET /api/fields/{id}/satellite-data` - Get satellite data for field

### Alerts System
- `GET /api/alerts` - List alerts (role-based access)
- `POST /api/alerts` - Create manual alert
- `GET /api/alerts/{id}` - Get alert details
- `POST /api/alerts/{id}/acknowledge` - Acknowledge alert
- `POST /api/alerts/{id}/resolve` - Resolve alert
- `GET /api/alerts-statistics` - Get alert statistics

### Dashboard
- `GET /api/dashboard` - Get dashboard data

## 🤖 Automated Monitoring

The system includes automated monitoring capabilities:

### Field Monitoring Command
```bash
# Monitor all conditions
php artisan agrovue:monitor-fields

# Monitor specific conditions
php artisan agrovue:monitor-fields --type=drought
php artisan agrovue:monitor-fields --type=fraud
php artisan agrovue:monitor-fields --type=weather
```

### Scheduled Tasks
Add to your cron job for automated monitoring:
```bash
# Run every hour
0 * * * * cd /path/to/agrovue && php artisan agrovue:monitor-fields
```

## 🌍 Sample Data

The system includes comprehensive sample data:

### Organizations
- Federal Ministry of Agriculture and Rural Development
- Kebbi State Agricultural Development Program
- ActionAid Nigeria
- International Fund for Agricultural Development
- Dangote Group Agricultural Division
- Olam Nigeria Limited
- Kebbi Rice Farmers Association
- Ogun State Cassava Growers Cooperative
- Bank of Agriculture Nigeria
- First Bank Agricultural Finance

### Fields
- Rice farms in Kebbi State with irrigation systems
- Cassava plantations in Ogun State
- Wheat demonstration farms
- Various crop types and farming systems

## 🔒 Security Features

- **Role-based Access Control**: Granular permissions based on user roles
- **Organization-level Data Isolation**: Users can only access their organization's data
- **API Authentication**: Secure API access using Laravel Sanctum
- **Activity Logging**: Comprehensive audit trail for all system activities
- **Fraud Detection**: AI-powered analysis to detect suspicious subsidy claims

## 📊 Monitoring & Analytics

### Satellite Data Analysis
- **NDVI (Normalized Difference Vegetation Index)**: Crop health assessment
- **EVI (Enhanced Vegetation Index)**: Advanced vegetation monitoring
- **Moisture Index**: Irrigation and drought monitoring
- **Cloud Coverage Analysis**: Data quality assessment

### Alert Types
- **Drought Alerts**: Based on vegetation stress indicators
- **Fraud Alerts**: Suspicious subsidy claim patterns
- **Weather Alerts**: Rapid vegetation changes indicating weather damage
- **Harvest Alerts**: Optimal harvest timing notifications

## 🛠️ Development

### Code Structure
```
app/
├── Http/Controllers/
│   ├── Api/              # API controllers
│   ├── Auth/             # Authentication controllers
│   └── DashboardController.php
├── Models/               # Eloquent models
├── Services/             # Business logic services
└── Console/Commands/     # Artisan commands

resources/js/
├── Pages/                # Inertia.js pages
├── Components/           # React components
└── Layouts/              # Layout components

database/
├── migrations/           # Database migrations
└── seeders/              # Database seeders
```

### Key Services
- **GeospatialService**: Satellite data processing and analysis
- **AlertService**: Automated monitoring and alert generation
- **NotificationService**: Multi-channel notification delivery

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to documentation]
- Issues: [GitHub Issues]

## 🔮 Future Enhancements

- Integration with weather APIs
- Machine learning models for yield prediction
- Mobile application for field workers
- Blockchain-based subsidy tracking
- Integration with IoT sensors
- Advanced reporting and analytics dashboard
