{"hash": "d801bf11", "configHash": "fe3098ac", "lockfileHash": "9f5fe11b", "browserHash": "72781850", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "eaac7165", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "229a363b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "47f487c0", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ccbe98dd", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "d72acfd8", "needsInterop": false}, "@inertiajs/react": {"src": "../../@inertiajs/react/dist/index.esm.js", "file": "@inertiajs_react.js", "fileHash": "af8261a0", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "d859ad18", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "6ac00154", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "80c0aa27", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "212d2ba4", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "17eb4715", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "b1b939c7", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2a9bd070", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "d4ceadda", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "cba67209", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "69d3cf10", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "82dc9553", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "5840060e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6d920246", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "b1dc5d44", "needsInterop": false}}, "chunks": {"chunk-PRAZ6XHD": {"file": "chunk-PRAZ6XHD.js"}, "chunk-4JKHPIC7": {"file": "chunk-4JKHPIC7.js"}, "chunk-OUSBKQTO": {"file": "chunk-OUSBKQTO.js"}, "chunk-BGU7DBJS": {"file": "chunk-BGU7DBJS.js"}, "chunk-5LPZKTKO": {"file": "chunk-5LPZKTKO.js"}, "chunk-5QB6WZBA": {"file": "chunk-5QB6WZBA.js"}, "chunk-EZGML3GP": {"file": "chunk-EZGML3GP.js"}, "chunk-35AGI64M": {"file": "chunk-35AGI64M.js"}, "chunk-Q5B56BVT": {"file": "chunk-Q5B56BVT.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-EK47CIPI": {"file": "chunk-EK47CIPI.js"}, "chunk-3M4ZFO5U": {"file": "chunk-3M4ZFO5U.js"}, "chunk-YJYY6GXC": {"file": "chunk-YJYY6GXC.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}