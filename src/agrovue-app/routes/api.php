<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Api\FieldController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Public API routes
Route::post('/auth/login', [AuthController::class, 'apiLogin']);

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'apiLogout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
    
    // Fields
    Route::apiResource('fields', FieldController::class);
    Route::get('/fields/{field}/satellite-data', [FieldController::class, 'satelliteData']);
    
    // User info
    Route::get('/user', function (Request $request) {
        return $request->user()->load('organization');
    });
});
