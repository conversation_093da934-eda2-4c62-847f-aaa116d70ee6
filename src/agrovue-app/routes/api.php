<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Api\FieldController;
use App\Http\Controllers\Api\AlertController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Public API routes
Route::post('/auth/login', [AuthController::class, 'apiLogin']);

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'apiLogout']);
    Route::get('/auth/user', [AuthController::class, 'user']);

    // Fields
    Route::apiResource('fields', FieldController::class);
    Route::get('/fields/{field}/satellite-data', [FieldController::class, 'satelliteData']);

    // Alerts
    Route::apiResource('alerts', AlertController::class);
    Route::post('/alerts/{alert}/acknowledge', [AlertController::class, 'acknowledge']);
    Route::post('/alerts/{alert}/resolve', [AlertController::class, 'resolve']);
    Route::get('/alerts-statistics', [AlertController::class, 'statistics']);

    // User info
    Route::get('/user', function (Request $request) {
        return $request->user()->load('organization');
    });

    // Dashboard data
    Route::get('/dashboard', function (Request $request) {
        $user = $request->user();

        // Basic dashboard data for API
        return response()->json([
            'user' => $user->load('organization'),
            'stats' => [
                'fields_count' => $user->fields()->count(),
                'active_alerts' => 0, // Would be calculated based on user's access
                'recent_activity' => [],
            ]
        ]);
    });
});
