<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\DashboardController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public routes
Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
})->name('welcome');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Fields management
    Route::get('/fields', function () {
        return Inertia::render('Fields/Index');
    })->name('fields.index');

    Route::get('/fields/create', function () {
        return Inertia::render('Fields/Create');
    })->name('fields.create');

    Route::get('/fields/{field}', function () {
        return Inertia::render('Fields/Show');
    })->name('fields.show');

    // Alerts management
    Route::get('/alerts', function () {
        return Inertia::render('Alerts/Index');
    })->name('alerts.index');

    // Subsidy claims management
    Route::get('/subsidies', function () {
        return Inertia::render('Subsidies/Index');
    })->name('subsidies.index');

    // Reports
    Route::get('/reports', function () {
        return Inertia::render('Reports/Index');
    })->name('reports.index');

    // Profile
    Route::get('/profile', function () {
        return Inertia::render('Profile/Show');
    })->name('profile.show');
});

require __DIR__.'/settings.php';
