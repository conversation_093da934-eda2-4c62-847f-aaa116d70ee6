<?php

namespace Database\Seeders;

use App\Models\Organization;
use Illuminate\Database\Seeder;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $organizations = [
            [
                'name' => 'Federal Ministry of Agriculture and Rural Development',
                'type' => 'government',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-9-523-0000',
                'address' => 'Area 11, Garki, Abuja',
                'country' => 'NG',
                'region' => 'FCT',
                'settings' => [
                    'verification_authority' => true,
                    'subsidy_management' => true,
                ],
            ],
            [
                'name' => 'Kano State Agricultural Development Programme',
                'type' => 'government',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-64-632-000',
                'address' => 'Kano State Secretariat, Kano',
                'country' => 'NG',
                'region' => 'Kano',
                'settings' => [
                    'verification_authority' => true,
                    'regional_coverage' => ['Kano', 'Jigawa', '<PERSON>sina'],
                ],
            ],
            [
                'name' => 'ActionAid Nigeria',
                'type' => 'ngo',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-280-0000',
                'address' => 'Plot 1419, Ahmadu Bello Way, Victoria Island, Lagos',
                'country' => 'NG',
                'region' => 'Lagos',
                'settings' => [
                    'disaster_response' => true,
                    'farmer_support' => true,
                ],
            ],
            [
                'name' => 'Oxfam Nigeria',
                'type' => 'ngo',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-9-291-0000',
                'address' => 'Plot 2408, Herbert Macaulay Way, Central Area, Abuja',
                'country' => 'NG',
                'region' => 'FCT',
                'settings' => [
                    'humanitarian_aid' => true,
                    'climate_adaptation' => true,
                ],
            ],
            [
                'name' => 'Dangote Group',
                'type' => 'corporate',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-448-0000',
                'address' => 'Dangote Industries Limited, Ikoyi, Lagos',
                'country' => 'NG',
                'region' => 'Lagos',
                'settings' => [
                    'supply_chain_monitoring' => true,
                    'contract_farming' => true,
                ],
            ],
            [
                'name' => 'Olam Nigeria Limited',
                'type' => 'corporate',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-270-0000',
                'address' => 'Olam House, Victoria Island, Lagos',
                'country' => 'NG',
                'region' => 'Lagos',
                'settings' => [
                    'commodity_trading' => true,
                    'farmer_financing' => true,
                ],
            ],
            [
                'name' => 'Kebbi Rice Farmers Association',
                'type' => 'cooperative',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-68-320-000',
                'address' => 'Kebbi State Agricultural Development Programme, Birnin Kebbi',
                'country' => 'NG',
                'region' => 'Kebbi',
                'settings' => [
                    'member_count' => 15000,
                    'primary_crop' => 'rice',
                ],
            ],
            [
                'name' => 'Kaduna Maize Growers Cooperative',
                'type' => 'cooperative',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-62-230-000',
                'address' => 'Kaduna State Ministry of Agriculture, Kaduna',
                'country' => 'NG',
                'region' => 'Kaduna',
                'settings' => [
                    'member_count' => 8500,
                    'primary_crop' => 'maize',
                ],
            ],
            [
                'name' => 'Bank of Agriculture',
                'type' => 'bank',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-9-523-8000',
                'address' => 'Herbert Macaulay Way, Central Business District, Abuja',
                'country' => 'NG',
                'region' => 'FCT',
                'settings' => [
                    'agricultural_lending' => true,
                    'loan_verification' => true,
                ],
            ],
            [
                'name' => 'First Bank of Nigeria - Agric Finance',
                'type' => 'bank',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-1-905-2326',
                'address' => 'Samuel Asabia House, 35 Marina, Lagos Island, Lagos',
                'country' => 'NG',
                'region' => 'Lagos',
                'settings' => [
                    'agricultural_lending' => true,
                    'risk_assessment' => true,
                ],
            ],
            [
                'name' => 'International Institute of Tropical Agriculture',
                'type' => 'research',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+234-2-241-2626',
                'address' => 'PMB 5320, Oyo Road, Ibadan, Oyo State',
                'country' => 'NG',
                'region' => 'Oyo',
                'settings' => [
                    'research_focus' => ['crop_improvement', 'sustainable_agriculture'],
                    'international_collaboration' => true,
                ],
            ],
        ];

        foreach ($organizations as $orgData) {
            Organization::create($orgData);
        }
    }
}
