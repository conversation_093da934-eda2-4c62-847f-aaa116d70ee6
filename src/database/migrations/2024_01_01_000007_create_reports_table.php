<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->string('title');
            $table->enum('type', ['compliance', 'yield_forecast', 'field_analysis', 'disaster_impact', 'fraud_detection', 'custom']);
            $table->text('description')->nullable();
            $table->json('parameters')->nullable(); // Report generation parameters
            $table->json('data')->nullable(); // Report data/results
            $table->string('file_path')->nullable(); // Generated file path
            $table->enum('format', ['pdf', 'csv', 'excel', 'json'])->default('pdf');
            $table->enum('status', ['generating', 'completed', 'failed'])->default('generating');
            $table->timestamp('generated_at')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->index(['user_id', 'type']);
            $table->index(['type', 'status']);
            $table->index(['generated_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
