<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('satellite_data', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('field_id');
            $table->date('capture_date');
            $table->decimal('ndvi_value', 5, 3)->nullable(); // NDVI ranges from -1 to 1
            $table->decimal('precipitation', 8, 2)->nullable(); // mm
            $table->decimal('temperature', 5, 2)->nullable(); // Celsius
            $table->decimal('humidity', 5, 2)->nullable(); // Percentage
            $table->string('image_url')->nullable();
            $table->string('data_source')->default('sentinel-2'); // sentinel-2, landsat, etc.
            $table->json('analysis_data')->nullable(); // Additional analysis results
            $table->json('metadata')->nullable(); // Cloud cover, quality flags, etc.
            $table->timestamps();

            $table->foreign('field_id')->references('id')->on('fields')->onDelete('cascade');
            
            $table->unique(['field_id', 'capture_date', 'data_source']);
            $table->index(['field_id', 'capture_date']);
            $table->index(['capture_date', 'data_source']);
            $table->index(['ndvi_value']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('satellite_data');
    }
};
