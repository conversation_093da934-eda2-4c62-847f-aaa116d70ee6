<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subsidy_claims', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('field_id');
            $table->uuid('user_id');
            $table->string('claim_number')->unique();
            $table->decimal('amount', 12, 2);
            $table->string('subsidy_type'); // fertilizer, seeds, equipment, etc.
            $table->enum('status', ['pending', 'under_review', 'approved', 'rejected', 'flagged'])->default('pending');
            $table->text('description')->nullable();
            $table->json('verification_data')->nullable(); // Satellite verification results
            $table->json('documents')->nullable(); // Uploaded documents
            $table->decimal('fraud_score', 5, 2)->nullable(); // 0-100 fraud risk score
            $table->text('verification_notes')->nullable();
            $table->uuid('verified_by')->nullable(); // User who verified
            $table->timestamp('submitted_at');
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            $table->foreign('field_id')->references('id')->on('fields')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('verified_by')->references('id')->on('users')->onDelete('set null');
            
            $table->index(['user_id', 'status']);
            $table->index(['field_id', 'status']);
            $table->index(['status', 'submitted_at']);
            $table->index(['fraud_score']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subsidy_claims');
    }
};
