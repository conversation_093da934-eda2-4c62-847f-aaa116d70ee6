<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enable PostGIS extension
        DB::statement('CREATE EXTENSION IF NOT EXISTS postgis');

        Schema::create('fields', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('organization_id')->nullable();
            $table->string('name');
            $table->decimal('area_hectares', 10, 4);
            $table->string('crop_type');
            $table->date('planting_date')->nullable();
            $table->date('harvest_date')->nullable();
            $table->json('metadata')->nullable();
            $table->boolean('is_active')->default(true);
            $table->enum('verification_status', ['pending', 'verified', 'flagged', 'rejected'])->default('pending');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            
            $table->index(['user_id', 'is_active']);
            $table->index(['organization_id', 'crop_type']);
            $table->index(['crop_type', 'verification_status']);
        });

        // Add PostGIS geometry column for field boundaries
        DB::statement('ALTER TABLE fields ADD COLUMN boundary GEOMETRY(POLYGON, 4326)');
        
        // Create spatial index
        DB::statement('CREATE INDEX idx_fields_boundary ON fields USING GIST (boundary)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
