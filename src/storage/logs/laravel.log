[2025-06-30 11:33:03] local.ERROR: The /Users/<USER>/Projects/luminouslabsbd/agrovue/src/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Projects/luminouslabsbd/agrovue/src/bootstrap/cache directory must be present and writable. at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:179)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-06-30 11:33:26] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  database "agrovue_mvp" does not exist (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f473d57-0741-4f6a-8b72-c62f44f6fe67, {"command":"key:generate","exit_code":0,"arguments":{"command":"key:generate"},"options":{"show":false,"force":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:33:26, ?, command, 9f473d57-04ba-47fe-9def-86e895cf9d08)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f473d57-0741-4f6a-8b72-c62f44f6fe67, {\"command\":\"key:generate\",\"exit_code\":0,\"arguments\":{\"command\":\"key:generate\"},\"options\":{\"show\":false,\"force\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:33:26, ?, command, 9f473d57-04ba-47fe-9def-86e895cf9d08)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(219): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1240): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(565): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#16 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(219): Illuminate\\Foundation\\Application->terminate()
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1240): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}
"} 
[2025-06-30 11:39:03] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  database "agrovue_mvp" does not exist (Connection: pgsql, SQL: select * from "sessions" where "id" = tY1AcawwAFAAVK0bVOGAgKPKvnd1Nx5a0Qua2ICn limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist (Connection: pgsql, SQL: select * from \"sessions\" where \"id\" = tY1AcawwAFAAVK0bVOGAgKPKvnd1Nx5a0Qua2ICn limit 1) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, false)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('tY1AcawwAFAAVK0...')
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('tY1AcawwAFAAVK0...')
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#51 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, false)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('tY1AcawwAFAAVK0...')
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('tY1AcawwAFAAVK0...')
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#60 {main}
"} 
[2025-06-30 11:39:03] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  database "agrovue_mvp" does not exist (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 9d5364e5fbe0608afd4fee23fb9c6f45) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 9d5364e5fbe0608afd4fee23fb9c6f45) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#32 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#38 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#39 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#40 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#42 {main}
"} 
[2025-06-30 11:39:03] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: FATAL:  database "agrovue_mvp" does not exist (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f473f5a-60ad-4cc8-b470-6f725f392363, {"name":"errors::404","path":"\/vendor\/laravel\/framework\/src\/Illuminate\/Foundation\/Exceptions\/views\/404.blade.php","data":["exception"],"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:39:03, ?, view, 9f473f5a-5f32-4630-96d8-8d6f35883f90), (9f473f5a-60ad-4cc8-b470-6f725f392363, {"name":"errors::minimal","path":"\/vendor\/laravel\/framework\/src\/Illuminate\/Foundation\/Exceptions\/views\/minimal.blade.php","data":["exception"],"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:39:03, ?, view, 9f473f5a-6065-48ac-9c52-3a6b8ff9ad66), (9f473f5a-60ad-4cc8-b470-6f725f392363, {"ip_address":"127.0.0.1","uri":"\/favicon.ico","method":"GET","controller_action":null,"middleware":[],"headers":{"host":"localhost:8000","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","accept":"image\/avif,image\/webp,image\/apng,image\/svg+xml,image\/*,*\/*;q=0.8","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"image","referer":"http:\/\/localhost:8000\/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,bn;q=0.8,sl;q=0.7","cookie":"locale_lang=sl; cookie_consent=%7B%22essential%22%3Atrue%2C%22functionality%22%3Atrue%2C%22analytics%22%3Atrue%7D; __stripe_mid=6ed73331-30cc-400a-813a-024b7b0d5be798b9d1; frontend_lang=en_US; sidebar:state=true; authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiQmNDamVSNUJqU01lX1FNMUMxVTdJWDlwWXZGSTlpRTVxRFVLdzlLbzhNbG1haGpNb3VuMmc3QTZ4c1c1a2tXZzB0VVNyMEcxMnRwcm4yZVpJRHVNYmcifQ..jANmRVGRVF-6LpTtJD_bSg.HTdugEvcDpNBOLk-o3W3fcf1avkCLcT6GZdhvj1i0B8d8aUCVcZwjux1MqpQuBpLXZ5pRuN0lJbeG2XZlYGvBg7UKJbnTgVEuigHNrJNcpJQ6faKHrf1EYyDbK5WlaL0AKAhDWzKTxZioVGoFMpNzJUcHg_Mu3UldHKRmtV5YEZAYsJMPB1XLaeWpPl-hImIjBSys6Rnc9Da-9cv3ZW8KYeXr7ZRQBebyW7xuL5r3YwsVktpmb5vQg60nTowvYDnAZFJprU6BxEm6sMR85WyejlsHAVra9AW1FSC1rqcxUFTFUz4KdiJfPjHGr9UbJbp2RYfvpziWXhD8D202pO2MEMhCe6ndonsPnlYJ5k0ZDnItDQrM-MEdDzIzJxRB1ABQkdZMS6s3hc3gYkCYvyHjl1ERY0wmnKnT812z1Zl73YKfVMqa5mnjXG4oEnIlO2kfmHvVCYrf2D-8M-wpVp6gqWJe8ALrQFI-rE41ViukvzR77u72MECmlWLKefNNEBteaI0R1d0Yc37OUWOUkMC58nAjgakxFTm8jKjIbqoHgpDGydsG6cFzHJdNjH1r2LsQmpezLUDQbT0EwUV8JrjAMum23T8RliiS33ewbr6MQuY19APjbu5m_1ZvYTeVmpTNc7t0Uz0jNiL1vJ9bUFmrwvgBnbvMEhTYeTPzPU7ODtwvRxuL4ZlwIlGE30DO-nEec_-89PaKgqUBryMEzem-sbxeaegnCUKXago5jNPwVkuHRtGuLEnBHdb-EJbWitxJBhrfGLHM0Es_CdUYLXq7C0nTeLhzonbiGOjLElentI0uWmUB82-eH9CQP8tc8AG3MWUUPbPEvfND1Eg_7g3cmFa4EQdFRVPqpkkElaKax8anN4LbAGI7lZReywD-q_ZWwREzzANa3vCD6WTXcWFu2KtomJS_ejKKhYZGPSFaxROVdwjP50XaNUYpVJFUv5b7m2I3CLnewKXFplw3RTzIsB_-citaeglPM3k2lyzxcjC_Iji6wIVYowiXNil1bDv.X31ffPdwwyh8d_pRuVVpNfo8eOY8XyOnyRBpYjB_Msg; pma_lang=en; tz=Asia\/Dhaka; session_id=vmIhcnkdgT7bbTxDVZGzdrrsGear6yG6_BnJuMukO7VQ49xCjAm7L_NUHxzWcb6Z-ZCrirCgs4MsP0yju8ik; cids=1; phpMyAdmin=109a3626403686274a3ed76a7c4217b7"},"payload":[],"session":[],"response_headers":{"cache-control":"no-cache, private","date":"Mon, 30 Jun 2025 11:39:03 GMT","content-type":"text\/html; charset=UTF-8"},"response_status":404,"response":"HTML Response","duration":19,"memory":8,"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:39:03, ?, request, 9f473f5a-609e-4b3e-92ae-943cf559cd8c)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f473f5a-60ad-4cc8-b470-6f725f392363, {\"name\":\"errors::404\",\"path\":\"\\/vendor\\/laravel\\/framework\\/src\\/Illuminate\\/Foundation\\/Exceptions\\/views\\/404.blade.php\",\"data\":[\"exception\"],\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:39:03, ?, view, 9f473f5a-5f32-4630-96d8-8d6f35883f90), (9f473f5a-60ad-4cc8-b470-6f725f392363, {\"name\":\"errors::minimal\",\"path\":\"\\/vendor\\/laravel\\/framework\\/src\\/Illuminate\\/Foundation\\/Exceptions\\/views\\/minimal.blade.php\",\"data\":[\"exception\"],\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:39:03, ?, view, 9f473f5a-6065-48ac-9c52-3a6b8ff9ad66), (9f473f5a-60ad-4cc8-b470-6f725f392363, {\"ip_address\":\"127.0.0.1\",\"uri\":\"\\/favicon.ico\",\"method\":\"GET\",\"controller_action\":null,\"middleware\":[],\"headers\":{\"host\":\"localhost:8000\",\"connection\":\"keep-alive\",\"sec-ch-ua-platform\":\"\\\"macOS\\\"\",\"user-agent\":\"Mozilla\\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\"sec-ch-ua\":\"\\\"Google Chrome\\\";v=\\\"137\\\", \\\"Chromium\\\";v=\\\"137\\\", \\\"Not\\/A)Brand\\\";v=\\\"24\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"accept\":\"image\\/avif,image\\/webp,image\\/apng,image\\/svg+xml,image\\/*,*\\/*;q=0.8\",\"sec-fetch-site\":\"same-origin\",\"sec-fetch-mode\":\"no-cors\",\"sec-fetch-dest\":\"image\",\"referer\":\"http:\\/\\/localhost:8000\\/\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"accept-language\":\"en-US,en;q=0.9,bn;q=0.8,sl;q=0.7\",\"cookie\":\"locale_lang=sl; cookie_consent=%7B%22essential%22%3Atrue%2C%22functionality%22%3Atrue%2C%22analytics%22%3Atrue%7D; __stripe_mid=6ed73331-30cc-400a-813a-024b7b0d5be798b9d1; frontend_lang=en_US; sidebar:state=true; authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiQmNDamVSNUJqU01lX1FNMUMxVTdJWDlwWXZGSTlpRTVxRFVLdzlLbzhNbG1haGpNb3VuMmc3QTZ4c1c1a2tXZzB0VVNyMEcxMnRwcm4yZVpJRHVNYmcifQ..jANmRVGRVF-6LpTtJD_bSg.HTdugEvcDpNBOLk-o3W3fcf1avkCLcT6GZdhvj1i0B8d8aUCVcZwjux1MqpQuBpLXZ5pRuN0lJbeG2XZlYGvBg7UKJbnTgVEuigHNrJNcpJQ6faKHrf1EYyDbK5WlaL0AKAhDWzKTxZioVGoFMpNzJUcHg_Mu3UldHKRmtV5YEZAYsJMPB1XLaeWpPl-hImIjBSys6Rnc9Da-9cv3ZW8KYeXr7ZRQBebyW7xuL5r3YwsVktpmb5vQg60nTowvYDnAZFJprU6BxEm6sMR85WyejlsHAVra9AW1FSC1rqcxUFTFUz4KdiJfPjHGr9UbJbp2RYfvpziWXhD8D202pO2MEMhCe6ndonsPnlYJ5k0ZDnItDQrM-MEdDzIzJxRB1ABQkdZMS6s3hc3gYkCYvyHjl1ERY0wmnKnT812z1Zl73YKfVMqa5mnjXG4oEnIlO2kfmHvVCYrf2D-8M-wpVp6gqWJe8ALrQFI-rE41ViukvzR77u72MECmlWLKefNNEBteaI0R1d0Yc37OUWOUkMC58nAjgakxFTm8jKjIbqoHgpDGydsG6cFzHJdNjH1r2LsQmpezLUDQbT0EwUV8JrjAMum23T8RliiS33ewbr6MQuY19APjbu5m_1ZvYTeVmpTNc7t0Uz0jNiL1vJ9bUFmrwvgBnbvMEhTYeTPzPU7ODtwvRxuL4ZlwIlGE30DO-nEec_-89PaKgqUBryMEzem-sbxeaegnCUKXago5jNPwVkuHRtGuLEnBHdb-EJbWitxJBhrfGLHM0Es_CdUYLXq7C0nTeLhzonbiGOjLElentI0uWmUB82-eH9CQP8tc8AG3MWUUPbPEvfND1Eg_7g3cmFa4EQdFRVPqpkkElaKax8anN4LbAGI7lZReywD-q_ZWwREzzANa3vCD6WTXcWFu2KtomJS_ejKKhYZGPSFaxROVdwjP50XaNUYpVJFUv5b7m2I3CLnewKXFplw3RTzIsB_-citaeglPM3k2lyzxcjC_Iji6wIVYowiXNil1bDv.X31ffPdwwyh8d_pRuVVpNfo8eOY8XyOnyRBpYjB_Msg; pma_lang=en; tz=Asia\\/Dhaka; session_id=vmIhcnkdgT7bbTxDVZGzdrrsGear6yG6_BnJuMukO7VQ49xCjAm7L_NUHxzWcb6Z-ZCrirCgs4MsP0yju8ik; cids=1; phpMyAdmin=109a3626403686274a3ed76a7c4217b7\"},\"payload\":[],\"session\":[],\"response_headers\":{\"cache-control\":\"no-cache, private\",\"date\":\"Mon, 30 Jun 2025 11:39:03 GMT\",\"content-type\":\"text\\/html; charset=UTF-8\"},\"response_status\":404,\"response\":\"HTML Response\",\"duration\":19,\"memory\":8,\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:39:03, ?, request, 9f473f5a-609e-4b3e-92ae-943cf559cd8c)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#22 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: FATAL:  database \"agrovue_mvp\" does not exist at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(565): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#16 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#30 {main}
"} 
[2025-06-30 11:39:52] local.ERROR: SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation "notifications_notifiable_type_notifiable_id_index" already exists (Connection: pgsql, SQL: create index "notifications_notifiable_type_notifiable_id_index" on "notifications" ("notifiable_type", "notifiable_id")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation \"notifications_notifiable_type_notifiable_id_index\" already exists (Connection: pgsql, SQL: create index \"notifications_notifiable_type_notifiable_id_index\" on \"notifications\" (\"notifiable_type\", \"notifiable_id\")) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create index \"n...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('create index \"n...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement('create index \"n...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('notifications', Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/database/migrations/2024_01_01_000008_create_notifications_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call(Array)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}

[previous exception] [object] (PDOException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation \"notifications_notifiable_type_notifiable_id_index\" already exists at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:571)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(571): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create index \"n...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create index \"n...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('create index \"n...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement('create index \"n...')
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('notifications', Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/database/migrations/2024_01_01_000008_create_notifications_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/Pr...', 1, false)
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call(Array)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#37 {main}
"} 
[2025-06-30 11:39:52] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: select count(*) as aggregate from "telescope_entries" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 9d5364e5fbe0608afd4fee23fb9c6f45) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 9d5364e5fbe0608afd4fee23fb9c6f45) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 74)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(219): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1240): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:412)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(412): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 74)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(219): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1240): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/artisan(11): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-06-30 11:40:13] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: insert into "telescope_entries" ("batch_id", "content", "cre...
                    ^ (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f473fc3-eef0-4788-85bd-cdc982ae3026, {"connection":"pgsql","bindings":[],"sql":"select * from \"sessions\" where \"id\" = 'JJWGXqV7sqV25nTJ2gSjMu2HcC0sehXwIEGtcvaW' limit 1","time":"17.62","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/public\/index.php","line":17,"hash":"9f575eff1fa5f641bf92d3e1f8304182","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:40:12, ?, query, 9f473fc3-ea2c-4651-829d-215f171a2eb2), (9f473fc3-eef0-4788-85bd-cdc982ae3026, {"connection":"pgsql","bindings":[],"sql":"select * from \"sessions\" where \"id\" = 'JJWGXqV7sqV25nTJ2gSjMu2HcC0sehXwIEGtcvaW' limit 1","time":"1.01","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/public\/index.php","line":17,"hash":"9f575eff1fa5f641bf92d3e1f8304182","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:40:12, ?, query, 9f473fc3-ed4f-4534-9dc7-d449a54acd85), (9f473fc3-eef0-4788-85bd-cdc982ae3026, {"connection":"pgsql","bindings":[],"sql":"insert into \"sessions\" (\"payload\", \"last_activity\", \"user_id\", \"ip_address\", \"user_agent\", \"id\") values ('YTozOntzOjY6Il90b2tlbiI7czo0MDoiUVJqTTdpS25NaHlXN0VaenlDU0NVR0VtOFV3NlNIaUZJSElPS3VMUSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1751283612, null, '127.0.0.1', 'Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36', 'JJWGXqV7sqV25nTJ2gSjMu2HcC0sehXwIEGtcvaW')","time":"1.67","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/public\/index.php","line":17,"hash":"2ed1688a00afcd151ba7ccd3c7c6764e","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:40:12, ?, query, 9f473fc3-ee1d-4227-ad49-087fa15dcfd9), (9f473fc3-eef0-4788-85bd-cdc982ae3026, {"ip_address":"127.0.0.1","uri":"\/","method":"GET","controller_action":"Closure","middleware":["web"],"headers":{"host":"localhost:8000","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,bn;q=0.8,sl;q=0.7","cookie":"locale_lang=sl; cookie_consent=%7B%22essential%22%3Atrue%2C%22functionality%22%3Atrue%2C%22analytics%22%3Atrue%7D; __stripe_mid=6ed73331-30cc-400a-813a-024b7b0d5be798b9d1; frontend_lang=en_US; sidebar:state=true; authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiQmNDamVSNUJqU01lX1FNMUMxVTdJWDlwWXZGSTlpRTVxRFVLdzlLbzhNbG1haGpNb3VuMmc3QTZ4c1c1a2tXZzB0VVNyMEcxMnRwcm4yZVpJRHVNYmcifQ..jANmRVGRVF-6LpTtJD_bSg.HTdugEvcDpNBOLk-o3W3fcf1avkCLcT6GZdhvj1i0B8d8aUCVcZwjux1MqpQuBpLXZ5pRuN0lJbeG2XZlYGvBg7UKJbnTgVEuigHNrJNcpJQ6faKHrf1EYyDbK5WlaL0AKAhDWzKTxZioVGoFMpNzJUcHg_Mu3UldHKRmtV5YEZAYsJMPB1XLaeWpPl-hImIjBSys6Rnc9Da-9cv3ZW8KYeXr7ZRQBebyW7xuL5r3YwsVktpmb5vQg60nTowvYDnAZFJprU6BxEm6sMR85WyejlsHAVra9AW1FSC1rqcxUFTFUz4KdiJfPjHGr9UbJbp2RYfvpziWXhD8D202pO2MEMhCe6ndonsPnlYJ5k0ZDnItDQrM-MEdDzIzJxRB1ABQkdZMS6s3hc3gYkCYvyHjl1ERY0wmnKnT812z1Zl73YKfVMqa5mnjXG4oEnIlO2kfmHvVCYrf2D-8M-wpVp6gqWJe8ALrQFI-rE41ViukvzR77u72MECmlWLKefNNEBteaI0R1d0Yc37OUWOUkMC58nAjgakxFTm8jKjIbqoHgpDGydsG6cFzHJdNjH1r2LsQmpezLUDQbT0EwUV8JrjAMum23T8RliiS33ewbr6MQuY19APjbu5m_1ZvYTeVmpTNc7t0Uz0jNiL1vJ9bUFmrwvgBnbvMEhTYeTPzPU7ODtwvRxuL4ZlwIlGE30DO-nEec_-89PaKgqUBryMEzem-sbxeaegnCUKXago5jNPwVkuHRtGuLEnBHdb-EJbWitxJBhrfGLHM0Es_CdUYLXq7C0nTeLhzonbiGOjLElentI0uWmUB82-eH9CQP8tc8AG3MWUUPbPEvfND1Eg_7g3cmFa4EQdFRVPqpkkElaKax8anN4LbAGI7lZReywD-q_ZWwREzzANa3vCD6WTXcWFu2KtomJS_ejKKhYZGPSFaxROVdwjP50XaNUYpVJFUv5b7m2I3CLnewKXFplw3RTzIsB_-citaeglPM3k2lyzxcjC_Iji6wIVYowiXNil1bDv.X31ffPdwwyh8d_pRuVVpNfo8eOY8XyOnyRBpYjB_Msg; pma_lang=en; tz=Asia\/Dhaka; session_id=vmIhcnkdgT7bbTxDVZGzdrrsGear6yG6_BnJuMukO7VQ49xCjAm7L_NUHxzWcb6Z-ZCrirCgs4MsP0yju8ik; cids=1; phpMyAdmin=109a3626403686274a3ed76a7c4217b7"},"payload":[],"session":{"_token":"QRjM7iKnMhyW7EZzyCSCUGEm8Uw6SHiFIHIOKuLQ","_previous":{"url":"http:\/\/localhost:8000"},"_flash":{"old":[],"new":[]}},"response_headers":{"cache-control":"no-cache, private","date":"Mon, 30 Jun 2025 11:40:12 GMT","location":"http:\/\/localhost:8000\/dashboard","content-type":"text\/html; charset=utf-8","vary":"X-Inertia","set-cookie":"XSRF-TOKEN=eyJpdiI6Im9WRnhySXEwOFBYcXdCWE1wNWxKUlE9PSIsInZhbHVlIjoiNWhhd3pxbTNLaXpKSndtUU9kRHVGNGJTRzAwSW9saFBObnZrbm9UT3FXVytGNlJPaFlGTzZmMXBaK3E4MVl2cDZWMXI4dmpyWktDZ0tDVXZBY0xJbGtnV0Nkcy9HT0lMWFhwSUFNOWpnb2xHY0hlQm9UdUlRZUVyaWcvQnVVZnciLCJtYWMiOiJmNTkzNGVmMDZjZWU3MzhhNThiMTVmNzVmYTdiMmVlNWM3ZWI3NDVkMzM4ZjIwZDQ4MzQ4MmM1N2QxNmEwMjJjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 13:40:12 GMT; Max-Age=7200; path=\/; samesite=lax, agrovue_mvp_session=eyJpdiI6InVKU2d4NXRGRTFiZ01xenZWQXRQaUE9PSIsInZhbHVlIjoiL2V1cUxWaktaS1A1d0ZWbDE0aG0veU51QVBmbWV0UngyajBYS2lrTEVpRGtLbjdxNUNPUHFERC9lUERjZW1Sdm9aZTg5d0V6OTFwSzBxMVpPWVBqUTF1bmtONzVsNkxqV0krUk9lVFVxUHBEZjBaL3k4YjRmdWVZRmNCZ2h2UEciLCJtYWMiOiJlODE1ZDY1Y2NiN2Y1YjRlNWU3ZmM3MWI0YzNiY2RlYjU3MmJlZWI4NDllYjQ3ZjFjNmQzODc5YWVlN2Q5N2EwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 13:40:12 GMT; Max-Age=7200; path=\/; httponly; samesite=lax"},"response_status":302,"response":"Redirected to http:\/\/localhost:8000\/dashboard","duration":59,"memory":6,"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 11:40:12, ?, request, 9f473fc3-eeba-4cb6-8b32-c0971484baf5)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"cre...
                    ^ (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f473fc3-eef0-4788-85bd-cdc982ae3026, {\"connection\":\"pgsql\",\"bindings\":[],\"sql\":\"select * from \\\"sessions\\\" where \\\"id\\\" = 'JJWGXqV7sqV25nTJ2gSjMu2HcC0sehXwIEGtcvaW' limit 1\",\"time\":\"17.62\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/public\\/index.php\",\"line\":17,\"hash\":\"9f575eff1fa5f641bf92d3e1f8304182\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:40:12, ?, query, 9f473fc3-ea2c-4651-829d-215f171a2eb2), (9f473fc3-eef0-4788-85bd-cdc982ae3026, {\"connection\":\"pgsql\",\"bindings\":[],\"sql\":\"select * from \\\"sessions\\\" where \\\"id\\\" = 'JJWGXqV7sqV25nTJ2gSjMu2HcC0sehXwIEGtcvaW' limit 1\",\"time\":\"1.01\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/public\\/index.php\",\"line\":17,\"hash\":\"9f575eff1fa5f641bf92d3e1f8304182\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:40:12, ?, query, 9f473fc3-ed4f-4534-9dc7-d449a54acd85), (9f473fc3-eef0-4788-85bd-cdc982ae3026, {\"connection\":\"pgsql\",\"bindings\":[],\"sql\":\"insert into \\\"sessions\\\" (\\\"payload\\\", \\\"last_activity\\\", \\\"user_id\\\", \\\"ip_address\\\", \\\"user_agent\\\", \\\"id\\\") values ('YTozOntzOjY6Il90b2tlbiI7czo0MDoiUVJqTTdpS25NaHlXN0VaenlDU0NVR0VtOFV3NlNIaUZJSElPS3VMUSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1751283612, null, '127.0.0.1', 'Mozilla\\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', 'JJWGXqV7sqV25nTJ2gSjMu2HcC0sehXwIEGtcvaW')\",\"time\":\"1.67\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/public\\/index.php\",\"line\":17,\"hash\":\"2ed1688a00afcd151ba7ccd3c7c6764e\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:40:12, ?, query, 9f473fc3-ee1d-4227-ad49-087fa15dcfd9), (9f473fc3-eef0-4788-85bd-cdc982ae3026, {\"ip_address\":\"127.0.0.1\",\"uri\":\"\\/\",\"method\":\"GET\",\"controller_action\":\"Closure\",\"middleware\":[\"web\"],\"headers\":{\"host\":\"localhost:8000\",\"connection\":\"keep-alive\",\"cache-control\":\"max-age=0\",\"sec-ch-ua\":\"\\\"Google Chrome\\\";v=\\\"137\\\", \\\"Chromium\\\";v=\\\"137\\\", \\\"Not\\/A)Brand\\\";v=\\\"24\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"macOS\\\"\",\"upgrade-insecure-requests\":\"1\",\"user-agent\":\"Mozilla\\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\"accept\":\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\",\"sec-fetch-site\":\"none\",\"sec-fetch-mode\":\"navigate\",\"sec-fetch-user\":\"?1\",\"sec-fetch-dest\":\"document\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"accept-language\":\"en-US,en;q=0.9,bn;q=0.8,sl;q=0.7\",\"cookie\":\"locale_lang=sl; cookie_consent=%7B%22essential%22%3Atrue%2C%22functionality%22%3Atrue%2C%22analytics%22%3Atrue%7D; __stripe_mid=6ed73331-30cc-400a-813a-024b7b0d5be798b9d1; frontend_lang=en_US; sidebar:state=true; authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiQmNDamVSNUJqU01lX1FNMUMxVTdJWDlwWXZGSTlpRTVxRFVLdzlLbzhNbG1haGpNb3VuMmc3QTZ4c1c1a2tXZzB0VVNyMEcxMnRwcm4yZVpJRHVNYmcifQ..jANmRVGRVF-6LpTtJD_bSg.HTdugEvcDpNBOLk-o3W3fcf1avkCLcT6GZdhvj1i0B8d8aUCVcZwjux1MqpQuBpLXZ5pRuN0lJbeG2XZlYGvBg7UKJbnTgVEuigHNrJNcpJQ6faKHrf1EYyDbK5WlaL0AKAhDWzKTxZioVGoFMpNzJUcHg_Mu3UldHKRmtV5YEZAYsJMPB1XLaeWpPl-hImIjBSys6Rnc9Da-9cv3ZW8KYeXr7ZRQBebyW7xuL5r3YwsVktpmb5vQg60nTowvYDnAZFJprU6BxEm6sMR85WyejlsHAVra9AW1FSC1rqcxUFTFUz4KdiJfPjHGr9UbJbp2RYfvpziWXhD8D202pO2MEMhCe6ndonsPnlYJ5k0ZDnItDQrM-MEdDzIzJxRB1ABQkdZMS6s3hc3gYkCYvyHjl1ERY0wmnKnT812z1Zl73YKfVMqa5mnjXG4oEnIlO2kfmHvVCYrf2D-8M-wpVp6gqWJe8ALrQFI-rE41ViukvzR77u72MECmlWLKefNNEBteaI0R1d0Yc37OUWOUkMC58nAjgakxFTm8jKjIbqoHgpDGydsG6cFzHJdNjH1r2LsQmpezLUDQbT0EwUV8JrjAMum23T8RliiS33ewbr6MQuY19APjbu5m_1ZvYTeVmpTNc7t0Uz0jNiL1vJ9bUFmrwvgBnbvMEhTYeTPzPU7ODtwvRxuL4ZlwIlGE30DO-nEec_-89PaKgqUBryMEzem-sbxeaegnCUKXago5jNPwVkuHRtGuLEnBHdb-EJbWitxJBhrfGLHM0Es_CdUYLXq7C0nTeLhzonbiGOjLElentI0uWmUB82-eH9CQP8tc8AG3MWUUPbPEvfND1Eg_7g3cmFa4EQdFRVPqpkkElaKax8anN4LbAGI7lZReywD-q_ZWwREzzANa3vCD6WTXcWFu2KtomJS_ejKKhYZGPSFaxROVdwjP50XaNUYpVJFUv5b7m2I3CLnewKXFplw3RTzIsB_-citaeglPM3k2lyzxcjC_Iji6wIVYowiXNil1bDv.X31ffPdwwyh8d_pRuVVpNfo8eOY8XyOnyRBpYjB_Msg; pma_lang=en; tz=Asia\\/Dhaka; session_id=vmIhcnkdgT7bbTxDVZGzdrrsGear6yG6_BnJuMukO7VQ49xCjAm7L_NUHxzWcb6Z-ZCrirCgs4MsP0yju8ik; cids=1; phpMyAdmin=109a3626403686274a3ed76a7c4217b7\"},\"payload\":[],\"session\":{\"_token\":\"QRjM7iKnMhyW7EZzyCSCUGEm8Uw6SHiFIHIOKuLQ\",\"_previous\":{\"url\":\"http:\\/\\/localhost:8000\"},\"_flash\":{\"old\":[],\"new\":[]}},\"response_headers\":{\"cache-control\":\"no-cache, private\",\"date\":\"Mon, 30 Jun 2025 11:40:12 GMT\",\"location\":\"http:\\/\\/localhost:8000\\/dashboard\",\"content-type\":\"text\\/html; charset=utf-8\",\"vary\":\"X-Inertia\",\"set-cookie\":\"XSRF-TOKEN=eyJpdiI6Im9WRnhySXEwOFBYcXdCWE1wNWxKUlE9PSIsInZhbHVlIjoiNWhhd3pxbTNLaXpKSndtUU9kRHVGNGJTRzAwSW9saFBObnZrbm9UT3FXVytGNlJPaFlGTzZmMXBaK3E4MVl2cDZWMXI4dmpyWktDZ0tDVXZBY0xJbGtnV0Nkcy9HT0lMWFhwSUFNOWpnb2xHY0hlQm9UdUlRZUVyaWcvQnVVZnciLCJtYWMiOiJmNTkzNGVmMDZjZWU3MzhhNThiMTVmNzVmYTdiMmVlNWM3ZWI3NDVkMzM4ZjIwZDQ4MzQ4MmM1N2QxNmEwMjJjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 13:40:12 GMT; Max-Age=7200; path=\\/; samesite=lax, agrovue_mvp_session=eyJpdiI6InVKU2d4NXRGRTFiZ01xenZWQXRQaUE9PSIsInZhbHVlIjoiL2V1cUxWaktaS1A1d0ZWbDE0aG0veU51QVBmbWV0UngyajBYS2lrTEVpRGtLbjdxNUNPUHFERC9lUERjZW1Sdm9aZTg5d0V6OTFwSzBxMVpPWVBqUTF1bmtONzVsNkxqV0krUk9lVFVxUHBEZjBaL3k4YjRmdWVZRmNCZ2h2UEciLCJtYWMiOiJlODE1ZDY1Y2NiN2Y1YjRlNWU3ZmM3MWI0YzNiY2RlYjU3MmJlZWI4NDllYjQ3ZjFjNmQzODc5YWVlN2Q5N2EwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 13:40:12 GMT; Max-Age=7200; path=\\/; httponly; samesite=lax\"},\"response_status\":302,\"response\":\"Redirected to http:\\/\\/localhost:8000\\/dashboard\",\"duration\":59,\"memory\":6,\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 11:40:12, ?, request, 9f473fc3-eeba-4cb6-8b32-c0971484baf5)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\RedirectResponse))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#22 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"cre...
                    ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:571)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(571): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#10 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\RedirectResponse))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#24 {main}
"} 
[2025-06-30 11:40:13] local.ERROR: Class "App\Http\Controllers\Controller" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\Controller\" not found at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/app/Http/Controllers/Dashboard/DashboardController.php:13)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(576): include()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(427): Composer\\Autoload\\{closure}('/Users/<USER>/Pr...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 11:40:13] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: select count(*) as aggregate from "telescope_entries" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 718c1a1569da51a216e082642eb51fe2) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 718c1a1569da51a216e082642eb51fe2) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#32 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:412)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(412): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 11:46:12] local.ERROR: Class "App\Http\Controllers\Controller" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\Controller\" not found at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/app/Http/Controllers/Dashboard/DashboardController.php:13)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(576): include()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(427): Composer\\Autoload\\{closure}('/Users/<USER>/Pr...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 11:46:13] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: select count(*) as aggregate from "telescope_entries" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 718c1a1569da51a216e082642eb51fe2) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 718c1a1569da51a216e082642eb51fe2) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#32 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:412)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(412): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 11:46:17] local.ERROR: Class "App\Http\Controllers\Controller" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\Controller\" not found at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/app/Http/Controllers/Dashboard/DashboardController.php:13)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(576): include()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(427): Composer\\Autoload\\{closure}('/Users/<USER>/Pr...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 11:46:18] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: select count(*) as aggregate from "telescope_entries" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 718c1a1569da51a216e082642eb51fe2) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 718c1a1569da51a216e082642eb51fe2) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#32 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:412)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(412): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 12:38:50] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: insert into "telescope_entries" ("batch_id", "content", "cre...
                    ^ (Connection: pgsql, SQL: insert into "telescope_entries" ("batch_id", "content", "created_at", "family_hash", "type", "uuid") values (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {"connection":"pgsql","bindings":[],"sql":"select * from \"sessions\" where \"id\" = '8uj1uwgqmI0XcrnTW9ZgnwtR5dc6dpejwLi9VJCG' limit 1","time":"15.53","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/public\/index.php","line":17,"hash":"9f575eff1fa5f641bf92d3e1f8304182","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:38:50, ?, query, 9f4754ba-fd74-419c-8975-421ac9d141d2), (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {"connection":"pgsql","bindings":[],"sql":"select * from \"sessions\" where \"id\" = '8uj1uwgqmI0XcrnTW9ZgnwtR5dc6dpejwLi9VJCG' limit 1","time":"0.51","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/public\/index.php","line":17,"hash":"9f575eff1fa5f641bf92d3e1f8304182","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:38:50, ?, query, 9f4754ba-febc-4407-aa9f-6213c0d96715), (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {"connection":"pgsql","bindings":[],"sql":"insert into \"sessions\" (\"payload\", \"last_activity\", \"user_id\", \"ip_address\", \"user_agent\", \"id\") values ('YTozOntzOjY6Il90b2tlbiI7czo0MDoiSjZBV09pd0N0Sk1aYzR0RXV2SnJnenFGV29NbjRybnRDOGZxZE5TVCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1751287130, null, '127.0.0.1', 'Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36', '8uj1uwgqmI0XcrnTW9ZgnwtR5dc6dpejwLi9VJCG')","time":"2.07","slow":false,"file":"\/Users\/<USER>\/Projects\/luminouslabsbd\/agrovue\/src\/public\/index.php","line":17,"hash":"2ed1688a00afcd151ba7ccd3c7c6764e","hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:38:50, ?, query, 9f4754ba-ffa1-49e1-95e3-d9658b4e377f), (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {"ip_address":"127.0.0.1","uri":"\/","method":"GET","controller_action":"Closure","middleware":["web"],"headers":{"host":"localhost:8000","connection":"keep-alive","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,bn;q=0.8,sl;q=0.7","cookie":"locale_lang=sl; cookie_consent=%7B%22essential%22%3Atrue%2C%22functionality%22%3Atrue%2C%22analytics%22%3Atrue%7D; __stripe_mid=6ed73331-30cc-400a-813a-024b7b0d5be798b9d1; frontend_lang=en_US; sidebar:state=true; authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiQmNDamVSNUJqU01lX1FNMUMxVTdJWDlwWXZGSTlpRTVxRFVLdzlLbzhNbG1haGpNb3VuMmc3QTZ4c1c1a2tXZzB0VVNyMEcxMnRwcm4yZVpJRHVNYmcifQ..jANmRVGRVF-6LpTtJD_bSg.HTdugEvcDpNBOLk-o3W3fcf1avkCLcT6GZdhvj1i0B8d8aUCVcZwjux1MqpQuBpLXZ5pRuN0lJbeG2XZlYGvBg7UKJbnTgVEuigHNrJNcpJQ6faKHrf1EYyDbK5WlaL0AKAhDWzKTxZioVGoFMpNzJUcHg_Mu3UldHKRmtV5YEZAYsJMPB1XLaeWpPl-hImIjBSys6Rnc9Da-9cv3ZW8KYeXr7ZRQBebyW7xuL5r3YwsVktpmb5vQg60nTowvYDnAZFJprU6BxEm6sMR85WyejlsHAVra9AW1FSC1rqcxUFTFUz4KdiJfPjHGr9UbJbp2RYfvpziWXhD8D202pO2MEMhCe6ndonsPnlYJ5k0ZDnItDQrM-MEdDzIzJxRB1ABQkdZMS6s3hc3gYkCYvyHjl1ERY0wmnKnT812z1Zl73YKfVMqa5mnjXG4oEnIlO2kfmHvVCYrf2D-8M-wpVp6gqWJe8ALrQFI-rE41ViukvzR77u72MECmlWLKefNNEBteaI0R1d0Yc37OUWOUkMC58nAjgakxFTm8jKjIbqoHgpDGydsG6cFzHJdNjH1r2LsQmpezLUDQbT0EwUV8JrjAMum23T8RliiS33ewbr6MQuY19APjbu5m_1ZvYTeVmpTNc7t0Uz0jNiL1vJ9bUFmrwvgBnbvMEhTYeTPzPU7ODtwvRxuL4ZlwIlGE30DO-nEec_-89PaKgqUBryMEzem-sbxeaegnCUKXago5jNPwVkuHRtGuLEnBHdb-EJbWitxJBhrfGLHM0Es_CdUYLXq7C0nTeLhzonbiGOjLElentI0uWmUB82-eH9CQP8tc8AG3MWUUPbPEvfND1Eg_7g3cmFa4EQdFRVPqpkkElaKax8anN4LbAGI7lZReywD-q_ZWwREzzANa3vCD6WTXcWFu2KtomJS_ejKKhYZGPSFaxROVdwjP50XaNUYpVJFUv5b7m2I3CLnewKXFplw3RTzIsB_-citaeglPM3k2lyzxcjC_Iji6wIVYowiXNil1bDv.X31ffPdwwyh8d_pRuVVpNfo8eOY8XyOnyRBpYjB_Msg; pma_lang=en; tz=Asia\/Dhaka; session_id=vmIhcnkdgT7bbTxDVZGzdrrsGear6yG6_BnJuMukO7VQ49xCjAm7L_NUHxzWcb6Z-ZCrirCgs4MsP0yju8ik; cids=1; phpMyAdmin=109a3626403686274a3ed76a7c4217b7; XSRF-TOKEN=eyJpdiI6Ii9JT1NlY1VzRElOTVAreENOUjdmY1E9PSIsInZhbHVlIjoiZUQ4OS85eFZBM29kZnRmaHBHRGNYZ2llUkZLbE5OZERhaFcvQWgwY3pzZmsrTVhNdEExYWFtSEpWdkVKQWlNd2dtZ0VZTmtnekFNUXc0REtMM3ZYMFZaZGN5c2MvV2ZFQVZQa1ZRRjVtVFlaUm5hcFI1TnBNbldCU2hZaHhvTHIiLCJtYWMiOiIxZWRjMTYwMGMwMTc3ZjUxZmUzYmU1MWNkZjU4ZmNmNjUyZWMwYTYxYjNjYjE3NWYzN2Y4ODEzMWFiNTU3MWVhIiwidGFnIjoiIn0%3D; agrovue_mvp_session=eyJpdiI6IjhBL1o1NVNTVTEwU2kxdHpSemZNRFE9PSIsInZhbHVlIjoiM1o3dWZBOWZxKzlITXdJeldlK1Q4aHYxSTJtaXJXc2JpSFltdkJhWSt2a1BCWEUvbXNJVnlQdXNJZTdWck5EUkFkeEZ1aTB1a1lGYTNNNFprT1JwczNjRURGU2xuclNoaFhFQjQ0TzFLc2N2aDRDdUpHMmtkK2MzcXZDSkZBYU8iLCJtYWMiOiJhNDRkYzIxNGMwMmM3OWZlMmQ3Y2RkODIyYjkxZTEzMTEzMWNhMzBkYzBkY2FmZWY3MWI0YTlkY2QxZTJlYWRjIiwidGFnIjoiIn0%3D"},"payload":[],"session":{"_token":"J6AWOiwCtJMZc4tEuvJrgzqFWoMn4rntC8fqdNST","_previous":{"url":"http:\/\/localhost:8000"},"_flash":{"old":[],"new":[]}},"response_headers":{"cache-control":"no-cache, private","date":"Mon, 30 Jun 2025 12:38:50 GMT","location":"http:\/\/localhost:8000\/dashboard","content-type":"text\/html; charset=utf-8","vary":"X-Inertia","set-cookie":"XSRF-TOKEN=eyJpdiI6IkdOQ3lQU3VpL1Z3QjB5Q094b3hBUUE9PSIsInZhbHVlIjoiMmh2bFdRRFZlaTJkeFdDaEpMSlNoUXJnb29QNnFqVjR1UU5rZjBMemVmVmZuNmdDclJnYTZXbGQvcTMzTjJKa3ozZFhSWTNTTmV4b01zUWhOaVJla0FtNVpLYUFpS1BGTjlROHlPcm5jYStVU3FHaVFaWWtud1dDaVJaTndFRkQiLCJtYWMiOiJjZTAyYWVkZTEyOTgyNzkzZGYwOTAzODFjMDRhMThmYzIzMzdiMTRiZGNjMjUxNWE0MjhmNzc4NzA1NDVjNTY2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 14:38:50 GMT; Max-Age=7200; path=\/; samesite=lax, agrovue_mvp_session=eyJpdiI6Ik9xMWNnd2JLekV5R0JBVzlMaTBYZWc9PSIsInZhbHVlIjoiaFpZUkM3elBDMDZKd1FPR1VsQjVaWnhady92UlMxZlBDZTFBSHlIaEcwcndlVHI4TllXdVpxcG9hOUg3RDlnY0VRZ3pDQmVFWlFSNGFkQVNUenB1QW1NQTl5ZWxlRGt2ank5Q2R2TGtDTm5EWTlNUkdvVlU2NDlqZ2tIemtxRE4iLCJtYWMiOiIzODZmNDYxMjU5MjEyOGM1ZTQ3M2E1YWQyOWE5YTdkNTE3Yzk1N2M3ZDc2ZWQ2NTkxZGYzYzIzZWU4YTFhMTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 14:38:50 GMT; Max-Age=7200; path=\/; httponly; samesite=lax"},"response_status":302,"response":"Redirected to http:\/\/localhost:8000\/dashboard","duration":57,"memory":6,"hostname":"Rakibs-MacBook-Pro.local"}, 2025-06-30 12:38:50, ?, request, 9f4754ba-ffcc-4544-9a74-7eaa8b810688)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"cre...
                    ^ (Connection: pgsql, SQL: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"created_at\", \"family_hash\", \"type\", \"uuid\") values (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {\"connection\":\"pgsql\",\"bindings\":[],\"sql\":\"select * from \\\"sessions\\\" where \\\"id\\\" = '8uj1uwgqmI0XcrnTW9ZgnwtR5dc6dpejwLi9VJCG' limit 1\",\"time\":\"15.53\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/public\\/index.php\",\"line\":17,\"hash\":\"9f575eff1fa5f641bf92d3e1f8304182\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:38:50, ?, query, 9f4754ba-fd74-419c-8975-421ac9d141d2), (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {\"connection\":\"pgsql\",\"bindings\":[],\"sql\":\"select * from \\\"sessions\\\" where \\\"id\\\" = '8uj1uwgqmI0XcrnTW9ZgnwtR5dc6dpejwLi9VJCG' limit 1\",\"time\":\"0.51\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/public\\/index.php\",\"line\":17,\"hash\":\"9f575eff1fa5f641bf92d3e1f8304182\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:38:50, ?, query, 9f4754ba-febc-4407-aa9f-6213c0d96715), (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {\"connection\":\"pgsql\",\"bindings\":[],\"sql\":\"insert into \\\"sessions\\\" (\\\"payload\\\", \\\"last_activity\\\", \\\"user_id\\\", \\\"ip_address\\\", \\\"user_agent\\\", \\\"id\\\") values ('YTozOntzOjY6Il90b2tlbiI7czo0MDoiSjZBV09pd0N0Sk1aYzR0RXV2SnJnenFGV29NbjRybnRDOGZxZE5TVCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1751287130, null, '127.0.0.1', 'Mozilla\\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '8uj1uwgqmI0XcrnTW9ZgnwtR5dc6dpejwLi9VJCG')\",\"time\":\"2.07\",\"slow\":false,\"file\":\"\\/Users\\/<USER>\\/Projects\\/luminouslabsbd\\/agrovue\\/src\\/public\\/index.php\",\"line\":17,\"hash\":\"2ed1688a00afcd151ba7ccd3c7c6764e\",\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:38:50, ?, query, 9f4754ba-ffa1-49e1-95e3-d9658b4e377f), (9f4754ba-fffb-43f1-9c18-6c1a3725684a, {\"ip_address\":\"127.0.0.1\",\"uri\":\"\\/\",\"method\":\"GET\",\"controller_action\":\"Closure\",\"middleware\":[\"web\"],\"headers\":{\"host\":\"localhost:8000\",\"connection\":\"keep-alive\",\"sec-ch-ua\":\"\\\"Google Chrome\\\";v=\\\"137\\\", \\\"Chromium\\\";v=\\\"137\\\", \\\"Not\\/A)Brand\\\";v=\\\"24\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"macOS\\\"\",\"upgrade-insecure-requests\":\"1\",\"user-agent\":\"Mozilla\\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\"accept\":\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\",\"sec-fetch-site\":\"none\",\"sec-fetch-mode\":\"navigate\",\"sec-fetch-user\":\"?1\",\"sec-fetch-dest\":\"document\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"accept-language\":\"en-US,en;q=0.9,bn;q=0.8,sl;q=0.7\",\"cookie\":\"locale_lang=sl; cookie_consent=%7B%22essential%22%3Atrue%2C%22functionality%22%3Atrue%2C%22analytics%22%3Atrue%7D; __stripe_mid=6ed73331-30cc-400a-813a-024b7b0d5be798b9d1; frontend_lang=en_US; sidebar:state=true; authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiQmNDamVSNUJqU01lX1FNMUMxVTdJWDlwWXZGSTlpRTVxRFVLdzlLbzhNbG1haGpNb3VuMmc3QTZ4c1c1a2tXZzB0VVNyMEcxMnRwcm4yZVpJRHVNYmcifQ..jANmRVGRVF-6LpTtJD_bSg.HTdugEvcDpNBOLk-o3W3fcf1avkCLcT6GZdhvj1i0B8d8aUCVcZwjux1MqpQuBpLXZ5pRuN0lJbeG2XZlYGvBg7UKJbnTgVEuigHNrJNcpJQ6faKHrf1EYyDbK5WlaL0AKAhDWzKTxZioVGoFMpNzJUcHg_Mu3UldHKRmtV5YEZAYsJMPB1XLaeWpPl-hImIjBSys6Rnc9Da-9cv3ZW8KYeXr7ZRQBebyW7xuL5r3YwsVktpmb5vQg60nTowvYDnAZFJprU6BxEm6sMR85WyejlsHAVra9AW1FSC1rqcxUFTFUz4KdiJfPjHGr9UbJbp2RYfvpziWXhD8D202pO2MEMhCe6ndonsPnlYJ5k0ZDnItDQrM-MEdDzIzJxRB1ABQkdZMS6s3hc3gYkCYvyHjl1ERY0wmnKnT812z1Zl73YKfVMqa5mnjXG4oEnIlO2kfmHvVCYrf2D-8M-wpVp6gqWJe8ALrQFI-rE41ViukvzR77u72MECmlWLKefNNEBteaI0R1d0Yc37OUWOUkMC58nAjgakxFTm8jKjIbqoHgpDGydsG6cFzHJdNjH1r2LsQmpezLUDQbT0EwUV8JrjAMum23T8RliiS33ewbr6MQuY19APjbu5m_1ZvYTeVmpTNc7t0Uz0jNiL1vJ9bUFmrwvgBnbvMEhTYeTPzPU7ODtwvRxuL4ZlwIlGE30DO-nEec_-89PaKgqUBryMEzem-sbxeaegnCUKXago5jNPwVkuHRtGuLEnBHdb-EJbWitxJBhrfGLHM0Es_CdUYLXq7C0nTeLhzonbiGOjLElentI0uWmUB82-eH9CQP8tc8AG3MWUUPbPEvfND1Eg_7g3cmFa4EQdFRVPqpkkElaKax8anN4LbAGI7lZReywD-q_ZWwREzzANa3vCD6WTXcWFu2KtomJS_ejKKhYZGPSFaxROVdwjP50XaNUYpVJFUv5b7m2I3CLnewKXFplw3RTzIsB_-citaeglPM3k2lyzxcjC_Iji6wIVYowiXNil1bDv.X31ffPdwwyh8d_pRuVVpNfo8eOY8XyOnyRBpYjB_Msg; pma_lang=en; tz=Asia\\/Dhaka; session_id=vmIhcnkdgT7bbTxDVZGzdrrsGear6yG6_BnJuMukO7VQ49xCjAm7L_NUHxzWcb6Z-ZCrirCgs4MsP0yju8ik; cids=1; phpMyAdmin=109a3626403686274a3ed76a7c4217b7; XSRF-TOKEN=eyJpdiI6Ii9JT1NlY1VzRElOTVAreENOUjdmY1E9PSIsInZhbHVlIjoiZUQ4OS85eFZBM29kZnRmaHBHRGNYZ2llUkZLbE5OZERhaFcvQWgwY3pzZmsrTVhNdEExYWFtSEpWdkVKQWlNd2dtZ0VZTmtnekFNUXc0REtMM3ZYMFZaZGN5c2MvV2ZFQVZQa1ZRRjVtVFlaUm5hcFI1TnBNbldCU2hZaHhvTHIiLCJtYWMiOiIxZWRjMTYwMGMwMTc3ZjUxZmUzYmU1MWNkZjU4ZmNmNjUyZWMwYTYxYjNjYjE3NWYzN2Y4ODEzMWFiNTU3MWVhIiwidGFnIjoiIn0%3D; agrovue_mvp_session=eyJpdiI6IjhBL1o1NVNTVTEwU2kxdHpSemZNRFE9PSIsInZhbHVlIjoiM1o3dWZBOWZxKzlITXdJeldlK1Q4aHYxSTJtaXJXc2JpSFltdkJhWSt2a1BCWEUvbXNJVnlQdXNJZTdWck5EUkFkeEZ1aTB1a1lGYTNNNFprT1JwczNjRURGU2xuclNoaFhFQjQ0TzFLc2N2aDRDdUpHMmtkK2MzcXZDSkZBYU8iLCJtYWMiOiJhNDRkYzIxNGMwMmM3OWZlMmQ3Y2RkODIyYjkxZTEzMTEzMWNhMzBkYzBkY2FmZWY3MWI0YTlkY2QxZTJlYWRjIiwidGFnIjoiIn0%3D\"},\"payload\":[],\"session\":{\"_token\":\"J6AWOiwCtJMZc4tEuvJrgzqFWoMn4rntC8fqdNST\",\"_previous\":{\"url\":\"http:\\/\\/localhost:8000\"},\"_flash\":{\"old\":[],\"new\":[]}},\"response_headers\":{\"cache-control\":\"no-cache, private\",\"date\":\"Mon, 30 Jun 2025 12:38:50 GMT\",\"location\":\"http:\\/\\/localhost:8000\\/dashboard\",\"content-type\":\"text\\/html; charset=utf-8\",\"vary\":\"X-Inertia\",\"set-cookie\":\"XSRF-TOKEN=eyJpdiI6IkdOQ3lQU3VpL1Z3QjB5Q094b3hBUUE9PSIsInZhbHVlIjoiMmh2bFdRRFZlaTJkeFdDaEpMSlNoUXJnb29QNnFqVjR1UU5rZjBMemVmVmZuNmdDclJnYTZXbGQvcTMzTjJKa3ozZFhSWTNTTmV4b01zUWhOaVJla0FtNVpLYUFpS1BGTjlROHlPcm5jYStVU3FHaVFaWWtud1dDaVJaTndFRkQiLCJtYWMiOiJjZTAyYWVkZTEyOTgyNzkzZGYwOTAzODFjMDRhMThmYzIzMzdiMTRiZGNjMjUxNWE0MjhmNzc4NzA1NDVjNTY2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 14:38:50 GMT; Max-Age=7200; path=\\/; samesite=lax, agrovue_mvp_session=eyJpdiI6Ik9xMWNnd2JLekV5R0JBVzlMaTBYZWc9PSIsInZhbHVlIjoiaFpZUkM3elBDMDZKd1FPR1VsQjVaWnhady92UlMxZlBDZTFBSHlIaEcwcndlVHI4TllXdVpxcG9hOUg3RDlnY0VRZ3pDQmVFWlFSNGFkQVNUenB1QW1NQTl5ZWxlRGt2ank5Q2R2TGtDTm5EWTlNUkdvVlU2NDlqZ2tIemtxRE4iLCJtYWMiOiIzODZmNDYxMjU5MjEyOGM1ZTQ3M2E1YWQyOWE5YTdkNTE3Yzk1N2M3ZDc2ZWQ2NTkxZGYzYzIzZWU4YTFhMTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 14:38:50 GMT; Max-Age=7200; path=\\/; httponly; samesite=lax\"},\"response_status\":302,\"response\":\"Redirected to http:\\/\\/localhost:8000\\/dashboard\",\"duration\":57,\"memory\":6,\"hostname\":\"Rakibs-MacBook-Pro.local\"}, 2025-06-30 12:38:50, ?, request, 9f4754ba-ffcc-4544-9a74-7eaa8b810688)) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#8 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\RedirectResponse))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#22 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: insert into \"telescope_entries\" (\"batch_id\", \"content\", \"cre...
                    ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:571)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(571): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"te...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"te...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run('insert into \"te...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(524): Illuminate\\Database\\Connection->statement('insert into \"te...', Array)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3717): Illuminate\\Database\\Connection->insert('insert into \"te...', Array)
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#10 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\RedirectResponse))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#24 {main}
"} 
[2025-06-30 12:38:50] local.ERROR: Class "App\Http\Controllers\Controller" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\Controller\" not found at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/app/Http/Controllers/Dashboard/DashboardController.php:13)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(576): include()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/composer/ClassLoader.php(427): Composer\\Autoload\\{closure}('/Users/<USER>/Pr...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
[2025-06-30 12:38:50] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "telescope_entries" does not exist
LINE 1: select count(*) as aggregate from "telescope_entries" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from "telescope_entries" where "type" = exception and "family_hash" = 718c1a1569da51a216e082642eb51fe2) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"telescope_entries\" where \"type\" = exception and \"family_hash\" = 718c1a1569da51a216e082642eb51fe2) at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#12 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#20 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#32 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"telescope_entries\" does not exist
LINE 1: select count(*) as aggregate from \"telescope_entries\" where ...
                                          ^ at /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php:412)
[stacktrace]
#0 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(412): PDOStatement->execute()
#1 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3603): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3531): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#13 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(609): array_map(Object(Closure), Array, Array)
#14 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(261): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Storage/DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(286): call_user_func(Object(Closure))
#22 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/telescope/src/ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1452): Illuminate\\Container\\Container->call(Object(Closure))
#30 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#32 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Projects/luminouslabsbd/agrovue/src/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Pr...')
#34 {main}
"} 
