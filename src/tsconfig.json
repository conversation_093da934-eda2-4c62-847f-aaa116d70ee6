{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["resources/js/*"],
      "@/components/*": ["resources/js/Components/*"],
      "@/pages/*": ["resources/js/Pages/*"],
      "@/types/*": ["resources/js/Types/*"],
      "@/utils/*": ["resources/js/Utils/*"],
      "@/hooks/*": ["resources/js/Hooks/*"],
      "@/stores/*": ["resources/js/Stores/*"]
    },

    /* Additional options */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": [
    "resources/js/**/*",
    "resources/js/**/*.ts",
    "resources/js/**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    "vendor",
    "storage",
    "bootstrap/cache"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
