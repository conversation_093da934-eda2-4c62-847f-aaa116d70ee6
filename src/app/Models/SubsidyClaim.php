<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class SubsidyClaim extends Model
{
    use HasFactory, HasUuids, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'field_id',
        'user_id',
        'claim_number',
        'amount',
        'subsidy_type',
        'status',
        'description',
        'verification_data',
        'documents',
        'fraud_score',
        'verification_notes',
        'verified_by',
        'submitted_at',
        'verified_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'verification_data' => 'array',
            'documents' => 'array',
            'fraud_score' => 'decimal:2',
            'submitted_at' => 'datetime',
            'verified_at' => 'datetime',
        ];
    }

    /**
     * Status enum
     */
    public const STATUS = [
        'pending' => 'Pending Review',
        'under_review' => 'Under Review',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'flagged' => 'Flagged for Investigation',
    ];

    /**
     * Subsidy types enum
     */
    public const SUBSIDY_TYPES = [
        'fertilizer' => 'Fertilizer Subsidy',
        'seeds' => 'Seeds Subsidy',
        'equipment' => 'Equipment Subsidy',
        'irrigation' => 'Irrigation Subsidy',
        'insurance' => 'Crop Insurance',
        'credit' => 'Agricultural Credit',
        'training' => 'Training Subsidy',
        'other' => 'Other',
    ];

    /**
     * Get the field this claim is for.
     */
    public function field()
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the user who submitted the claim.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who verified the claim.
     */
    public function verifier()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Check if claim is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if claim is under review.
     */
    public function isUnderReview(): bool
    {
        return $this->status === 'under_review';
    }

    /**
     * Check if claim is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if claim is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if claim is flagged.
     */
    public function isFlagged(): bool
    {
        return $this->status === 'flagged';
    }

    /**
     * Check if claim has high fraud risk.
     */
    public function hasHighFraudRisk(): bool
    {
        return $this->fraud_score !== null && $this->fraud_score > 70;
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayName(): string
    {
        return self::STATUS[$this->status] ?? $this->status;
    }

    /**
     * Get subsidy type display name.
     */
    public function getSubsidyTypeDisplayName(): string
    {
        return self::SUBSIDY_TYPES[$this->subsidy_type] ?? $this->subsidy_type;
    }

    /**
     * Get fraud risk level.
     */
    public function getFraudRiskLevel(): string
    {
        if ($this->fraud_score === null) {
            return 'unknown';
        }

        if ($this->fraud_score < 30) {
            return 'low';
        } elseif ($this->fraud_score < 70) {
            return 'medium';
        } else {
            return 'high';
        }
    }

    /**
     * Generate unique claim number.
     */
    public static function generateClaimNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        $sequence = str_pad(self::whereYear('created_at', $year)->count() + 1, 6, '0', STR_PAD_LEFT);
        
        return "CLM-{$year}{$month}-{$sequence}";
    }

    /**
     * Scope for pending claims.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for flagged claims.
     */
    public function scopeFlagged($query)
    {
        return $query->where('status', 'flagged');
    }

    /**
     * Scope for high fraud risk claims.
     */
    public function scopeHighFraudRisk($query)
    {
        return $query->where('fraud_score', '>', 70);
    }

    /**
     * Scope by subsidy type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('subsidy_type', $type);
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'fraud_score', 'verified_by', 'verification_notes'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
