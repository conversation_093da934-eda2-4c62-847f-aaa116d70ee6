<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Organization extends Model
{
    use HasFactory, HasUuids, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'contact_email',
        'contact_phone',
        'address',
        'country',
        'region',
        'settings',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Organization types enum
     */
    public const TYPES = [
        'government' => 'Government Agency',
        'ngo' => 'Non-Governmental Organization',
        'corporate' => 'Corporate/Private Company',
        'cooperative' => 'Farmers Cooperative',
        'bank' => 'Financial Institution',
        'research' => 'Research Institution',
    ];

    /**
     * Get the users that belong to the organization.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the fields managed by the organization.
     */
    public function fields()
    {
        return $this->hasMany(Field::class);
    }

    /**
     * Get the subsidy claims associated with the organization.
     */
    public function subsidyClaims()
    {
        return $this->hasManyThrough(SubsidyClaim::class, User::class);
    }

    /**
     * Get the reports generated by the organization.
     */
    public function reports()
    {
        return $this->hasManyThrough(Report::class, User::class);
    }

    /**
     * Check if organization is government agency.
     */
    public function isGovernment(): bool
    {
        return $this->type === 'government';
    }

    /**
     * Check if organization is NGO.
     */
    public function isNGO(): bool
    {
        return $this->type === 'ngo';
    }

    /**
     * Check if organization is corporate.
     */
    public function isCorporate(): bool
    {
        return $this->type === 'corporate';
    }

    /**
     * Check if organization is cooperative.
     */
    public function isCooperative(): bool
    {
        return $this->type === 'cooperative';
    }

    /**
     * Check if organization is bank.
     */
    public function isBank(): bool
    {
        return $this->type === 'bank';
    }

    /**
     * Get organization's settings.
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Update organization's settings.
     */
    public function updateSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['settings' => $settings]);
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'type', 'contact_email', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
