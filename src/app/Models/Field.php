<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Field extends Model
{
    use HasFactory, HasUuids, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'organization_id',
        'name',
        'boundary',
        'area_hectares',
        'crop_type',
        'planting_date',
        'harvest_date',
        'metadata',
        'is_active',
        'verification_status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'planting_date' => 'date',
            'harvest_date' => 'date',
            'metadata' => 'array',
            'is_active' => 'boolean',
            'area_hectares' => 'decimal:4',
        ];
    }

    /**
     * Crop types enum
     */
    public const CROP_TYPES = [
        'rice' => 'Rice',
        'maize' => 'Maize/Corn',
        'wheat' => 'Wheat',
        'cassava' => 'Cassava',
        'yam' => 'Yam',
        'millet' => 'Millet',
        'sorghum' => 'Sorghum',
        'beans' => 'Beans',
        'groundnut' => 'Groundnut',
        'cotton' => 'Cotton',
        'cocoa' => 'Cocoa',
        'palm_oil' => 'Palm Oil',
        'vegetables' => 'Vegetables',
        'fruits' => 'Fruits',
        'other' => 'Other',
    ];

    /**
     * Verification status enum
     */
    public const VERIFICATION_STATUS = [
        'pending' => 'Pending Verification',
        'verified' => 'Verified',
        'flagged' => 'Flagged for Review',
        'rejected' => 'Rejected',
    ];

    /**
     * Get the user that owns the field.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization that manages the field.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the satellite data for the field.
     */
    public function satelliteData()
    {
        return $this->hasMany(SatelliteData::class);
    }

    /**
     * Get the subsidy claims for the field.
     */
    public function subsidyClaims()
    {
        return $this->hasMany(SubsidyClaim::class);
    }

    /**
     * Get the alerts for the field.
     */
    public function alerts()
    {
        return $this->hasMany(Alert::class);
    }

    /**
     * Get the latest satellite data.
     */
    public function latestSatelliteData()
    {
        return $this->hasOne(SatelliteData::class)->latestOfMany('capture_date');
    }

    /**
     * Get the current NDVI value.
     */
    public function getCurrentNDVI(): ?float
    {
        return $this->latestSatelliteData?->ndvi_value;
    }

    /**
     * Get NDVI trend over the last 30 days.
     */
    public function getNDVITrend(int $days = 30): array
    {
        return $this->satelliteData()
            ->where('capture_date', '>=', now()->subDays($days))
            ->orderBy('capture_date')
            ->pluck('ndvi_value', 'capture_date')
            ->toArray();
    }

    /**
     * Check if field is under stress.
     */
    public function isUnderStress(): bool
    {
        $currentNDVI = $this->getCurrentNDVI();
        $threshold = $this->getMetadata('stress_threshold', 0.3);
        
        return $currentNDVI !== null && $currentNDVI < $threshold;
    }

    /**
     * Get field metadata value.
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Update field metadata.
     */
    public function updateMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->update(['metadata' => $metadata]);
    }

    /**
     * Get field boundary as GeoJSON.
     */
    public function getBoundaryGeoJSON(): array
    {
        if (!$this->boundary) {
            return [];
        }

        // Convert PostGIS geometry to GeoJSON
        // This would typically use a spatial library
        return json_decode($this->boundary, true);
    }

    /**
     * Calculate field area from boundary.
     */
    public function calculateArea(): float
    {
        // This would use PostGIS functions to calculate area
        // For now, return the stored area
        return $this->area_hectares;
    }

    /**
     * Get crop type display name.
     */
    public function getCropTypeDisplayName(): string
    {
        return self::CROP_TYPES[$this->crop_type] ?? $this->crop_type;
    }

    /**
     * Get verification status display name.
     */
    public function getVerificationStatusDisplayName(): string
    {
        return self::VERIFICATION_STATUS[$this->verification_status] ?? $this->verification_status;
    }

    /**
     * Scope for active fields.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for fields by crop type.
     */
    public function scopeByCropType($query, string $cropType)
    {
        return $query->where('crop_type', $cropType);
    }

    /**
     * Scope for fields within a bounding box.
     */
    public function scopeWithinBounds($query, array $bounds)
    {
        // This would use PostGIS spatial queries
        // For now, return the query as-is
        return $query;
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'crop_type', 'area_hectares', 'is_active', 'verification_status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
