<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Report extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'type',
        'description',
        'parameters',
        'data',
        'file_path',
        'format',
        'status',
        'generated_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'parameters' => 'array',
            'data' => 'array',
            'generated_at' => 'datetime',
        ];
    }

    /**
     * Report types enum
     */
    public const TYPES = [
        'compliance' => 'Compliance Report',
        'yield_forecast' => 'Yield Forecast',
        'field_analysis' => 'Field Analysis',
        'disaster_impact' => 'Disaster Impact Assessment',
        'fraud_detection' => 'Fraud Detection Report',
        'custom' => 'Custom Report',
    ];

    /**
     * Report formats enum
     */
    public const FORMATS = [
        'pdf' => 'PDF',
        'csv' => 'CSV',
        'excel' => 'Excel',
        'json' => 'JSON',
    ];

    /**
     * Report status enum
     */
    public const STATUS = [
        'generating' => 'Generating',
        'completed' => 'Completed',
        'failed' => 'Failed',
    ];

    /**
     * Get the user who generated the report.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if report is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if report generation failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if report is still generating.
     */
    public function isGenerating(): bool
    {
        return $this->status === 'generating';
    }

    /**
     * Get type display name.
     */
    public function getTypeDisplayName(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * Get format display name.
     */
    public function getFormatDisplayName(): string
    {
        return self::FORMATS[$this->format] ?? $this->format;
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayName(): string
    {
        return self::STATUS[$this->status] ?? $this->status;
    }

    /**
     * Get file download URL.
     */
    public function getDownloadUrl(): ?string
    {
        if (!$this->file_path || !$this->isCompleted()) {
            return null;
        }

        return route('reports.download', $this->id);
    }

    /**
     * Get file size in human readable format.
     */
    public function getFileSize(): ?string
    {
        if (!$this->file_path || !file_exists(storage_path('app/' . $this->file_path))) {
            return null;
        }

        $bytes = filesize(storage_path('app/' . $this->file_path));
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Mark report as completed.
     */
    public function markAsCompleted(string $filePath): void
    {
        $this->update([
            'status' => 'completed',
            'file_path' => $filePath,
            'generated_at' => now(),
        ]);
    }

    /**
     * Mark report as failed.
     */
    public function markAsFailed(): void
    {
        $this->update([
            'status' => 'failed',
            'generated_at' => now(),
        ]);
    }

    /**
     * Scope for completed reports.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed reports.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for generating reports.
     */
    public function scopeGenerating($query)
    {
        return $query->where('status', 'generating');
    }

    /**
     * Scope by report type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by format.
     */
    public function scopeByFormat($query, string $format)
    {
        return $query->where('format', $format);
    }
}
