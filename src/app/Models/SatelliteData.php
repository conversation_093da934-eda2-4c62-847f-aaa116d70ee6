<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SatelliteData extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'field_id',
        'capture_date',
        'ndvi_value',
        'precipitation',
        'temperature',
        'humidity',
        'image_url',
        'data_source',
        'analysis_data',
        'metadata',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'capture_date' => 'date',
            'ndvi_value' => 'decimal:3',
            'precipitation' => 'decimal:2',
            'temperature' => 'decimal:2',
            'humidity' => 'decimal:2',
            'analysis_data' => 'array',
            'metadata' => 'array',
        ];
    }

    /**
     * Data sources enum
     */
    public const DATA_SOURCES = [
        'sentinel-2' => 'Sentinel-2',
        'landsat-8' => 'Landsat 8',
        'landsat-9' => 'Landsat 9',
        'modis' => 'MODIS',
        'planet' => 'Planet Labs',
    ];

    /**
     * Get the field that this satellite data belongs to.
     */
    public function field()
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Check if NDVI indicates vegetation stress.
     */
    public function isStressed(float $threshold = 0.3): bool
    {
        return $this->ndvi_value !== null && $this->ndvi_value < $threshold;
    }

    /**
     * Get NDVI status based on value.
     */
    public function getNDVIStatus(): string
    {
        if ($this->ndvi_value === null) {
            return 'unknown';
        }

        if ($this->ndvi_value < 0.2) {
            return 'critical';
        } elseif ($this->ndvi_value < 0.4) {
            return 'stressed';
        } elseif ($this->ndvi_value < 0.6) {
            return 'moderate';
        } else {
            return 'healthy';
        }
    }

    /**
     * Get cloud cover percentage from metadata.
     */
    public function getCloudCover(): ?float
    {
        return data_get($this->metadata, 'cloud_cover');
    }

    /**
     * Check if data quality is acceptable.
     */
    public function isQualityAcceptable(): bool
    {
        $cloudCover = $this->getCloudCover();
        return $cloudCover === null || $cloudCover < 20; // Less than 20% cloud cover
    }

    /**
     * Get data source display name.
     */
    public function getDataSourceDisplayName(): string
    {
        return self::DATA_SOURCES[$this->data_source] ?? $this->data_source;
    }

    /**
     * Scope for recent data.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('capture_date', '>=', now()->subDays($days));
    }

    /**
     * Scope for high quality data.
     */
    public function scopeHighQuality($query)
    {
        return $query->whereRaw("(metadata->>'cloud_cover')::float < 20 OR metadata->>'cloud_cover' IS NULL");
    }

    /**
     * Scope by data source.
     */
    public function scopeBySource($query, string $source)
    {
        return $query->where('data_source', $source);
    }
}
