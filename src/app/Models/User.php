<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids, HasRoles, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'organization_id',
        'preferences',
        'email_verified_at',
        'phone_verified_at',
        'avatar_url',
        'last_login_at',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
            'preferences' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * User roles enum
     */
    public const ROLES = [
        'government' => 'Government',
        'ngo' => 'NGO',
        'corporate' => 'Corporate',
        'coop' => 'Cooperative',
        'bank' => 'Bank',
        'admin' => 'Administrator',
    ];

    /**
     * Get the organization that the user belongs to.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the fields owned by the user.
     */
    public function fields()
    {
        return $this->hasMany(Field::class);
    }

    /**
     * Get the subsidy claims submitted by the user.
     */
    public function subsidyClaims()
    {
        return $this->hasMany(SubsidyClaim::class);
    }

    /**
     * Get the reports generated by the user.
     */
    public function reports()
    {
        return $this->hasMany(Report::class);
    }

    /**
     * Get the notifications for the user.
     */
    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable');
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is government official.
     */
    public function isGovernment(): bool
    {
        return $this->hasRole('government');
    }

    /**
     * Check if user is NGO coordinator.
     */
    public function isNGO(): bool
    {
        return $this->hasRole('ngo');
    }

    /**
     * Check if user is corporate user.
     */
    public function isCorporate(): bool
    {
        return $this->hasRole('corporate');
    }

    /**
     * Check if user is cooperative member.
     */
    public function isCoop(): bool
    {
        return $this->hasRole('coop');
    }

    /**
     * Check if user is bank analyst.
     */
    public function isBank(): bool
    {
        return $this->hasRole('bank');
    }

    /**
     * Check if user is administrator.
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Get user's preferred language.
     */
    public function getPreferredLanguage(): string
    {
        return $this->preferences['language'] ?? 'en';
    }

    /**
     * Get user's notification preferences.
     */
    public function getNotificationPreferences(): array
    {
        return $this->preferences['notifications'] ?? [
            'email' => true,
            'sms' => true,
            'push' => true,
            'whatsapp' => false,
        ];
    }

    /**
     * Get user's dashboard preferences.
     */
    public function getDashboardPreferences(): array
    {
        return $this->preferences['dashboard'] ?? [
            'theme' => 'light',
            'widgets' => [],
            'layout' => 'default',
        ];
    }

    /**
     * Update user's last login timestamp.
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'role', 'organization_id', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'id';
    }
}
