<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Alert extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'field_id',
        'type',
        'severity',
        'title',
        'description',
        'metadata',
        'affected_area',
        'acknowledged',
        'acknowledged_by',
        'acknowledged_at',
        'resolved',
        'resolved_by',
        'resolved_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'affected_area' => 'array',
            'acknowledged' => 'boolean',
            'acknowledged_at' => 'datetime',
            'resolved' => 'boolean',
            'resolved_at' => 'datetime',
        ];
    }

    /**
     * Alert types enum
     */
    public const TYPES = [
        'drought' => 'Drought Warning',
        'flood' => 'Flood Warning',
        'pest' => 'Pest Outbreak',
        'disease' => 'Disease Outbreak',
        'stress' => 'Vegetation Stress',
        'fraud' => 'Fraud Detection',
        'weather' => 'Weather Alert',
    ];

    /**
     * Severity levels enum
     */
    public const SEVERITY_LEVELS = [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'critical' => 'Critical',
    ];

    /**
     * Get the field this alert is for.
     */
    public function field()
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the user who acknowledged the alert.
     */
    public function acknowledgedBy()
    {
        return $this->belongsTo(User::class, 'acknowledged_by');
    }

    /**
     * Get the user who resolved the alert.
     */
    public function resolvedBy()
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Check if alert is acknowledged.
     */
    public function isAcknowledged(): bool
    {
        return $this->acknowledged;
    }

    /**
     * Check if alert is resolved.
     */
    public function isResolved(): bool
    {
        return $this->resolved;
    }

    /**
     * Check if alert is critical.
     */
    public function isCritical(): bool
    {
        return $this->severity === 'critical';
    }

    /**
     * Check if alert is high priority.
     */
    public function isHighPriority(): bool
    {
        return in_array($this->severity, ['high', 'critical']);
    }

    /**
     * Get type display name.
     */
    public function getTypeDisplayName(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * Get severity display name.
     */
    public function getSeverityDisplayName(): string
    {
        return self::SEVERITY_LEVELS[$this->severity] ?? $this->severity;
    }

    /**
     * Get severity color for UI.
     */
    public function getSeverityColor(): string
    {
        return match ($this->severity) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray',
        };
    }

    /**
     * Acknowledge the alert.
     */
    public function acknowledge(User $user): void
    {
        $this->update([
            'acknowledged' => true,
            'acknowledged_by' => $user->id,
            'acknowledged_at' => now(),
        ]);
    }

    /**
     * Resolve the alert.
     */
    public function resolve(User $user): void
    {
        $this->update([
            'resolved' => true,
            'resolved_by' => $user->id,
            'resolved_at' => now(),
        ]);
    }

    /**
     * Scope for unacknowledged alerts.
     */
    public function scopeUnacknowledged($query)
    {
        return $query->where('acknowledged', false);
    }

    /**
     * Scope for unresolved alerts.
     */
    public function scopeUnresolved($query)
    {
        return $query->where('resolved', false);
    }

    /**
     * Scope for critical alerts.
     */
    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    /**
     * Scope for high priority alerts.
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('severity', ['high', 'critical']);
    }

    /**
     * Scope by alert type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by severity.
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope for recent alerts.
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
