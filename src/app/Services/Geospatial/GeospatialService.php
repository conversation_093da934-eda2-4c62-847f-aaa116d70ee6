<?php

namespace App\Services\Geospatial;

use App\Models\Field;
use Illuminate\Support\Facades\DB;

class GeospatialService
{
    /**
     * Calculate area of a polygon in hectares.
     */
    public function calculateArea(array $coordinates): float
    {
        // This would use PostGIS in a real implementation
        // For now, using a simplified calculation
        
        if (empty($coordinates) || !isset($coordinates[0])) {
            return 0;
        }

        $polygon = $coordinates[0];
        $area = 0;
        $n = count($polygon);

        for ($i = 0; $i < $n - 1; $i++) {
            $area += ($polygon[$i][0] * $polygon[$i + 1][1]) - ($polygon[$i + 1][0] * $polygon[$i][1]);
        }

        $area = abs($area) / 2;
        
        // Convert from square degrees to hectares (rough approximation)
        // This should use proper geodetic calculations in production
        return $area * 12321; // Very rough conversion factor
    }

    /**
     * Check if a point is within a polygon.
     */
    public function pointInPolygon(array $point, array $polygon): bool
    {
        $x = $point[0];
        $y = $point[1];
        $vertices = $polygon[0] ?? $polygon;
        
        $inside = false;
        $j = count($vertices) - 1;
        
        for ($i = 0; $i < count($vertices); $i++) {
            $xi = $vertices[$i][0];
            $yi = $vertices[$i][1];
            $xj = $vertices[$j][0];
            $yj = $vertices[$j][1];
            
            if ((($yi > $y) !== ($yj > $y)) && ($x < ($xj - $xi) * ($y - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
            $j = $i;
        }
        
        return $inside;
    }

    /**
     * Get fields within a bounding box.
     */
    public function getFieldsWithinBounds(float $north, float $south, float $east, float $west): \Illuminate\Database\Eloquent\Collection
    {
        // In a real implementation, this would use PostGIS spatial queries
        // For now, return all fields as a placeholder
        return Field::with(['user', 'organization', 'latestSatelliteData'])->get();
    }

    /**
     * Calculate distance between two points in kilometers.
     */
    public function calculateDistance(array $point1, array $point2): float
    {
        $lat1 = deg2rad($point1[1]);
        $lon1 = deg2rad($point1[0]);
        $lat2 = deg2rad($point2[1]);
        $lon2 = deg2rad($point2[0]);

        $deltaLat = $lat2 - $lat1;
        $deltaLon = $lon2 - $lon1;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1) * cos($lat2) *
             sin($deltaLon / 2) * sin($deltaLon / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        // Earth's radius in kilometers
        $earthRadius = 6371;
        
        return $earthRadius * $c;
    }

    /**
     * Get centroid of a polygon.
     */
    public function getCentroid(array $coordinates): array
    {
        if (empty($coordinates) || !isset($coordinates[0])) {
            return [0, 0];
        }

        $polygon = $coordinates[0];
        $x = 0;
        $y = 0;
        $count = count($polygon);

        foreach ($polygon as $point) {
            $x += $point[0];
            $y += $point[1];
        }

        return [$x / $count, $y / $count];
    }

    /**
     * Convert GeoJSON to WKT format.
     */
    public function geoJsonToWkt(array $geoJson): string
    {
        if ($geoJson['type'] !== 'Polygon') {
            throw new \InvalidArgumentException('Only Polygon type is supported');
        }

        $coordinates = $geoJson['coordinates'][0];
        $points = array_map(function ($point) {
            return $point[0] . ' ' . $point[1];
        }, $coordinates);

        return 'POLYGON((' . implode(', ', $points) . '))';
    }

    /**
     * Convert WKT to GeoJSON format.
     */
    public function wktToGeoJson(string $wkt): array
    {
        // Simple WKT parser for POLYGON
        if (!preg_match('/POLYGON\(\(([^)]+)\)\)/', $wkt, $matches)) {
            throw new \InvalidArgumentException('Invalid WKT format');
        }

        $pointsStr = $matches[1];
        $points = explode(', ', $pointsStr);
        
        $coordinates = array_map(function ($point) {
            $coords = explode(' ', trim($point));
            return [floatval($coords[0]), floatval($coords[1])];
        }, $points);

        return [
            'type' => 'Polygon',
            'coordinates' => [$coordinates]
        ];
    }

    /**
     * Simplify polygon coordinates to reduce complexity.
     */
    public function simplifyPolygon(array $coordinates, float $tolerance = 0.001): array
    {
        if (empty($coordinates) || !isset($coordinates[0])) {
            return $coordinates;
        }

        $polygon = $coordinates[0];
        $simplified = [$polygon[0]]; // Always keep first point
        
        for ($i = 1; $i < count($polygon) - 1; $i++) {
            $distance = $this->calculateDistance($polygon[$i], $simplified[count($simplified) - 1]);
            if ($distance > $tolerance) {
                $simplified[] = $polygon[$i];
            }
        }
        
        // Always keep last point
        $simplified[] = $polygon[count($polygon) - 1];
        
        return [$simplified];
    }

    /**
     * Validate GeoJSON polygon.
     */
    public function validatePolygon(array $geoJson): array
    {
        $errors = [];

        if (!isset($geoJson['type']) || $geoJson['type'] !== 'Polygon') {
            $errors[] = 'GeoJSON must be of type Polygon';
        }

        if (!isset($geoJson['coordinates']) || !is_array($geoJson['coordinates'])) {
            $errors[] = 'GeoJSON must have coordinates array';
        } else {
            $coordinates = $geoJson['coordinates'];
            
            if (empty($coordinates) || !isset($coordinates[0])) {
                $errors[] = 'Polygon must have at least one ring';
            } else {
                $ring = $coordinates[0];
                
                if (count($ring) < 4) {
                    $errors[] = 'Polygon ring must have at least 4 points';
                }
                
                // Check if polygon is closed
                $first = $ring[0];
                $last = $ring[count($ring) - 1];
                if ($first[0] !== $last[0] || $first[1] !== $last[1]) {
                    $errors[] = 'Polygon must be closed (first and last points must be the same)';
                }
                
                // Check for valid coordinates
                foreach ($ring as $point) {
                    if (!is_array($point) || count($point) < 2) {
                        $errors[] = 'Invalid coordinate point';
                        break;
                    }
                    
                    $lon = $point[0];
                    $lat = $point[1];
                    
                    if ($lon < -180 || $lon > 180) {
                        $errors[] = 'Longitude must be between -180 and 180';
                        break;
                    }
                    
                    if ($lat < -90 || $lat > 90) {
                        $errors[] = 'Latitude must be between -90 and 90';
                        break;
                    }
                }
            }
        }

        return $errors;
    }
}
