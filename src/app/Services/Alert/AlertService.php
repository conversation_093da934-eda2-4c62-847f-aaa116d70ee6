<?php

namespace App\Services\Alert;

use App\Models\Alert;
use App\Models\Field;
use App\Models\SatelliteData;
use App\Services\Notification\NotificationService;
use Illuminate\Support\Collection;

class AlertService
{
    public function __construct(
        private NotificationService $notificationService
    ) {}

    /**
     * Generate alerts based on satellite data analysis.
     */
    public function generateAlertsFromSatelliteData(SatelliteData $satelliteData): Collection
    {
        $alerts = collect();
        $field = $satelliteData->field;

        // Check for vegetation stress
        if ($this->isVegetationStressed($satelliteData)) {
            $alert = $this->createStressAlert($field, $satelliteData);
            $alerts->push($alert);
        }

        // Check for drought conditions
        if ($this->isDroughtCondition($satelliteData, $field)) {
            $alert = $this->createDroughtAlert($field, $satelliteData);
            $alerts->push($alert);
        }

        // Send notifications for new alerts
        $alerts->each(function ($alert) {
            $this->notificationService->sendAlertNotification($alert);
        });

        return $alerts;
    }

    /**
     * Check if vegetation is stressed based on NDVI.
     */
    private function isVegetationStressed(SatelliteData $satelliteData): bool
    {
        $ndvi = $satelliteData->ndvi_value;
        $stressThreshold = config('satellite.alerts.stress_threshold', 0.4);

        return $ndvi !== null && $ndvi < $stressThreshold;
    }

    /**
     * Check for drought conditions.
     */
    private function isDroughtCondition(SatelliteData $satelliteData, Field $field): bool
    {
        $ndvi = $satelliteData->ndvi_value;
        $droughtThreshold = config('satellite.alerts.drought_threshold', 0.3);
        $consecutiveDays = config('satellite.alerts.consecutive_days', 7);

        if ($ndvi === null || $ndvi >= $droughtThreshold) {
            return false;
        }

        // Check if NDVI has been below threshold for consecutive days
        $recentData = $field->satelliteData()
            ->where('capture_date', '>=', now()->subDays($consecutiveDays))
            ->where('ndvi_value', '<', $droughtThreshold)
            ->count();

        return $recentData >= $consecutiveDays;
    }

    /**
     * Create vegetation stress alert.
     */
    private function createStressAlert(Field $field, SatelliteData $satelliteData): Alert
    {
        $severity = $this->calculateStressSeverity($satelliteData->ndvi_value);

        return Alert::create([
            'field_id' => $field->id,
            'type' => 'stress',
            'severity' => $severity,
            'title' => "Vegetation Stress Detected - {$field->name}",
            'description' => "NDVI value of {$satelliteData->ndvi_value} indicates vegetation stress in {$field->name}. Immediate attention may be required.",
            'metadata' => [
                'ndvi_value' => $satelliteData->ndvi_value,
                'capture_date' => $satelliteData->capture_date,
                'data_source' => $satelliteData->data_source,
                'recommendations' => $this->getStressRecommendations($satelliteData->ndvi_value),
            ],
        ]);
    }

    /**
     * Create drought alert.
     */
    private function createDroughtAlert(Field $field, SatelliteData $satelliteData): Alert
    {
        return Alert::create([
            'field_id' => $field->id,
            'type' => 'drought',
            'severity' => 'high',
            'title' => "Drought Conditions Detected - {$field->name}",
            'description' => "Prolonged low NDVI values indicate drought conditions in {$field->name}. Immediate irrigation or intervention required.",
            'metadata' => [
                'ndvi_value' => $satelliteData->ndvi_value,
                'capture_date' => $satelliteData->capture_date,
                'data_source' => $satelliteData->data_source,
                'recommendations' => [
                    'Implement emergency irrigation',
                    'Check soil moisture levels',
                    'Consider drought-resistant crop varieties',
                    'Monitor weather forecasts',
                ],
            ],
        ]);
    }

    /**
     * Calculate stress severity based on NDVI value.
     */
    private function calculateStressSeverity(float $ndvi): string
    {
        if ($ndvi < 0.2) {
            return 'critical';
        } elseif ($ndvi < 0.3) {
            return 'high';
        } elseif ($ndvi < 0.4) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get recommendations based on NDVI value.
     */
    private function getStressRecommendations(float $ndvi): array
    {
        if ($ndvi < 0.2) {
            return [
                'Emergency irrigation required',
                'Check for pest or disease issues',
                'Soil nutrient analysis recommended',
                'Consider replanting if damage is severe',
            ];
        } elseif ($ndvi < 0.3) {
            return [
                'Increase irrigation frequency',
                'Monitor for pest activity',
                'Check soil moisture levels',
                'Apply appropriate fertilizers',
            ];
        } elseif ($ndvi < 0.4) {
            return [
                'Monitor vegetation health closely',
                'Ensure adequate water supply',
                'Check weather conditions',
                'Consider preventive measures',
            ];
        } else {
            return [
                'Continue regular monitoring',
                'Maintain current practices',
            ];
        }
    }

    /**
     * Generate fraud alert for suspicious subsidy claims.
     */
    public function generateFraudAlert(Field $field, array $fraudIndicators): Alert
    {
        $fraudScore = $this->calculateFraudScore($fraudIndicators);
        $severity = $fraudScore > 80 ? 'critical' : ($fraudScore > 60 ? 'high' : 'medium');

        return Alert::create([
            'field_id' => $field->id,
            'type' => 'fraud',
            'severity' => $severity,
            'title' => "Potential Fraud Detected - {$field->name}",
            'description' => "Suspicious activity detected in subsidy claims for {$field->name}. Manual verification required.",
            'metadata' => [
                'fraud_score' => $fraudScore,
                'indicators' => $fraudIndicators,
                'detection_date' => now(),
                'requires_investigation' => true,
            ],
        ]);
    }

    /**
     * Calculate fraud score based on indicators.
     */
    private function calculateFraudScore(array $indicators): float
    {
        $score = 0;
        $weights = [
            'ndvi_inconsistency' => 30,
            'area_mismatch' => 25,
            'temporal_anomalies' => 20,
            'historical_patterns' => 15,
            'document_issues' => 10,
        ];

        foreach ($indicators as $indicator => $value) {
            if (isset($weights[$indicator])) {
                $score += $weights[$indicator] * ($value ? 1 : 0);
            }
        }

        return min($score, 100);
    }

    /**
     * Generate weather-based alerts.
     */
    public function generateWeatherAlert(string $region, array $weatherData): Alert
    {
        $severity = $this->calculateWeatherSeverity($weatherData);

        return Alert::create([
            'field_id' => null, // Region-wide alert
            'type' => 'weather',
            'severity' => $severity,
            'title' => "Weather Alert - {$region}",
            'description' => $this->getWeatherDescription($weatherData),
            'metadata' => [
                'region' => $region,
                'weather_data' => $weatherData,
                'forecast_period' => '7 days',
            ],
            'affected_area' => $this->getRegionBounds($region),
        ]);
    }

    /**
     * Calculate weather alert severity.
     */
    private function calculateWeatherSeverity(array $weatherData): string
    {
        $temperature = $weatherData['temperature'] ?? 0;
        $precipitation = $weatherData['precipitation'] ?? 0;
        $windSpeed = $weatherData['wind_speed'] ?? 0;

        if ($temperature > 40 || $precipitation > 100 || $windSpeed > 60) {
            return 'critical';
        } elseif ($temperature > 35 || $precipitation > 50 || $windSpeed > 40) {
            return 'high';
        } elseif ($temperature > 30 || $precipitation > 25 || $windSpeed > 25) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get weather description based on data.
     */
    private function getWeatherDescription(array $weatherData): string
    {
        $conditions = [];

        if (($weatherData['temperature'] ?? 0) > 35) {
            $conditions[] = 'extreme heat';
        }

        if (($weatherData['precipitation'] ?? 0) > 50) {
            $conditions[] = 'heavy rainfall';
        }

        if (($weatherData['wind_speed'] ?? 0) > 40) {
            $conditions[] = 'strong winds';
        }

        if (empty($conditions)) {
            return 'Weather conditions may affect agricultural activities.';
        }

        return 'Severe weather conditions detected: ' . implode(', ', $conditions) . '. Take necessary precautions.';
    }

    /**
     * Get region bounds (placeholder).
     */
    private function getRegionBounds(string $region): array
    {
        // This would return actual region boundaries in a real implementation
        return [
            'type' => 'Polygon',
            'coordinates' => [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]
        ];
    }

    /**
     * Bulk acknowledge alerts.
     */
    public function bulkAcknowledge(array $alertIds, $user): int
    {
        $acknowledged = 0;

        Alert::whereIn('id', $alertIds)
            ->where('acknowledged', false)
            ->chunk(100, function ($alerts) use ($user, &$acknowledged) {
                foreach ($alerts as $alert) {
                    $alert->acknowledge($user);
                    $acknowledged++;
                }
            });

        return $acknowledged;
    }

    /**
     * Get alert statistics for dashboard.
     */
    public function getAlertStatistics($user): array
    {
        $query = Alert::query();

        // Apply user-specific filters
        if ($user->role !== 'admin') {
            if ($user->role === 'government') {
                if ($user->organization) {
                    $query->whereHas('field.organization', function ($q) use ($user) {
                        $q->where('region', $user->organization->region);
                    });
                }
            } elseif ($user->role === 'ngo') {
                $query->whereIn('type', ['drought', 'flood', 'weather']);
            } elseif ($user->role === 'bank') {
                $query->where(function ($q) {
                    $q->where('type', 'fraud')
                      ->orWhereHas('field.subsidyClaims');
                });
            } else {
                $query->whereHas('field', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
            }
        }

        return [
            'total' => $query->count(),
            'unacknowledged' => $query->where('acknowledged', false)->count(),
            'critical' => $query->where('severity', 'critical')->count(),
            'recent' => $query->where('created_at', '>=', now()->subDays(7))->count(),
            'by_type' => $query->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'by_severity' => $query->selectRaw('severity, COUNT(*) as count')
                ->groupBy('severity')
                ->pluck('count', 'severity')
                ->toArray(),
        ];
    }
}
