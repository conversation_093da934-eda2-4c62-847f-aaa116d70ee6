<?php

namespace App\Services\Notification;

use App\Models\Alert;
use App\Models\User;
use App\Models\SubsidyClaim;
use App\Models\Report;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send alert notification to relevant users.
     */
    public function sendAlertNotification(Alert $alert): void
    {
        $recipients = $this->getAlertRecipients($alert);

        foreach ($recipients as $user) {
            $this->sendNotificationToUser($user, 'alert', [
                'alert' => $alert,
                'field' => $alert->field,
            ]);
        }
    }

    /**
     * Send subsidy claim status notification.
     */
    public function sendClaimStatusNotification(SubsidyClaim $claim): void
    {
        $user = $claim->user;
        
        $this->sendNotificationToUser($user, 'claim_status', [
            'claim' => $claim,
            'field' => $claim->field,
        ]);
    }

    /**
     * Send report ready notification.
     */
    public function sendReportReadyNotification(Report $report): void
    {
        $user = $report->user;
        
        $this->sendNotificationToUser($user, 'report_ready', [
            'report' => $report,
        ]);
    }

    /**
     * Send notification to a specific user via their preferred channels.
     */
    public function sendNotificationToUser(User $user, string $type, array $data): void
    {
        $preferences = $user->getNotificationPreferences();
        $template = $this->getNotificationTemplate($type);

        // Send email notification
        if ($preferences['email'] && in_array('email', $template['channels'])) {
            $this->sendEmailNotification($user, $type, $data);
        }

        // Send SMS notification
        if ($preferences['sms'] && in_array('sms', $template['channels']) && $user->phone) {
            $this->sendSMSNotification($user, $type, $data);
        }

        // Send push notification
        if ($preferences['push'] && in_array('push', $template['channels'])) {
            $this->sendPushNotification($user, $type, $data);
        }

        // Send WhatsApp notification
        if ($preferences['whatsapp'] && in_array('whatsapp', $template['channels']) && $user->phone) {
            $this->sendWhatsAppNotification($user, $type, $data);
        }
    }

    /**
     * Send email notification.
     */
    private function sendEmailNotification(User $user, string $type, array $data): void
    {
        try {
            $template = $this->getNotificationTemplate($type);
            $subject = $this->parseTemplate($template['subject'], $data);
            $content = $this->generateEmailContent($type, $data);

            Mail::send('emails.notification', [
                'user' => $user,
                'content' => $content,
                'data' => $data,
            ], function ($message) use ($user, $subject) {
                $message->to($user->email, $user->name)
                        ->subject($subject);
            });

            $this->logNotification($user, 'email', $type, 'sent');
        } catch (\Exception $e) {
            Log::error('Failed to send email notification', [
                'user_id' => $user->id,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            
            $this->logNotification($user, 'email', $type, 'failed', $e->getMessage());
        }
    }

    /**
     * Send SMS notification.
     */
    private function sendSMSNotification(User $user, string $type, array $data): void
    {
        try {
            $message = $this->generateSMSContent($type, $data);
            
            // Use Twilio or other SMS service
            $this->sendSMS($user->phone, $message);
            
            $this->logNotification($user, 'sms', $type, 'sent');
        } catch (\Exception $e) {
            Log::error('Failed to send SMS notification', [
                'user_id' => $user->id,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            
            $this->logNotification($user, 'sms', $type, 'failed', $e->getMessage());
        }
    }

    /**
     * Send push notification.
     */
    private function sendPushNotification(User $user, string $type, array $data): void
    {
        try {
            $title = $this->generatePushTitle($type, $data);
            $body = $this->generatePushBody($type, $data);
            
            // Use Pusher or other push notification service
            $this->sendPush($user, $title, $body, $data);
            
            $this->logNotification($user, 'push', $type, 'sent');
        } catch (\Exception $e) {
            Log::error('Failed to send push notification', [
                'user_id' => $user->id,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            
            $this->logNotification($user, 'push', $type, 'failed', $e->getMessage());
        }
    }

    /**
     * Send WhatsApp notification.
     */
    private function sendWhatsAppNotification(User $user, string $type, array $data): void
    {
        try {
            $message = $this->generateWhatsAppContent($type, $data);
            
            // Use WhatsApp Business API
            $this->sendWhatsApp($user->phone, $message);
            
            $this->logNotification($user, 'whatsapp', $type, 'sent');
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp notification', [
                'user_id' => $user->id,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            
            $this->logNotification($user, 'whatsapp', $type, 'failed', $e->getMessage());
        }
    }

    /**
     * Get recipients for alert notifications.
     */
    private function getAlertRecipients(Alert $alert): \Illuminate\Database\Eloquent\Collection
    {
        $recipients = collect();

        if ($alert->field) {
            // Add field owner
            $recipients->push($alert->field->user);

            // Add organization members if applicable
            if ($alert->field->organization) {
                $orgUsers = User::where('organization_id', $alert->field->organization_id)
                    ->where('is_active', true)
                    ->get();
                $recipients = $recipients->merge($orgUsers);
            }
        }

        // Add government users for critical alerts
        if ($alert->severity === 'critical') {
            $govUsers = User::where('role', 'government')
                ->where('is_active', true)
                ->get();
            $recipients = $recipients->merge($govUsers);
        }

        // Add NGO users for disaster-related alerts
        if (in_array($alert->type, ['drought', 'flood', 'weather'])) {
            $ngoUsers = User::where('role', 'ngo')
                ->where('is_active', true)
                ->get();
            $recipients = $recipients->merge($ngoUsers);
        }

        return $recipients->unique('id');
    }

    /**
     * Get notification template configuration.
     */
    private function getNotificationTemplate(string $type): array
    {
        $templates = config('agrovue.notifications.templates', []);
        
        return $templates[$type] ?? [
            'subject' => 'AgroVue Notification',
            'channels' => ['email'],
        ];
    }

    /**
     * Parse template with data.
     */
    private function parseTemplate(string $template, array $data): string
    {
        $parsed = $template;
        
        foreach ($data as $key => $value) {
            if (is_object($value) && method_exists($value, 'toArray')) {
                $value = $value->toArray();
            }
            
            if (is_array($value)) {
                foreach ($value as $subKey => $subValue) {
                    $placeholder = '{' . $key . '_' . $subKey . '}';
                    $parsed = str_replace($placeholder, $subValue, $parsed);
                }
            } else {
                $placeholder = '{' . $key . '}';
                $parsed = str_replace($placeholder, $value, $parsed);
            }
        }
        
        return $parsed;
    }

    /**
     * Generate email content based on notification type.
     */
    private function generateEmailContent(string $type, array $data): string
    {
        switch ($type) {
            case 'alert':
                $alert = $data['alert'];
                return "Alert: {$alert->title}\n\n{$alert->description}\n\nSeverity: {$alert->severity}\nType: {$alert->type}";
                
            case 'claim_status':
                $claim = $data['claim'];
                return "Your subsidy claim {$claim->claim_number} status has been updated to: {$claim->status}";
                
            case 'report_ready':
                $report = $data['report'];
                return "Your report '{$report->title}' is ready for download.";
                
            default:
                return 'You have a new notification from AgroVue.';
        }
    }

    /**
     * Generate SMS content.
     */
    private function generateSMSContent(string $type, array $data): string
    {
        switch ($type) {
            case 'alert':
                $alert = $data['alert'];
                return "AgroVue Alert: {$alert->title} - {$alert->severity} severity. Check your dashboard for details.";
                
            case 'claim_status':
                $claim = $data['claim'];
                return "AgroVue: Claim {$claim->claim_number} status updated to {$claim->status}.";
                
            case 'report_ready':
                $report = $data['report'];
                return "AgroVue: Report '{$report->title}' is ready for download.";
                
            default:
                return 'New AgroVue notification. Check your dashboard.';
        }
    }

    /**
     * Generate push notification title.
     */
    private function generatePushTitle(string $type, array $data): string
    {
        switch ($type) {
            case 'alert':
                return 'AgroVue Alert';
            case 'claim_status':
                return 'Claim Status Update';
            case 'report_ready':
                return 'Report Ready';
            default:
                return 'AgroVue Notification';
        }
    }

    /**
     * Generate push notification body.
     */
    private function generatePushBody(string $type, array $data): string
    {
        return $this->generateSMSContent($type, $data);
    }

    /**
     * Generate WhatsApp content.
     */
    private function generateWhatsAppContent(string $type, array $data): string
    {
        return $this->generateSMSContent($type, $data);
    }

    /**
     * Send SMS via Twilio or other service.
     */
    private function sendSMS(string $phone, string $message): void
    {
        // Implement actual SMS sending logic here
        // This is a placeholder
        Log::info('SMS sent', ['phone' => $phone, 'message' => $message]);
    }

    /**
     * Send push notification.
     */
    private function sendPush(User $user, string $title, string $body, array $data): void
    {
        // Implement actual push notification logic here
        // This is a placeholder
        Log::info('Push notification sent', [
            'user_id' => $user->id,
            'title' => $title,
            'body' => $body,
        ]);
    }

    /**
     * Send WhatsApp message.
     */
    private function sendWhatsApp(string $phone, string $message): void
    {
        // Implement actual WhatsApp sending logic here
        // This is a placeholder
        Log::info('WhatsApp sent', ['phone' => $phone, 'message' => $message]);
    }

    /**
     * Log notification attempt.
     */
    private function logNotification(User $user, string $channel, string $type, string $status, ?string $error = null): void
    {
        // In a real implementation, this would save to notification_logs table
        Log::info('Notification logged', [
            'user_id' => $user->id,
            'channel' => $channel,
            'type' => $type,
            'status' => $status,
            'error' => $error,
        ]);
    }
}
