<?php

namespace App\Http\Requests\Field;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $field = $this->route('field');
        
        // Users can only update their own fields, unless they're government/admin
        return $this->user()->id === $field->user_id || 
               in_array($this->user()->role, ['government', 'admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'boundary' => 'sometimes|required|json',
            'area_hectares' => 'sometimes|required|numeric|min:0.01|max:10000',
            'crop_type' => 'sometimes|required|string|in:' . implode(',', array_keys(\App\Models\Field::CROP_TYPES)),
            'planting_date' => 'nullable|date',
            'harvest_date' => 'nullable|date|after:planting_date',
            'metadata' => 'nullable|array',
            'verification_status' => 'sometimes|string|in:' . implode(',', array_keys(\App\Models\Field::VERIFICATION_STATUS)),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Field name is required.',
            'boundary.required' => 'Field boundary is required.',
            'boundary.json' => 'Field boundary must be valid GeoJSON.',
            'area_hectares.required' => 'Field area is required.',
            'area_hectares.min' => 'Field area must be at least 0.01 hectares.',
            'area_hectares.max' => 'Field area cannot exceed 10,000 hectares.',
            'crop_type.required' => 'Crop type is required.',
            'crop_type.in' => 'Invalid crop type selected.',
            'harvest_date.after' => 'Harvest date must be after planting date.',
            'verification_status.in' => 'Invalid verification status.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Only allow government users to update verification status
        if ($this->has('verification_status') && $this->user()->role !== 'government') {
            $this->request->remove('verification_status');
        }

        // If boundary is provided as a string, ensure it's valid JSON
        if ($this->has('boundary') && is_string($this->boundary)) {
            $boundary = json_decode($this->boundary, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->merge(['boundary' => json_encode($boundary)]);
            }
        }
    }
}
