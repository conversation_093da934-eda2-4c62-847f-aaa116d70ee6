<?php

namespace App\Http\Requests\Field;

use Illuminate\Foundation\Http\FormRequest;

class CreateFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'boundary' => 'required|json', // GeoJSON polygon
            'area_hectares' => 'required|numeric|min:0.01|max:10000',
            'crop_type' => 'required|string|in:' . implode(',', array_keys(\App\Models\Field::CROP_TYPES)),
            'planting_date' => 'nullable|date',
            'harvest_date' => 'nullable|date|after:planting_date',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Field name is required.',
            'boundary.required' => 'Field boundary is required.',
            'boundary.json' => 'Field boundary must be valid GeoJSON.',
            'area_hectares.required' => 'Field area is required.',
            'area_hectares.min' => 'Field area must be at least 0.01 hectares.',
            'area_hectares.max' => 'Field area cannot exceed 10,000 hectares.',
            'crop_type.required' => 'Crop type is required.',
            'crop_type.in' => 'Invalid crop type selected.',
            'harvest_date.after' => 'Harvest date must be after planting date.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // If boundary is provided as a string, ensure it's valid JSON
        if ($this->has('boundary') && is_string($this->boundary)) {
            $boundary = json_decode($this->boundary, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->merge(['boundary' => json_encode($boundary)]);
            }
        }

        // Calculate area from boundary if not provided
        if ($this->has('boundary') && !$this->has('area_hectares')) {
            $area = $this->calculateAreaFromBoundary($this->boundary);
            if ($area > 0) {
                $this->merge(['area_hectares' => $area]);
            }
        }
    }

    /**
     * Calculate area from GeoJSON boundary.
     */
    private function calculateAreaFromBoundary(string $boundary): float
    {
        // This is a simplified calculation
        // In a real implementation, you would use PostGIS or a proper geospatial library
        try {
            $geojson = json_decode($boundary, true);
            if (isset($geojson['coordinates'][0])) {
                $coordinates = $geojson['coordinates'][0];
                // Simple polygon area calculation (not accurate for large areas)
                $area = 0;
                $n = count($coordinates);
                for ($i = 0; $i < $n - 1; $i++) {
                    $area += ($coordinates[$i][0] * $coordinates[$i + 1][1]) - ($coordinates[$i + 1][0] * $coordinates[$i][1]);
                }
                $area = abs($area) / 2;
                
                // Convert from square degrees to hectares (very rough approximation)
                // 1 degree ≈ 111 km at equator, so 1 square degree ≈ 12321 km² ≈ 1,232,100 hectares
                return $area * 1232100;
            }
        } catch (\Exception $e) {
            // Return 0 if calculation fails
        }
        
        return 0;
    }
}
