<?php

namespace App\Http\Controllers\Alert;

use App\Http\Controllers\Controller;
use App\Models\Alert;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AlertController extends Controller
{
    /**
     * Display a listing of alerts.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        $query = Alert::with(['field.user', 'field.organization', 'acknowledgedBy', 'resolvedBy'])
            ->when($user->role !== 'admin', function ($query) use ($user) {
                if ($user->role === 'government') {
                    // Government users can see all alerts in their region
                    if ($user->organization) {
                        $query->whereHas('field.organization', function ($q) use ($user) {
                            $q->where('region', $user->organization->region);
                        });
                    }
                } elseif ($user->role === 'ngo') {
                    // NGO users can see disaster-related alerts
                    $query->whereIn('type', ['drought', 'flood', 'weather']);
                } elseif ($user->role === 'bank') {
                    // Bank users can see fraud alerts and alerts for fields with claims
                    $query->where(function ($q) {
                        $q->where('type', 'fraud')
                          ->orWhereHas('field.subsidyClaims');
                    });
                } else {
                    // Other users can only see alerts for their fields
                    $query->whereHas('field', function ($q) use ($user) {
                        $q->where('user_id', $user->id);
                    });
                }
            });

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        if ($request->filled('severity')) {
            $query->where('severity', $request->input('severity'));
        }

        if ($request->filled('status')) {
            $status = $request->input('status');
            if ($status === 'unacknowledged') {
                $query->where('acknowledged', false);
            } elseif ($status === 'acknowledged') {
                $query->where('acknowledged', true)->where('resolved', false);
            } elseif ($status === 'resolved') {
                $query->where('resolved', true);
            }
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $alerts = $query->orderBy('created_at', 'desc')->paginate(20);

        return Inertia::render('Alerts/Index', [
            'alerts' => $alerts,
            'filters' => $request->only(['type', 'severity', 'status', 'search']),
            'alertTypes' => Alert::TYPES,
            'severityLevels' => Alert::SEVERITY_LEVELS,
        ]);
    }

    /**
     * Display the specified alert.
     */
    public function show(Alert $alert): Response
    {
        $alert->load([
            'field.user',
            'field.organization',
            'field.satelliteData' => function ($query) {
                $query->orderBy('capture_date', 'desc')->limit(10);
            },
            'acknowledgedBy',
            'resolvedBy'
        ]);

        return Inertia::render('Alerts/Show', [
            'alert' => $alert,
        ]);
    }

    /**
     * Acknowledge an alert.
     */
    public function acknowledge(Request $request, Alert $alert)
    {
        if ($alert->acknowledged) {
            return back()->with('error', 'Alert is already acknowledged.');
        }

        $alert->acknowledge($request->user());

        return back()->with('success', 'Alert acknowledged successfully.');
    }

    /**
     * Resolve an alert.
     */
    public function resolve(Request $request, Alert $alert)
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        if ($alert->resolved) {
            return back()->with('error', 'Alert is already resolved.');
        }

        // Acknowledge first if not already acknowledged
        if (!$alert->acknowledged) {
            $alert->acknowledge($request->user());
        }

        $alert->resolve($request->user());

        // Update metadata with resolution notes if provided
        if ($request->filled('resolution_notes')) {
            $metadata = $alert->metadata ?? [];
            $metadata['resolution_notes'] = $request->input('resolution_notes');
            $alert->update(['metadata' => $metadata]);
        }

        return back()->with('success', 'Alert resolved successfully.');
    }
}
