<?php

namespace App\Http\Controllers\Field;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Http\Requests\Field\CreateFieldRequest;
use App\Http\Requests\Field\UpdateFieldRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FieldController extends Controller
{
    /**
     * Display a listing of fields.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        $query = Field::with(['user', 'organization', 'latestSatelliteData'])
            ->when($user->role !== 'admin', function ($query) use ($user) {
                if ($user->role === 'government') {
                    // Government users can see fields in their region
                    if ($user->organization) {
                        $query->whereHas('organization', function ($q) use ($user) {
                            $q->where('region', $user->organization->region);
                        });
                    }
                } elseif ($user->role === 'bank') {
                    // Bank users can see fields with subsidy claims
                    $query->whereHas('subsidyClaims');
                } else {
                    // Other users can only see their own fields
                    $query->where('user_id', $user->id);
                }
            });

        // Apply filters
        if ($request->filled('crop_type')) {
            $query->where('crop_type', $request->input('crop_type'));
        }

        if ($request->filled('verification_status')) {
            $query->where('verification_status', $request->input('verification_status'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('crop_type', 'like', "%{$search}%");
            });
        }

        $fields = $query->orderBy('created_at', 'desc')->paginate(12);

        return Inertia::render('Fields/Index', [
            'fields' => $fields,
            'filters' => $request->only(['crop_type', 'verification_status', 'search']),
            'cropTypes' => Field::CROP_TYPES,
            'verificationStatuses' => Field::VERIFICATION_STATUS,
        ]);
    }

    /**
     * Show the form for creating a new field.
     */
    public function create(): Response
    {
        return Inertia::render('Fields/Create', [
            'cropTypes' => Field::CROP_TYPES,
        ]);
    }

    /**
     * Store a newly created field.
     */
    public function store(CreateFieldRequest $request)
    {
        $field = Field::create([
            'user_id' => $request->user()->id,
            'organization_id' => $request->user()->organization_id,
            'name' => $request->input('name'),
            'boundary' => $request->input('boundary'), // GeoJSON polygon
            'area_hectares' => $request->input('area_hectares'),
            'crop_type' => $request->input('crop_type'),
            'planting_date' => $request->input('planting_date'),
            'harvest_date' => $request->input('harvest_date'),
            'metadata' => $request->input('metadata', []),
        ]);

        return redirect()->route('fields.show', $field)
            ->with('success', 'Field created successfully.');
    }

    /**
     * Display the specified field.
     */
    public function show(Field $field): Response
    {
        $field->load([
            'user',
            'organization',
            'satelliteData' => function ($query) {
                $query->orderBy('capture_date', 'desc')->limit(30);
            },
            'alerts' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(10);
            },
            'subsidyClaims' => function ($query) {
                $query->orderBy('submitted_at', 'desc');
            }
        ]);

        // Calculate NDVI trend
        $ndviTrend = $field->satelliteData
            ->where('ndvi_value', '!=', null)
            ->map(function ($data) {
                return [
                    'date' => $data->capture_date->format('Y-m-d'),
                    'ndvi' => $data->ndvi_value,
                ];
            })
            ->values();

        return Inertia::render('Fields/Show', [
            'field' => $field,
            'ndviTrend' => $ndviTrend,
        ]);
    }

    /**
     * Show the form for editing the specified field.
     */
    public function edit(Field $field): Response
    {
        return Inertia::render('Fields/Edit', [
            'field' => $field,
            'cropTypes' => Field::CROP_TYPES,
        ]);
    }

    /**
     * Update the specified field.
     */
    public function update(UpdateFieldRequest $request, Field $field)
    {
        $field->update($request->validated());

        return redirect()->route('fields.show', $field)
            ->with('success', 'Field updated successfully.');
    }

    /**
     * Remove the specified field.
     */
    public function destroy(Field $field)
    {
        $field->delete();

        return redirect()->route('fields.index')
            ->with('success', 'Field deleted successfully.');
    }

    /**
     * Get satellite data for a field.
     */
    public function satelliteData(Field $field)
    {
        $satelliteData = $field->satelliteData()
            ->orderBy('capture_date', 'desc')
            ->paginate(50);

        return response()->json($satelliteData);
    }
}
