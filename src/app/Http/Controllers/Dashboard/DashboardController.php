<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Models\Alert;
use App\Models\SubsidyClaim;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        // Get user-specific data based on role and organization
        $fieldsQuery = Field::query();
        $alertsQuery = Alert::query();
        $claimsQuery = SubsidyClaim::query();
        
        // Filter data based on user role and organization
        if ($user->role === 'government') {
            // Government users can see all data in their region
            if ($user->organization) {
                $fieldsQuery->whereHas('organization', function ($query) use ($user) {
                    $query->where('region', $user->organization->region);
                });
            }
        } elseif ($user->role === 'ngo') {
            // NGO users can see data they have access to
            $fieldsQuery->whereHas('user.organization', function ($query) use ($user) {
                $query->where('type', 'cooperative');
            });
        } elseif ($user->role === 'bank') {
            // Bank users can see fields with subsidy claims
            $fieldsQuery->whereHas('subsidyClaims');
        } else {
            // Other users can only see their own fields
            $fieldsQuery->where('user_id', $user->id);
        }
        
        // Calculate statistics
        $totalFields = $fieldsQuery->count();
        $activeAlerts = $alertsQuery->where('acknowledged', false)->count();
        $pendingClaims = $claimsQuery->where('status', 'pending')->count();
        $verifiedFields = $fieldsQuery->where('verification_status', 'verified')->count();
        $totalArea = $fieldsQuery->sum('area_hectares');
        
        // Calculate average NDVI
        $avgNdvi = $fieldsQuery->whereHas('latestSatelliteData')
            ->with('latestSatelliteData')
            ->get()
            ->avg(function ($field) {
                return $field->latestSatelliteData?->ndvi_value;
            }) ?? 0;
        
        // Get recent activity
        $recentActivity = collect([
            [
                'id' => '1',
                'type' => 'field_created',
                'description' => 'New field "Rice Farm A" was added',
                'created_at' => now()->subHours(2)->toISOString(),
            ],
            [
                'id' => '2',
                'type' => 'alert_generated',
                'description' => 'Drought alert generated for Kano region',
                'created_at' => now()->subHours(4)->toISOString(),
            ],
            [
                'id' => '3',
                'type' => 'claim_verified',
                'description' => 'Subsidy claim CLM-202412-000123 verified',
                'created_at' => now()->subHours(6)->toISOString(),
            ],
        ]);
        
        $stats = [
            'total_fields' => $totalFields,
            'active_alerts' => $activeAlerts,
            'pending_claims' => $pendingClaims,
            'verified_fields' => $verifiedFields,
            'total_area' => $totalArea,
            'avg_ndvi' => $avgNdvi,
            'recent_activity' => $recentActivity,
        ];
        
        return Inertia::render('Dashboard/Index', [
            'stats' => $stats,
        ]);
    }
}
