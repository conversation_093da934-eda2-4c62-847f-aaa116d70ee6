<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Http\Requests\Field\CreateFieldRequest;
use App\Http\Requests\Field\UpdateFieldRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class FieldController extends Controller
{
    /**
     * Display a listing of fields.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $query = Field::with(['user', 'organization', 'latestSatelliteData'])
            ->when($user->role !== 'admin', function ($query) use ($user) {
                if ($user->role === 'government') {
                    if ($user->organization) {
                        $query->whereHas('organization', function ($q) use ($user) {
                            $q->where('region', $user->organization->region);
                        });
                    }
                } elseif ($user->role === 'bank') {
                    $query->whereHas('subsidyClaims');
                } else {
                    $query->where('user_id', $user->id);
                }
            });

        // Apply filters
        if ($request->filled('crop_type')) {
            $query->where('crop_type', $request->input('crop_type'));
        }

        if ($request->filled('verification_status')) {
            $query->where('verification_status', $request->input('verification_status'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('crop_type', 'like', "%{$search}%");
            });
        }

        $fields = $query->orderBy('created_at', 'desc')->paginate(
            $request->input('per_page', 15)
        );

        return response()->json($fields);
    }

    /**
     * Store a newly created field.
     */
    public function store(CreateFieldRequest $request): JsonResponse
    {
        $field = Field::create([
            'user_id' => $request->user()->id,
            'organization_id' => $request->user()->organization_id,
            'name' => $request->input('name'),
            'boundary' => $request->input('boundary'),
            'area_hectares' => $request->input('area_hectares'),
            'crop_type' => $request->input('crop_type'),
            'planting_date' => $request->input('planting_date'),
            'harvest_date' => $request->input('harvest_date'),
            'metadata' => $request->input('metadata', []),
        ]);

        $field->load(['user', 'organization']);

        return response()->json([
            'data' => $field,
            'message' => 'Field created successfully.',
        ], 201);
    }

    /**
     * Display the specified field.
     */
    public function show(Field $field): JsonResponse
    {
        $field->load([
            'user',
            'organization',
            'satelliteData' => function ($query) {
                $query->orderBy('capture_date', 'desc')->limit(30);
            },
            'alerts' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(10);
            },
            'subsidyClaims'
        ]);

        return response()->json(['data' => $field]);
    }

    /**
     * Update the specified field.
     */
    public function update(UpdateFieldRequest $request, Field $field): JsonResponse
    {
        $field->update($request->validated());
        $field->load(['user', 'organization']);

        return response()->json([
            'data' => $field,
            'message' => 'Field updated successfully.',
        ]);
    }

    /**
     * Remove the specified field.
     */
    public function destroy(Field $field): JsonResponse
    {
        $field->delete();

        return response()->json([
            'message' => 'Field deleted successfully.',
        ]);
    }

    /**
     * Get satellite data for a field.
     */
    public function satelliteData(Request $request, Field $field): JsonResponse
    {
        $query = $field->satelliteData()->orderBy('capture_date', 'desc');

        if ($request->filled('start_date')) {
            $query->where('capture_date', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('capture_date', '<=', $request->input('end_date'));
        }

        if ($request->filled('data_source')) {
            $query->where('data_source', $request->input('data_source'));
        }

        $satelliteData = $query->paginate($request->input('per_page', 50));

        return response()->json($satelliteData);
    }

    /**
     * Get NDVI analysis for a field.
     */
    public function ndviAnalysis(Field $field): JsonResponse
    {
        $currentNdvi = $field->getCurrentNDVI();
        $ndviTrend = $field->getNDVITrend(30);
        
        // Calculate trend direction
        $trendDirection = 'stable';
        if (count($ndviTrend) >= 2) {
            $recent = array_slice($ndviTrend, -5, 5, true);
            $older = array_slice($ndviTrend, -10, 5, true);
            
            $recentAvg = array_sum($recent) / count($recent);
            $olderAvg = array_sum($older) / count($older);
            
            if ($recentAvg > $olderAvg * 1.05) {
                $trendDirection = 'increasing';
            } elseif ($recentAvg < $olderAvg * 0.95) {
                $trendDirection = 'decreasing';
            }
        }

        // Determine stress level
        $stressLevel = 'low';
        if ($currentNdvi !== null) {
            if ($currentNdvi < 0.2) {
                $stressLevel = 'critical';
            } elseif ($currentNdvi < 0.4) {
                $stressLevel = 'high';
            } elseif ($currentNdvi < 0.6) {
                $stressLevel = 'medium';
            }
        }

        // Generate recommendations
        $recommendations = [];
        if ($stressLevel === 'critical') {
            $recommendations[] = 'Immediate irrigation required';
            $recommendations[] = 'Check for pest or disease issues';
            $recommendations[] = 'Consider soil nutrient analysis';
        } elseif ($stressLevel === 'high') {
            $recommendations[] = 'Increase irrigation frequency';
            $recommendations[] = 'Monitor for stress indicators';
        } elseif ($trendDirection === 'decreasing') {
            $recommendations[] = 'Monitor vegetation health closely';
            $recommendations[] = 'Check weather conditions';
        }

        return response()->json([
            'current_ndvi' => $currentNdvi,
            'trend' => $trendDirection,
            'stress_level' => $stressLevel,
            'recommendations' => $recommendations,
            'ndvi_history' => $ndviTrend,
        ]);
    }

    /**
     * Search fields.
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:2',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        $query = $request->input('q');
        $limit = $request->input('limit', 10);

        $fields = Field::where('name', 'like', "%{$query}%")
            ->orWhere('crop_type', 'like', "%{$query}%")
            ->with(['user', 'organization'])
            ->limit($limit)
            ->get();

        return response()->json(['data' => $fields]);
    }

    /**
     * Get fields within bounds.
     */
    public function withinBounds(Request $request): JsonResponse
    {
        $request->validate([
            'north' => 'required|numeric',
            'south' => 'required|numeric',
            'east' => 'required|numeric',
            'west' => 'required|numeric',
        ]);

        // In a real implementation, this would use PostGIS spatial queries
        // For now, we'll return all fields as a placeholder
        $fields = Field::with(['user', 'organization', 'latestSatelliteData'])
            ->get();

        return response()->json(['data' => $fields]);
    }

    /**
     * Bulk import fields.
     */
    public function bulkImport(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,json,geojson',
            'format' => 'required|string|in:csv,geojson',
        ]);

        // This would implement actual file parsing and field creation
        // For now, return a success message
        return response()->json([
            'message' => 'Bulk import initiated. Fields will be processed in the background.',
            'job_id' => 'bulk-import-' . time(),
        ]);
    }
}
