<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Handle API login request.
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'role' => 'nullable|string|in:government,ngo,corporate,coop,bank,admin',
        ]);

        $credentials = $request->only('email', 'password');
        
        if ($request->filled('role')) {
            $credentials['role'] = $request->input('role');
        }
        
        $credentials['is_active'] = true;

        if (!Auth::attempt($credentials)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = Auth::user();
        $user->updateLastLogin();

        $token = $user->createToken('api-token')->plainTextToken;

        return response()->json([
            'user' => $user->load('organization'),
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    /**
     * Handle API registration request.
     */
    public function register(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|string|in:government,ngo,corporate,coop,bank',
            'phone' => 'nullable|string|max:20',
            'organization_id' => 'nullable|exists:organizations,id',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'phone' => $request->phone,
            'organization_id' => $request->organization_id,
            'preferences' => [
                'language' => 'en',
                'notifications' => [
                    'email' => true,
                    'sms' => true,
                    'push' => true,
                    'whatsapp' => false,
                ],
                'dashboard' => [
                    'theme' => 'light',
                    'widgets' => [],
                    'layout' => 'default',
                ],
            ],
        ]);

        $token = $user->createToken('api-token')->plainTextToken;

        return response()->json([
            'user' => $user->load('organization'),
            'token' => $token,
            'token_type' => 'Bearer',
        ], 201);
    }

    /**
     * Handle API logout request.
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Successfully logged out',
        ]);
    }

    /**
     * Get authenticated user.
     */
    public function user(Request $request): JsonResponse
    {
        return response()->json([
            'user' => $request->user()->load('organization'),
        ]);
    }

    /**
     * Refresh token.
     */
    public function refresh(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->currentAccessToken()->delete();
        
        $token = $user->createToken('api-token')->plainTextToken;

        return response()->json([
            'user' => $user->load('organization'),
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    /**
     * Verify OTP for two-factor authentication.
     */
    public function verifyOtp(Request $request): JsonResponse
    {
        $request->validate([
            'phone' => 'required|string',
            'otp' => 'required|string|size:6',
        ]);

        // In a real implementation, you would verify the OTP against a stored value
        // For now, we'll accept any 6-digit code for demonstration
        $otp = $request->input('otp');
        
        if (!preg_match('/^\d{6}$/', $otp)) {
            return response()->json([
                'verified' => false,
                'message' => 'Invalid OTP format',
            ], 400);
        }

        // Find user by phone number
        $user = User::where('phone', $request->input('phone'))->first();
        
        if (!$user) {
            return response()->json([
                'verified' => false,
                'message' => 'User not found',
            ], 404);
        }

        // Mark phone as verified
        $user->update(['phone_verified_at' => now()]);

        $token = $user->createToken('api-token')->plainTextToken;

        return response()->json([
            'verified' => true,
            'user' => $user->load('organization'),
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }
}
