<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Alert;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AlertController extends Controller
{
    /**
     * Display a listing of alerts.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $query = Alert::with(['field.user', 'field.organization', 'acknowledgedBy', 'resolvedBy'])
            ->when($user->role !== 'admin', function ($query) use ($user) {
                if ($user->role === 'government') {
                    if ($user->organization) {
                        $query->whereHas('field.organization', function ($q) use ($user) {
                            $q->where('region', $user->organization->region);
                        });
                    }
                } elseif ($user->role === 'ngo') {
                    $query->whereIn('type', ['drought', 'flood', 'weather']);
                } elseif ($user->role === 'bank') {
                    $query->where(function ($q) {
                        $q->where('type', 'fraud')
                          ->orWhereHas('field.subsidyClaims');
                    });
                } else {
                    $query->whereHas('field', function ($q) use ($user) {
                        $q->where('user_id', $user->id);
                    });
                }
            });

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        if ($request->filled('severity')) {
            $query->where('severity', $request->input('severity'));
        }

        if ($request->filled('acknowledged')) {
            $query->where('acknowledged', $request->boolean('acknowledged'));
        }

        if ($request->filled('resolved')) {
            $query->where('resolved', $request->boolean('resolved'));
        }

        $alerts = $query->orderBy('created_at', 'desc')->paginate(
            $request->input('per_page', 20)
        );

        return response()->json($alerts);
    }

    /**
     * Display the specified alert.
     */
    public function show(Alert $alert): JsonResponse
    {
        $alert->load([
            'field.user',
            'field.organization',
            'field.satelliteData' => function ($query) {
                $query->orderBy('capture_date', 'desc')->limit(10);
            },
            'acknowledgedBy',
            'resolvedBy'
        ]);

        return response()->json(['data' => $alert]);
    }

    /**
     * Acknowledge an alert.
     */
    public function acknowledge(Request $request, Alert $alert): JsonResponse
    {
        if ($alert->acknowledged) {
            return response()->json([
                'message' => 'Alert is already acknowledged.',
            ], 400);
        }

        $alert->acknowledge($request->user());

        return response()->json([
            'data' => $alert->fresh(['acknowledgedBy']),
            'message' => 'Alert acknowledged successfully.',
        ]);
    }

    /**
     * Resolve an alert.
     */
    public function resolve(Request $request, Alert $alert): JsonResponse
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        if ($alert->resolved) {
            return response()->json([
                'message' => 'Alert is already resolved.',
            ], 400);
        }

        // Acknowledge first if not already acknowledged
        if (!$alert->acknowledged) {
            $alert->acknowledge($request->user());
        }

        $alert->resolve($request->user());

        // Update metadata with resolution notes if provided
        if ($request->filled('resolution_notes')) {
            $metadata = $alert->metadata ?? [];
            $metadata['resolution_notes'] = $request->input('resolution_notes');
            $alert->update(['metadata' => $metadata]);
        }

        return response()->json([
            'data' => $alert->fresh(['acknowledgedBy', 'resolvedBy']),
            'message' => 'Alert resolved successfully.',
        ]);
    }

    /**
     * Get dashboard alert summary.
     */
    public function dashboard(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $query = Alert::query()
            ->when($user->role !== 'admin', function ($query) use ($user) {
                if ($user->role === 'government') {
                    if ($user->organization) {
                        $query->whereHas('field.organization', function ($q) use ($user) {
                            $q->where('region', $user->organization->region);
                        });
                    }
                } elseif ($user->role === 'ngo') {
                    $query->whereIn('type', ['drought', 'flood', 'weather']);
                } elseif ($user->role === 'bank') {
                    $query->where(function ($q) {
                        $q->where('type', 'fraud')
                          ->orWhereHas('field.subsidyClaims');
                    });
                } else {
                    $query->whereHas('field', function ($q) use ($user) {
                        $q->where('user_id', $user->id);
                    });
                }
            });

        $totalAlerts = $query->count();
        $unacknowledged = $query->where('acknowledged', false)->count();
        $critical = $query->where('severity', 'critical')->count();
        $recentAlerts = $query->where('created_at', '>=', now()->subDays(7))->count();

        // Get alerts by type
        $alertsByType = $query->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Get alerts by severity
        $alertsBySeverity = $query->selectRaw('severity, COUNT(*) as count')
            ->groupBy('severity')
            ->pluck('count', 'severity')
            ->toArray();

        return response()->json([
            'summary' => [
                'total' => $totalAlerts,
                'unacknowledged' => $unacknowledged,
                'critical' => $critical,
                'recent' => $recentAlerts,
            ],
            'by_type' => $alertsByType,
            'by_severity' => $alertsBySeverity,
        ]);
    }

    /**
     * Get alerts by region.
     */
    public function byRegion(Request $request): JsonResponse
    {
        $request->validate([
            'region' => 'nullable|string',
            'bounds' => 'nullable|array',
            'bounds.north' => 'required_with:bounds|numeric',
            'bounds.south' => 'required_with:bounds|numeric',
            'bounds.east' => 'required_with:bounds|numeric',
            'bounds.west' => 'required_with:bounds|numeric',
        ]);

        $query = Alert::with(['field'])
            ->whereNotNull('field_id');

        if ($request->filled('region')) {
            $query->whereHas('field.organization', function ($q) use ($request) {
                $q->where('region', $request->input('region'));
            });
        }

        // In a real implementation, this would use PostGIS spatial queries for bounds
        if ($request->filled('bounds')) {
            // Placeholder for spatial filtering
        }

        $alerts = $query->get();

        return response()->json(['data' => $alerts]);
    }
}
