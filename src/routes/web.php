<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\Field\FieldController;
use App\Http\Controllers\Alert\AlertController;
use App\Http\Controllers\Subsidy\ClaimController;
use App\Http\Controllers\Report\ReportController;
use App\Http\Controllers\AI\ChatbotController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'show'])->name('login');
    Route::post('/login', [LoginController::class, 'store']);
    Route::get('/register', [RegisterController::class, 'show'])->name('register');
    Route::post('/register', [RegisterController::class, 'store']);
});

Route::post('/logout', [LoginController::class, 'destroy'])
    ->middleware('auth')
    ->name('logout');

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Fields
    Route::prefix('fields')->name('fields.')->group(function () {
        Route::get('/', [FieldController::class, 'index'])->name('index');
        Route::get('/create', [FieldController::class, 'create'])->name('create');
        Route::post('/', [FieldController::class, 'store'])->name('store');
        Route::get('/{field}', [FieldController::class, 'show'])->name('show');
        Route::get('/{field}/edit', [FieldController::class, 'edit'])->name('edit');
        Route::put('/{field}', [FieldController::class, 'update'])->name('update');
        Route::delete('/{field}', [FieldController::class, 'destroy'])->name('destroy');
        Route::get('/{field}/satellite-data', [FieldController::class, 'satelliteData'])->name('satellite-data');
    });
    
    // Alerts
    Route::prefix('alerts')->name('alerts.')->group(function () {
        Route::get('/', [AlertController::class, 'index'])->name('index');
        Route::get('/{alert}', [AlertController::class, 'show'])->name('show');
        Route::post('/{alert}/acknowledge', [AlertController::class, 'acknowledge'])->name('acknowledge');
        Route::post('/{alert}/resolve', [AlertController::class, 'resolve'])->name('resolve');
    });
    
    // Subsidy Claims (Government and Bank roles only)
    Route::middleware(['role:government,bank'])->prefix('subsidies')->name('subsidies.')->group(function () {
        Route::get('/', [ClaimController::class, 'index'])->name('index');
        Route::get('/create', [ClaimController::class, 'create'])->name('create');
        Route::post('/', [ClaimController::class, 'store'])->name('store');
        Route::get('/{claim}', [ClaimController::class, 'show'])->name('show');
        Route::post('/{claim}/verify', [ClaimController::class, 'verify'])->name('verify');
        Route::post('/{claim}/flag', [ClaimController::class, 'flag'])->name('flag');
        Route::post('/{claim}/approve', [ClaimController::class, 'approve'])->name('approve');
        Route::post('/{claim}/reject', [ClaimController::class, 'reject'])->name('reject');
    });
    
    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/create', [ReportController::class, 'create'])->name('create');
        Route::post('/', [ReportController::class, 'store'])->name('store');
        Route::get('/{report}', [ReportController::class, 'show'])->name('show');
        Route::get('/{report}/download', [ReportController::class, 'download'])->name('download');
    });
    
    // AI Advisory
    Route::prefix('ai-advisory')->name('ai-advisory.')->group(function () {
        Route::get('/', [ChatbotController::class, 'index'])->name('index');
        Route::post('/chat', [ChatbotController::class, 'chat'])->name('chat');
        Route::get('/recommendations/{field}', [ChatbotController::class, 'recommendations'])->name('recommendations');
    });
    
    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', function () {
            return Inertia::render('Settings/Index');
        })->name('index');
        
        Route::get('/profile', function () {
            return Inertia::render('Settings/Profile');
        })->name('profile');
        
        Route::get('/notifications', function () {
            return Inertia::render('Settings/Notifications');
        })->name('notifications');
        
        Route::get('/security', function () {
            return Inertia::render('Settings/Security');
        })->name('security');
    });
});

// Admin routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('Admin/Dashboard');
    })->name('dashboard');
    
    Route::get('/users', function () {
        return Inertia::render('Admin/Users');
    })->name('users');
    
    Route::get('/organizations', function () {
        return Inertia::render('Admin/Organizations');
    })->name('organizations');
    
    Route::get('/system', function () {
        return Inertia::render('Admin/System');
    })->name('system');
});
