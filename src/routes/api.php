<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\FieldController;
use App\Http\Controllers\Api\AlertController;
use App\Http\Controllers\Api\SubsidyController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\AIAdvisoryController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::post('/refresh', [AuthController::class, 'refresh'])->middleware('auth:sanctum');
    Route::post('/verify-otp', [AuthController::class, 'verifyOtp']);
    Route::get('/user', [AuthController::class, 'user'])->middleware('auth:sanctum');
});

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    
    // Fields API
    Route::apiResource('fields', FieldController::class);
    Route::get('fields/{field}/satellite-data', [FieldController::class, 'satelliteData']);
    Route::get('fields/{field}/ndvi-analysis', [FieldController::class, 'ndviAnalysis']);
    Route::post('fields/bulk-import', [FieldController::class, 'bulkImport']);
    Route::get('fields/search', [FieldController::class, 'search']);
    
    // Alerts API
    Route::apiResource('alerts', AlertController::class)->only(['index', 'show']);
    Route::post('alerts/{alert}/acknowledge', [AlertController::class, 'acknowledge']);
    Route::post('alerts/{alert}/resolve', [AlertController::class, 'resolve']);
    Route::get('alerts/dashboard', [AlertController::class, 'dashboard']);
    
    // Subsidy Claims API
    Route::apiResource('subsidy-claims', SubsidyController::class);
    Route::post('subsidy-claims/{claim}/verify', [SubsidyController::class, 'verify']);
    Route::get('subsidy-claims/{claim}/verification-report', [SubsidyController::class, 'verificationReport']);
    Route::get('subsidy-claims/fraud-detection', [SubsidyController::class, 'fraudDetection']);
    
    // Reports API
    Route::apiResource('reports', ReportController::class);
    Route::post('reports/generate', [ReportController::class, 'generate']);
    Route::get('reports/{report}/download', [ReportController::class, 'download']);
    Route::get('reports/templates', [ReportController::class, 'templates']);
    
    // AI Advisory API
    Route::prefix('ai')->group(function () {
        Route::post('/chat', [AIAdvisoryController::class, 'chat']);
        Route::get('/recommendations/{field}', [AIAdvisoryController::class, 'recommendations']);
        Route::get('/advisory/crop/{cropType}', [AIAdvisoryController::class, 'cropAdvisory']);
        Route::get('/advisory/weather/{location}', [AIAdvisoryController::class, 'weatherAdvisory']);
    });
    
    // Dashboard API
    Route::get('dashboard/stats', function (Request $request) {
        $user = $request->user();
        
        // Mock data for now - replace with actual queries
        return response()->json([
            'total_fields' => 150,
            'active_alerts' => 12,
            'pending_claims' => 8,
            'verified_fields' => 142,
            'total_area' => 2450.75,
            'avg_ndvi' => 0.654,
            'recent_activity' => [
                [
                    'id' => '1',
                    'type' => 'field_created',
                    'description' => 'New field "Rice Farm A" was added',
                    'created_at' => now()->subHours(2)->toISOString(),
                ],
                [
                    'id' => '2',
                    'type' => 'alert_generated',
                    'description' => 'Drought alert generated for Kano region',
                    'created_at' => now()->subHours(4)->toISOString(),
                ],
                [
                    'id' => '3',
                    'type' => 'claim_verified',
                    'description' => 'Subsidy claim CLM-202412-000123 verified',
                    'created_at' => now()->subHours(6)->toISOString(),
                ],
            ],
        ]);
    });
    
    // Geospatial API
    Route::prefix('geo')->group(function () {
        Route::get('/fields/within-bounds', [FieldController::class, 'withinBounds']);
        Route::get('/alerts/by-region', [AlertController::class, 'byRegion']);
        Route::post('/calculate-area', function (Request $request) {
            // Calculate area from GeoJSON polygon
            $polygon = $request->input('polygon');
            // This would use PostGIS functions in real implementation
            return response()->json(['area_hectares' => 10.5]);
        });
    });
    
    // Notifications API
    Route::prefix('notifications')->group(function () {
        Route::get('/', function (Request $request) {
            return $request->user()->notifications()->paginate(20);
        });
        Route::post('/{notification}/mark-read', function (Request $request, $notification) {
            $request->user()->notifications()->find($notification)->markAsRead();
            return response()->json(['success' => true]);
        });
        Route::post('/mark-all-read', function (Request $request) {
            $request->user()->unreadNotifications->markAsRead();
            return response()->json(['success' => true]);
        });
    });
    
    // File upload API
    Route::post('upload', function (Request $request) {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
        ]);
        
        $path = $request->file('file')->store('uploads', 'public');
        
        return response()->json([
            'path' => $path,
            'url' => asset('storage/' . $path),
        ]);
    });
});

// Public API routes (for external integrations)
Route::prefix('public')->group(function () {
    Route::get('/weather/{location}', function ($location) {
        // Mock weather data
        return response()->json([
            'location' => $location,
            'temperature' => 28.5,
            'humidity' => 65,
            'precipitation' => 0,
            'forecast' => [
                ['date' => now()->addDay()->toDateString(), 'temp' => 29, 'rain' => 0],
                ['date' => now()->addDays(2)->toDateString(), 'temp' => 27, 'rain' => 5],
                ['date' => now()->addDays(3)->toDateString(), 'temp' => 26, 'rain' => 12],
            ],
        ]);
    });
    
    Route::get('/crop-prices/{crop}', function ($crop) {
        // Mock crop price data
        return response()->json([
            'crop' => $crop,
            'current_price' => 250.00,
            'currency' => 'NGN',
            'unit' => 'per kg',
            'trend' => 'up',
            'change_percent' => 5.2,
        ]);
    });
});
