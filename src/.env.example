APP_NAME="AgroVue MVP"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=agrovue_mvp
DB_USERNAME=postgres
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=pusher
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Satellite Data APIs
SENTINEL_API_URL=https://scihub.copernicus.eu/dhus
SENTINEL_USERNAME=
SENTINEL_PASSWORD=

LANDSAT_API_URL=https://m2m.cr.usgs.gov/api/api/json/stable
LANDSAT_USERNAME=
LANDSAT_PASSWORD=

GOOGLE_EARTH_ENGINE_KEY=

# Weather APIs
OPENWEATHER_API_KEY=
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# SMS and Communication
TWILIO_SID=
TWILIO_TOKEN=
TWILIO_FROM=

WHATSAPP_TOKEN=
WHATSAPP_PHONE_NUMBER_ID=

# AI Services
OPENAI_API_KEY=
AI_MODEL=gpt-4

# Geospatial Services
MAPBOX_ACCESS_TOKEN=
GOOGLE_MAPS_API_KEY=

# Monitoring
SENTRY_LARAVEL_DSN=
TELESCOPE_ENABLED=true
