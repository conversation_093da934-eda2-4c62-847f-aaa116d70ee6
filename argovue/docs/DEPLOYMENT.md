# AgroVue Deployment Guide

This guide covers deploying AgroVue to production environments with best practices for security, performance, and scalability.

## Prerequisites

### Server Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **CPU**: 4+ cores (8+ recommended for high traffic)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB+ SSD storage
- **Network**: Stable internet connection with public IP

### Software Requirements
- **PHP**: 8.2+ with extensions (mbstring, xml, pdo, json, curl, gd, zip)
- **Database**: PostgreSQL 13+ or MySQL 8.0+
- **Web Server**: Nginx 1.18+ or Apache 2.4+
- **Cache**: Redis 6.0+
- **Node.js**: 18+ (for building assets)
- **SSL Certificate**: Let's Encrypt or commercial certificate

## Production Deployment

### 1. Server Setup

#### Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git unzip software-properties-common
```

#### Install PHP 8.2
```bash
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.2 php8.2-fpm php8.2-cli php8.2-common \
    php8.2-mysql php8.2-pgsql php8.2-xml php8.2-curl php8.2-gd \
    php8.2-mbstring php8.2-zip php8.2-bcmath php8.2-intl php8.2-redis
```

#### Install PostgreSQL
```bash
sudo apt install -y postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE agrovue;
CREATE USER agrovue WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE agrovue TO agrovue;
\q
```

#### Install Redis
```bash
sudo apt install -y redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Install Nginx
```bash
sudo apt install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### Install Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

#### Install Composer
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer
```

### 2. Application Deployment

#### Clone Repository
```bash
cd /var/www
sudo git clone https://github.com/your-org/agrovue.git
sudo chown -R www-data:www-data agrovue
cd agrovue
```

#### Install Dependencies
```bash
# PHP dependencies
composer install --no-dev --optimize-autoloader

# Node.js dependencies and build
npm ci
npm run build
```

#### Environment Configuration
```bash
cp .env.example .env
php artisan key:generate
```

Edit `.env` file:
```env
APP_NAME=AgroVue
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=agrovue
DB_USERNAME=agrovue
DB_PASSWORD=secure_password_here

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls

# Satellite Data APIs
SENTINEL_API_URL=https://scihub.copernicus.eu/dhus/search
SENTINEL_USERNAME=your-username
SENTINEL_PASSWORD=your-password

# File Storage (use S3 for production)
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=agrovue-production
```

#### Database Migration
```bash
php artisan migrate --force
php artisan db:seed --class=AgroVueSeeder
```

#### Optimize Application
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

#### Set Permissions
```bash
sudo chown -R www-data:www-data /var/www/agrovue
sudo chmod -R 755 /var/www/agrovue
sudo chmod -R 775 /var/www/agrovue/storage
sudo chmod -R 775 /var/www/agrovue/bootstrap/cache
```

### 3. Web Server Configuration

#### Nginx Configuration
Create `/etc/nginx/sites-available/agrovue`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /var/www/agrovue/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
}

# Rate limiting zones
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/agrovue /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. SSL Certificate Setup

#### Using Let's Encrypt
```bash
sudo apt install -y certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

#### Auto-renewal
```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

### 5. Process Management

#### Supervisor for Queue Workers
Install Supervisor:
```bash
sudo apt install -y supervisor
```

Create `/etc/supervisor/conf.d/agrovue-worker.conf`:
```ini
[program:agrovue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/agrovue/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/agrovue/storage/logs/worker.log
stopwaitsecs=3600
```

Start Supervisor:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start agrovue-worker:*
```

#### Scheduler Cron Job
```bash
sudo crontab -e -u www-data
# Add this line:
* * * * * cd /var/www/agrovue && php artisan schedule:run >> /dev/null 2>&1
```

### 6. Monitoring and Logging

#### Log Rotation
Create `/etc/logrotate.d/agrovue`:
```
/var/www/agrovue/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

#### System Monitoring
Install monitoring tools:
```bash
sudo apt install -y htop iotop nethogs
```

#### Application Monitoring
Consider using:
- **New Relic** or **DataDog** for APM
- **Sentry** for error tracking
- **Prometheus + Grafana** for metrics
- **ELK Stack** for log analysis

### 7. Backup Strategy

#### Database Backup Script
Create `/usr/local/bin/backup-agrovue.sh`:
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/agrovue"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="agrovue"
DB_USER="agrovue"

mkdir -p $BACKUP_DIR

# Database backup
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Application files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C /var/www agrovue --exclude=node_modules --exclude=.git

# Keep only last 30 days of backups
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

Make executable and schedule:
```bash
sudo chmod +x /usr/local/bin/backup-agrovue.sh
sudo crontab -e
# Add this line for daily backup at 2 AM:
0 2 * * * /usr/local/bin/backup-agrovue.sh
```

### 8. Security Hardening

#### Firewall Configuration
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 3306  # MySQL
sudo ufw deny 5432  # PostgreSQL
```

#### Fail2Ban Setup
```bash
sudo apt install -y fail2ban
```

Create `/etc/fail2ban/jail.local`:
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

#### Regular Security Updates
```bash
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## Docker Deployment

### Docker Compose Setup
Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    environment:
      - APP_ENV=production
    volumes:
      - ./storage:/var/www/html/storage
      - ./ssl:/etc/ssl/certs
    depends_on:
      - database
      - redis

  database:
    image: postgres:13
    environment:
      POSTGRES_DB: agrovue
      POSTGRES_USER: agrovue
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

  worker:
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: php artisan queue:work redis --sleep=3 --tries=3
    depends_on:
      - database
      - redis
    deploy:
      replicas: 3

volumes:
  postgres_data:
  redis_data:
```

Deploy:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Performance Optimization

### PHP-FPM Tuning
Edit `/etc/php/8.2/fpm/pool.d/www.conf`:
```ini
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

### Database Optimization
PostgreSQL configuration in `/etc/postgresql/13/main/postgresql.conf`:
```ini
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

### Redis Configuration
Edit `/etc/redis/redis.conf`:
```ini
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## Troubleshooting

### Common Issues

#### Permission Errors
```bash
sudo chown -R www-data:www-data /var/www/agrovue
sudo chmod -R 755 /var/www/agrovue
sudo chmod -R 775 /var/www/agrovue/storage
sudo chmod -R 775 /var/www/agrovue/bootstrap/cache
```

#### Queue Not Processing
```bash
# Check supervisor status
sudo supervisorctl status

# Restart workers
sudo supervisorctl restart agrovue-worker:*

# Check logs
tail -f /var/www/agrovue/storage/logs/worker.log
```

#### Database Connection Issues
```bash
# Test database connection
php artisan tinker
DB::connection()->getPdo();
```

#### High Memory Usage
```bash
# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Restart PHP-FPM
sudo systemctl restart php8.2-fpm
```

### Log Locations
- **Application**: `/var/www/agrovue/storage/logs/laravel.log`
- **Nginx**: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- **PHP-FPM**: `/var/log/php8.2-fpm.log`
- **PostgreSQL**: `/var/log/postgresql/postgresql-13-main.log`
- **Redis**: `/var/log/redis/redis-server.log`

## Maintenance

### Regular Tasks
- **Daily**: Check logs for errors
- **Weekly**: Review performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and optimize database queries

### Update Procedure
1. Backup database and files
2. Put application in maintenance mode
3. Pull latest code changes
4. Update dependencies
5. Run migrations
6. Clear and rebuild caches
7. Restart services
8. Remove maintenance mode
9. Verify functionality

```bash
# Maintenance mode
php artisan down

# Update process
git pull origin main
composer install --no-dev --optimize-autoloader
npm ci && npm run build
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
sudo supervisorctl restart agrovue-worker:*

# Remove maintenance mode
php artisan up
```
