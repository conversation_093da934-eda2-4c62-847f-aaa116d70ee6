# AgroVue API Documentation

## Overview

The AgroVue API provides programmatic access to agricultural field monitoring, satellite data, fraud detection, and alert management capabilities. The API follows RESTful principles and returns JSON responses.

## Base URL
```
Production: https://api.agrovue.com/v1
Development: http://localhost:8000/api/v1
```

## Authentication

### API Key Authentication
Include your API key in the request header:
```http
X-API-Key: your-api-key-here
```

### Bearer Token Authentication
For user-specific operations, use Sanctum tokens:
```http
Authorization: Bearer your-token-here
```

## Rate Limiting
- **Public endpoints**: 100 requests per minute
- **Authenticated endpoints**: 1000 requests per minute
- **Bulk operations**: 10 requests per minute

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

## Endpoints

### Fields Management

#### List Fields
```http
GET /fields
```

**Parameters:**
- `search` (string): Search by field name or ID
- `state` (string): Filter by state
- `crop_type` (string): Filter by crop type
- `status` (string): Filter by status
- `organization_id` (integer): Filter by organization
- `per_page` (integer): Results per page (max 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Sunrise Rice Farm",
        "field_id": "FLD-000001",
        "area_hectares": 25.5,
        "crop_type": "rice",
        "crop_variety": "FARO 44",
        "state": "Kaduna",
        "lga": "Zaria",
        "status": "active",
        "health_status": "good",
        "latest_ndvi": 0.7234,
        "last_satellite_update": "2024-06-15T10:30:00Z",
        "owner": {
          "id": 1,
          "name": "John Farmer",
          "email": "<EMAIL>"
        }
      }
    ],
    "current_page": 1,
    "last_page": 5,
    "per_page": 20,
    "total": 95
  }
}
```

#### Get Field Details
```http
GET /fields/{id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Sunrise Rice Farm",
    "field_id": "FLD-000001",
    "area_hectares": 25.5,
    "crop_type": "rice",
    "coordinates": {
      "type": "Polygon",
      "coordinates": [[[7.4, 9.1], [7.5, 9.1], [7.5, 9.2], [7.4, 9.2], [7.4, 9.1]]]
    },
    "satellite_data": [
      {
        "id": 1,
        "capture_date": "2024-06-15",
        "ndvi_average": 0.7234,
        "vegetation_health": "good"
      }
    ],
    "alerts": [
      {
        "id": 1,
        "type": "drought",
        "severity": "medium",
        "title": "Drought Risk Alert",
        "status": "active"
      }
    ]
  }
}
```

#### Update Field Status
```http
POST /fields/{id}/update-status
```

**Request Body:**
```json
{
  "status": "verified",
  "notes": "Field verified by satellite data analysis"
}
```

### Satellite Data

#### Ingest Satellite Data
```http
POST /satellite-data
```

**Request Body:**
```json
{
  "field_id": 1,
  "satellite_source": "sentinel-2",
  "capture_date": "2024-06-15",
  "cloud_coverage": 15.5,
  "ndvi_data": [0.7, 0.75, 0.8, 0.72, 0.78],
  "evi_average": 0.65,
  "savi_average": 0.55,
  "image_url": "https://example.com/satellite-image.jpg",
  "metadata": {
    "resolution": "10m",
    "bands": ["B4", "B8"],
    "scene_id": "S2A_MSIL1C_20240615T103021_N0204_R108_T32NMH_20240615T123456"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "field_id": 1,
    "satellite_source": "sentinel-2",
    "ndvi_average": 0.75,
    "vegetation_health": "good",
    "anomalies_detected": [],
    "processed_at": "2024-06-15T12:00:00Z"
  }
}
```

#### Bulk Satellite Data Ingestion
```http
POST /satellite-data/bulk
```

**Request Body:**
```json
{
  "satellite_data": [
    {
      "field_id": 1,
      "satellite_source": "sentinel-2",
      "capture_date": "2024-06-15",
      "cloud_coverage": 10,
      "ndvi_data": [0.7, 0.75, 0.8]
    },
    {
      "field_id": 2,
      "satellite_source": "landsat-8",
      "capture_date": "2024-06-15",
      "cloud_coverage": 20,
      "ndvi_data": [0.6, 0.65, 0.7]
    }
  ]
}
```

#### Get Field Satellite Data
```http
GET /satellite-data/{field_id}
```

**Parameters:**
- `start_date` (date): Filter from date (YYYY-MM-DD)
- `end_date` (date): Filter to date (YYYY-MM-DD)
- `satellite_source` (string): Filter by satellite source
- `max_cloud_coverage` (number): Maximum cloud coverage percentage
- `per_page` (integer): Results per page

### Alerts Management

#### List Alerts
```http
GET /alerts
```

**Parameters:**
- `type` (string): Filter by alert type (drought, flood, pest, disease, fraud)
- `severity` (string): Filter by severity (low, medium, high, critical)
- `status` (string): Filter by status (active, acknowledged, resolved)
- `field_id` (integer): Filter by field
- `organization_id` (integer): Filter by organization

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "alert_id": "ALT-00000001",
      "field_id": 1,
      "type": "drought",
      "severity": "medium",
      "title": "Drought Risk Alert",
      "description": "Low soil moisture detected in field area",
      "status": "active",
      "source": "satellite",
      "metadata": {
        "confidence_score": 0.85,
        "affected_area": 15.2,
        "recommended_actions": ["Increase irrigation", "Monitor soil moisture"]
      },
      "created_at": "2024-06-15T10:00:00Z",
      "field": {
        "id": 1,
        "name": "Sunrise Rice Farm",
        "owner": {
          "name": "John Farmer"
        }
      }
    }
  ]
}
```

#### Create Alert
```http
POST /alerts
```

**Request Body:**
```json
{
  "field_id": 1,
  "type": "drought",
  "severity": "high",
  "title": "Severe Drought Detected",
  "description": "Critical soil moisture levels detected via satellite analysis",
  "source": "satellite",
  "metadata": {
    "confidence_score": 0.95,
    "affected_area": 25.5,
    "recommended_actions": ["Immediate irrigation required", "Contact extension officer"]
  },
  "expires_at": "2024-06-30T23:59:59Z"
}
```

#### Update Alert Status
```http
PUT /alerts/{id}/status
```

**Request Body:**
```json
{
  "status": "acknowledged",
  "notes": "Alert reviewed and irrigation scheduled"
}
```

### Subsidy Claims

#### List Subsidy Claims
```http
GET /subsidy-claims
```

**Parameters:**
- `status` (string): Filter by claim status
- `subsidy_type` (string): Filter by subsidy type
- `risk_score_min` (number): Minimum fraud risk score
- `risk_score_max` (number): Maximum fraud risk score
- `claimant_id` (integer): Filter by claimant

#### Get Claim Details
```http
GET /subsidy-claims/{id}
```

#### Update Fraud Risk Score
```http
POST /subsidy-claims/{id}/fraud-score
```

**Request Body:**
```json
{
  "fraud_risk_score": 75,
  "fraud_indicators": [
    "field_not_properly_mapped",
    "poor_vegetation_health",
    "multiple_recent_claims"
  ],
  "analysis_notes": "High risk due to satellite data inconsistencies"
}
```

### Mobile API

#### Mobile Dashboard
```http
GET /mobile/dashboard
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "John Farmer",
      "role": "farmer"
    },
    "summary": {
      "total_fields": 3,
      "active_alerts": 2,
      "pending_claims": 1,
      "health_status": "good"
    },
    "recent_fields": [
      {
        "id": 1,
        "name": "Rice Farm Alpha",
        "health_status": "good",
        "latest_ndvi": 0.75
      }
    ],
    "weather_info": {
      "temperature": 28,
      "humidity": 65,
      "rainfall_today": 0,
      "forecast": [
        {
          "day": "Today",
          "condition": "Sunny",
          "temp_high": 32,
          "temp_low": 24
        }
      ]
    }
  }
}
```

#### Upload Field Photo
```http
POST /mobile/field-photo
```

**Request Body (multipart/form-data):**
- `field_id` (integer): Field ID
- `photo` (file): Image file (max 5MB)
- `description` (string): Photo description
- `latitude` (number): GPS latitude
- `longitude` (number): GPS longitude

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid request format |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 422 | Unprocessable Entity - Validation errors |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## Webhooks

AgroVue can send webhook notifications for important events:

### Alert Created
```json
{
  "event": "alert.created",
  "data": {
    "alert_id": "ALT-00000001",
    "field_id": 1,
    "type": "drought",
    "severity": "high"
  },
  "timestamp": "2024-06-15T10:00:00Z"
}
```

### Satellite Data Processed
```json
{
  "event": "satellite_data.processed",
  "data": {
    "field_id": 1,
    "satellite_data_id": 123,
    "vegetation_health": "good",
    "anomalies_detected": []
  },
  "timestamp": "2024-06-15T12:00:00Z"
}
```

## SDKs and Libraries

### JavaScript/Node.js
```bash
npm install agrovue-sdk
```

```javascript
import AgroVue from 'agrovue-sdk';

const client = new AgroVue({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.agrovue.com/v1'
});

const fields = await client.fields.list();
```

### Python
```bash
pip install agrovue-python
```

```python
from agrovue import AgroVueClient

client = AgroVueClient(api_key='your-api-key')
fields = client.fields.list()
```

## Support

For API support and questions:
- **Documentation**: https://docs.agrovue.com
- **Support Email**: <EMAIL>
- **Status Page**: https://status.agrovue.com
