<?php

use App\Http\Controllers\AlertController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FieldController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SubsidyClaimController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth')->name('logout');

// Protected Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Field Management Routes
    Route::prefix('fields')->name('fields.')->group(function () {
        Route::get('/', [FieldController::class, 'index'])->name('index');
        Route::get('/create', [FieldController::class, 'create'])->name('create');
        Route::post('/', [FieldController::class, 'store'])->name('store');
        Route::get('/{field}', [FieldController::class, 'show'])->name('show');
        Route::get('/{field}/edit', [FieldController::class, 'edit'])->name('edit');
        Route::put('/{field}', [FieldController::class, 'update'])->name('update');
        Route::delete('/{field}', [FieldController::class, 'destroy'])->name('destroy');
        Route::get('/{field}/satellite-data', [FieldController::class, 'satelliteData'])->name('satellite-data');
    });

    // Subsidy Claims Routes (Government Admin and Bank Analyst)
    Route::middleware(['role:government_admin,bank_analyst,farmer'])->prefix('subsidy-claims')->name('subsidy-claims.')->group(function () {
        Route::get('/', [SubsidyClaimController::class, 'index'])->name('index');
        Route::get('/create', [SubsidyClaimController::class, 'create'])->name('create')->middleware('role:farmer');
        Route::post('/', [SubsidyClaimController::class, 'store'])->name('store')->middleware('role:farmer');
        Route::get('/{subsidyClaim}', [SubsidyClaimController::class, 'show'])->name('show');
        Route::put('/{subsidyClaim}/review', [SubsidyClaimController::class, 'review'])->name('review')->middleware('role:government_admin,bank_analyst');
        Route::put('/{subsidyClaim}/approve', [SubsidyClaimController::class, 'approve'])->name('approve')->middleware('role:government_admin');
        Route::put('/{subsidyClaim}/reject', [SubsidyClaimController::class, 'reject'])->name('reject')->middleware('role:government_admin');
    });

    // Alerts Routes
    Route::prefix('alerts')->name('alerts.')->group(function () {
        Route::get('/', [AlertController::class, 'index'])->name('index');
        Route::post('/', [AlertController::class, 'store'])->name('store');
        Route::get('/{alert}', [AlertController::class, 'show'])->name('show');
        Route::put('/{alert}/acknowledge', [AlertController::class, 'acknowledge'])->name('acknowledge');
        Route::put('/{alert}/resolve', [AlertController::class, 'resolve'])->name('resolve');
        Route::put('/{alert}/assign', [AlertController::class, 'assign'])->name('assign');
    });

    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::post('/generate', [ReportController::class, 'generate'])->name('generate');
        Route::get('/{report}', [ReportController::class, 'show'])->name('show');
        Route::get('/{report}/download', [ReportController::class, 'download'])->name('download');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
