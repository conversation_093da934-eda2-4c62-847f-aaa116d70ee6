<?php

use App\Http\Controllers\Api\FieldApiController;
use App\Http\Controllers\Api\SatelliteDataApiController;
use App\Http\Controllers\Api\AlertApiController;
use App\Http\Controllers\Api\SubsidyClaimApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Public API endpoints (with API key authentication)
Route::middleware(['api.key'])->prefix('v1')->group(function () {
    // Satellite data ingestion
    Route::post('/satellite-data', [SatelliteDataApiController::class, 'store']);
    Route::get('/satellite-data/{field}', [SatelliteDataApiController::class, 'getByField']);
    
    // Field data for external systems
    Route::get('/fields', [FieldApiController::class, 'index']);
    Route::get('/fields/{field}', [FieldApiController::class, 'show']);
    Route::post('/fields/{field}/update-status', [FieldApiController::class, 'updateStatus']);
    
    // Alert system integration
    Route::post('/alerts', [AlertApiController::class, 'store']);
    Route::get('/alerts', [AlertApiController::class, 'index']);
    Route::put('/alerts/{alert}/status', [AlertApiController::class, 'updateStatus']);
    
    // Subsidy claims for government systems
    Route::get('/subsidy-claims', [SubsidyClaimApiController::class, 'index']);
    Route::get('/subsidy-claims/{claim}', [SubsidyClaimApiController::class, 'show']);
    Route::post('/subsidy-claims/{claim}/fraud-score', [SubsidyClaimApiController::class, 'updateFraudScore']);
});

// Authenticated user endpoints
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    // Mobile app endpoints
    Route::prefix('mobile')->group(function () {
        Route::get('/dashboard', [FieldApiController::class, 'mobileDashboard']);
        Route::get('/my-fields', [FieldApiController::class, 'myFields']);
        Route::get('/my-alerts', [AlertApiController::class, 'myAlerts']);
        Route::post('/field-photo', [FieldApiController::class, 'uploadPhoto']);
    });
});
