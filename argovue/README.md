# AgroVue - Satellite-Powered Agricultural Intelligence Platform

AgroVue is a comprehensive agricultural monitoring and management platform that leverages satellite data, AI-powered fraud detection, and real-time analytics to support farmers, government agencies, NGOs, and financial institutions across Africa.

## 🌟 Features

### Core Capabilities
- **Satellite Monitoring**: Real-time NDVI analysis and vegetation health monitoring using Sentinel-2 and Landsat data
- **Fraud Detection**: AI-powered subsidy fraud detection with satellite verification and risk scoring
- **Yield Forecasting**: Predictive analytics for crop yield estimation and harvest planning
- **Disaster Alerts**: Early warning system for drought, flood, and weather-related agricultural risks
- **Compliance Reports**: Automated generation of compliance and performance reports
- **Mobile Optimized**: Offline-capable mobile interface for field agents and farmers

### User Roles
- **Government Admin**: Monitor subsidy programs, detect fraud, generate compliance reports
- **NGO Coordinator**: Track disaster impacts, coordinate relief efforts, monitor field conditions
- **Farmer**: Monitor fields, access AI advisory, manage subsidy applications
- **Bank Analyst**: Assess loan risks, verify agricultural activities, monitor borrower performance
- **Corporate**: Supply chain auditing and sustainability compliance monitoring

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- Node.js 18+
- Composer
- SQLite/MySQL/PostgreSQL
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd agrovue
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database setup**
   ```bash
   php artisan migrate
   php artisan db:seed --class=AgroVueSeeder
   ```

6. **Build frontend assets**
   ```bash
   npm run build
   ```

7. **Start the development servers**
   ```bash
   # Terminal 1: Laravel server
   php artisan serve

   # Terminal 2: Vite dev server
   npm run dev
   ```

8. **Access the application**
   - Web: http://localhost:8000
   - API: http://localhost:8000/api/v1

## 🔐 Demo Credentials

Use these credentials to test different user roles:

- **Government Admin**: <EMAIL> / password
- **NGO Coordinator**: <EMAIL> / password
- **Farmer**: <EMAIL> / password
- **Bank Analyst**: <EMAIL> / password

## 📱 Mobile Access

AgroVue is fully responsive and optimized for mobile devices. The mobile interface includes:
- Bottom navigation for easy thumb access
- Compact field cards with essential information
- Offline-capable functionality for field agents
- Touch-optimized controls and gestures

## 🛠 Technology Stack

### Backend
- **Framework**: Laravel 11
- **Database**: SQLite (development), PostgreSQL (production)
- **Authentication**: Laravel Sanctum
- **API**: RESTful API with JSON responses
- **Queue System**: Redis/Database queues for background processing

### Frontend
- **Framework**: React 18 with TypeScript
- **Routing**: Inertia.js for SPA-like experience
- **UI Components**: ShadCN UI with Tailwind CSS
- **State Management**: React hooks and context
- **Build Tool**: Vite

### Services & Integrations
- **Satellite Data**: Sentinel-2, Landsat-8/9, MODIS
- **Maps**: OpenStreetMap with Leaflet
- **Weather**: OpenWeatherMap API
- **Storage**: Local filesystem (development), S3 (production)

## 📊 API Documentation

### Authentication
All API endpoints require authentication via API key or Sanctum token.

```bash
# API Key authentication
curl -H "X-API-Key: your-api-key" http://localhost:8000/api/v1/fields

# Token authentication
curl -H "Authorization: Bearer your-token" http://localhost:8000/api/v1/fields
```

### Key Endpoints

#### Fields Management
- `GET /api/v1/fields` - List all fields
- `GET /api/v1/fields/{id}` - Get field details
- `POST /api/v1/fields/{id}/update-status` - Update field status

#### Satellite Data
- `POST /api/v1/satellite-data` - Ingest satellite data
- `GET /api/v1/satellite-data/{field}` - Get field satellite data
- `POST /api/v1/satellite-data/bulk` - Bulk satellite data ingestion

#### Alerts
- `GET /api/v1/alerts` - List alerts
- `POST /api/v1/alerts` - Create alert
- `PUT /api/v1/alerts/{id}/status` - Update alert status

#### Mobile API
- `GET /api/mobile/dashboard` - Mobile dashboard data
- `GET /api/mobile/my-fields` - User's fields
- `POST /api/mobile/field-photo` - Upload field photo

## 🧪 Testing

### Running Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --filter=AuthenticationTest
php artisan test --filter=FieldManagementTest
php artisan test --filter=SatelliteDataTest

# Run with coverage
php artisan test --coverage
```

### Test Categories
- **Feature Tests**: End-to-end functionality testing
- **Unit Tests**: Individual component testing
- **API Tests**: API endpoint testing
- **Browser Tests**: Frontend interaction testing

## 🔧 Configuration

### Environment Variables
```env
# Application
APP_NAME=AgroVue
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=agrovue
DB_USERNAME=agrovue
DB_PASSWORD=your-password

# Satellite Data APIs
SENTINEL_API_URL=https://scihub.copernicus.eu/dhus/search
SENTINEL_USERNAME=your-username
SENTINEL_PASSWORD=your-password

# Weather API
OPENWEATHER_API_KEY=your-api-key

# File Storage
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=agrovue-storage

# Queue Configuration
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

## 📈 Performance Optimization

### Database Optimization
- Indexed fields for fast queries
- Optimized satellite data storage with JSON columns
- Efficient pagination for large datasets
- Database query optimization with eager loading

### Caching Strategy
- Redis caching for frequently accessed data
- API response caching
- Static asset caching with CDN
- Database query result caching

### Background Processing
- Queue-based satellite data processing
- Asynchronous alert generation
- Background report generation
- Scheduled data synchronization

## 🔒 Security Features

### Authentication & Authorization
- Multi-factor authentication support
- Role-based access control (RBAC)
- API rate limiting
- Session management with secure cookies

### Data Protection
- Encrypted sensitive data storage
- HTTPS enforcement
- CSRF protection
- SQL injection prevention
- XSS protection

### API Security
- API key authentication
- Request validation and sanitization
- Rate limiting per user/IP
- Audit logging for sensitive operations

## 🌍 Deployment

### Production Deployment
1. **Server Requirements**
   - Ubuntu 20.04+ or CentOS 8+
   - PHP 8.2+ with required extensions
   - PostgreSQL 13+
   - Redis 6+
   - Nginx or Apache

2. **Deployment Steps**
   ```bash
   # Clone and setup
   git clone <repository-url>
   cd agrovue
   composer install --no-dev --optimize-autoloader
   npm ci && npm run build

   # Environment and database
   cp .env.production .env
   php artisan key:generate
   php artisan migrate --force
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache

   # Set permissions
   chown -R www-data:www-data storage bootstrap/cache
   chmod -R 775 storage bootstrap/cache
   ```

3. **Web Server Configuration**
   - Configure Nginx/Apache virtual host
   - Set up SSL certificates
   - Configure reverse proxy for API
   - Set up log rotation

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d

# Scale services
docker-compose up -d --scale worker=3
```

## 📞 Support & Contributing

### Getting Help
- **Documentation**: Check this README and inline code documentation
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Community**: Join our community discussions

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Submit a pull request

### Development Guidelines
- Follow PSR-12 coding standards for PHP
- Use TypeScript for all React components
- Write comprehensive tests for new features
- Update documentation for API changes
- Follow semantic versioning for releases

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Satellite Data Providers**: ESA Copernicus, NASA Landsat, MODIS
- **Open Source Libraries**: Laravel, React, Tailwind CSS, and many others
- **Community Contributors**: Thanks to all contributors who help improve AgroVue

---

**AgroVue** - Empowering agriculture through satellite intelligence 🛰️🌾
