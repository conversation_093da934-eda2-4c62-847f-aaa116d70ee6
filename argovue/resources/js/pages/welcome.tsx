import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Satellite,
  Shield,
  TrendingUp,
  Users,
  MapPin,
  AlertTriangle,
  BarChart3,
  Smartphone
} from 'lucide-react';

export default function Welcome() {
  const features = [
    {
      icon: Satellite,
      title: 'Satellite Monitoring',
      description: 'Real-time NDVI analysis and vegetation health monitoring using Sentinel-2 and Landsat data.',
      badge: 'Core Feature'
    },
    {
      icon: Shield,
      title: 'Fraud Detection',
      description: 'AI-powered subsidy fraud detection with satellite verification and risk scoring.',
      badge: 'Government'
    },
    {
      icon: TrendingUp,
      title: 'Yield Forecasting',
      description: 'Predictive analytics for crop yield estimation and harvest planning.',
      badge: 'Analytics'
    },
    {
      icon: AlertTriangle,
      title: 'Disaster Alerts',
      description: 'Early warning system for drought, flood, and weather-related agricultural risks.',
      badge: 'NGO'
    },
    {
      icon: BarChart3,
      title: 'Compliance Reports',
      description: 'Automated generation of compliance and performance reports for stakeholders.',
      badge: 'Reporting'
    },
    {
      icon: Smartphone,
      title: 'Mobile Optimized',
      description: 'Offline-capable mobile interface for field agents and farmers with low bandwidth.',
      badge: 'Mobile'
    }
  ];

  const userTypes = [
    {
      title: 'Government Admin',
      description: 'Monitor subsidy programs, detect fraud, and generate compliance reports.',
      features: ['Subsidy verification', 'Fraud detection', 'Compliance reporting', 'Field assignment'],
      color: 'bg-blue-50 border-blue-200'
    },
    {
      title: 'NGO Coordinator',
      description: 'Track disaster impacts, coordinate relief efforts, and monitor field conditions.',
      features: ['Disaster monitoring', 'Impact assessment', 'Relief coordination', 'Advisory distribution'],
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Farmer',
      description: 'Monitor your fields, access AI advisory, and manage subsidy applications.',
      features: ['Field monitoring', 'AI advisory', 'Yield tracking', 'Subsidy applications'],
      color: 'bg-yellow-50 border-yellow-200'
    },
    {
      title: 'Bank Analyst',
      description: 'Assess loan risks, verify agricultural activities, and monitor borrower performance.',
      features: ['Risk assessment', 'Activity verification', 'Loan monitoring', 'Performance tracking'],
      color: 'bg-purple-50 border-purple-200'
    }
  ];

  return (
    <>
      <Head title="Welcome to AgroVue" />

      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-yellow-50">
        {/* Header */}
        <header className="border-b bg-white/80 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Satellite className="h-8 w-8 text-green-600" />
                <span className="text-2xl font-bold text-gray-900">AgroVue</span>
              </div>
              <div className="flex items-center space-x-4">
                <Link href="/login">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/register">
                  <Button>Get Started</Button>
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              Satellite-Powered Agricultural Intelligence
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Delivering geospatial insights for subsidy fraud detection, disaster monitoring,
              yield optimization, and sustainable supply chain auditing across Africa.
            </p>
            <div className="flex items-center justify-center space-x-4">
              <Link href="/register">
                <Button size="lg" className="px-8">
                  Start Monitoring
                </Button>
              </Link>
              <Link href="/login">
                <Button size="lg" variant="outline" className="px-8">
                  Sign In
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Comprehensive Agricultural Monitoring
              </h2>
              <p className="text-lg text-gray-600">
                Advanced satellite technology meets agricultural expertise
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <feature.icon className="h-8 w-8 text-green-600" />
                      <Badge variant="secondary">{feature.badge}</Badge>
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* User Types Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Built for Every Stakeholder
              </h2>
              <p className="text-lg text-gray-600">
                Role-based access and features tailored to your needs
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {userTypes.map((userType, index) => (
                <Card key={index} className={`${userType.color} hover:shadow-lg transition-shadow`}>
                  <CardHeader>
                    <CardTitle className="text-xl">{userType.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {userType.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {userType.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-green-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Transform Agricultural Monitoring?
            </h2>
            <p className="text-lg mb-8 opacity-90">
              Join thousands of users already leveraging satellite intelligence for better agricultural outcomes.
            </p>
            <Link href="/register">
              <Button size="lg" variant="secondary" className="px-8">
                Get Started Today
              </Button>
            </Link>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-8">
          <div className="container mx-auto px-4 text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Satellite className="h-6 w-6" />
              <span className="text-lg font-semibold">AgroVue</span>
            </div>
            <p className="text-gray-400 text-sm">
              © 2025 AgroVue. Empowering agriculture through satellite intelligence.
            </p>
          </div>
        </footer>
      </div>
    </>
  );
}
