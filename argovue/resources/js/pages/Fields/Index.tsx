import { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Field, PaginatedResponse } from '@/types/agrovue';
import AgroVueLayout from '@/layouts/AgroVueLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2,
  Satellite,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';

interface FieldsIndexProps {
  fields: PaginatedResponse<Field>;
  filters: {
    search?: string;
    crop_type?: string;
    status?: string;
    owner?: string;
  };
}

export default function FieldsIndex({ fields, filters }: FieldsIndexProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [cropType, setCropType] = useState(filters.crop_type || '');
  const [status, setStatus] = useState(filters.status || '');

  const handleSearch = () => {
    router.get('/fields', {
      search: searchTerm,
      crop_type: cropType,
      status: status,
    }, {
      preserveState: true,
      replace: true,
    });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setCropType('');
    setStatus('');
    router.get('/fields', {}, {
      preserveState: true,
      replace: true,
    });
  };

  const getHealthStatusColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'fair': return 'bg-yellow-100 text-yellow-800';
      case 'poor': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'disputed': return 'bg-red-100 text-red-800';
      case 'verified': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (changeType?: string) => {
    switch (changeType) {
      case 'improvement': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'decline': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <Minus className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <AgroVueLayout 
      title="Fields Management"
      breadcrumbs={[
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Fields' }
      ]}
    >
      <Head title="Fields" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Fields</h1>
            <p className="text-muted-foreground">
              Manage and monitor agricultural fields with satellite intelligence
            </p>
          </div>
          <Link href="/fields/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search fields..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Crop Type</label>
                <Select value={cropType} onValueChange={setCropType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All crops" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All crops</SelectItem>
                    <SelectItem value="rice">Rice</SelectItem>
                    <SelectItem value="maize">Maize</SelectItem>
                    <SelectItem value="cassava">Cassava</SelectItem>
                    <SelectItem value="millet">Millet</SelectItem>
                    <SelectItem value="sorghum">Sorghum</SelectItem>
                    <SelectItem value="yam">Yam</SelectItem>
                    <SelectItem value="beans">Beans</SelectItem>
                    <SelectItem value="groundnut">Groundnut</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="disputed">Disputed</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Actions</label>
                <div className="flex space-x-2">
                  <Button onClick={handleSearch} className="flex-1">
                    Apply
                  </Button>
                  <Button variant="outline" onClick={clearFilters}>
                    Clear
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fields Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {fields.data.map((field) => (
            <Card key={field.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{field.name}</CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <MapPin className="h-4 w-4 mr-1" />
                      {field.lga}, {field.state}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(field.status)}>
                    {field.status}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Field Info */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Area:</span>
                    <p className="font-medium">{field.area_hectares} ha</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Crop:</span>
                    <p className="font-medium capitalize">{field.crop_type}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Owner:</span>
                    <p className="font-medium">{field.owner?.name}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Field ID:</span>
                    <p className="font-medium">{field.field_id}</p>
                  </div>
                </div>

                {/* Health Status */}
                {field.health_status && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Health:</span>
                    <div className="flex items-center space-x-2">
                      <Badge className={getHealthStatusColor(field.health_status)}>
                        {field.health_status}
                      </Badge>
                      {field.satellite_data?.[0]?.change_detection && 
                        getTrendIcon(field.satellite_data[0].change_detection.change_type)
                      }
                    </div>
                  </div>
                )}

                {/* NDVI */}
                {field.latest_ndvi && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Latest NDVI:</span>
                    <span className="font-medium">{field.latest_ndvi.toFixed(3)}</span>
                  </div>
                )}

                {/* Last Update */}
                {field.last_satellite_update && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Last Update:</span>
                    <div className="flex items-center space-x-1">
                      <Satellite className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm">
                        {new Date(field.last_satellite_update).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Link href={`/fields/${field.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </Link>
                  <Link href={`/fields/${field.id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {fields.data.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No fields found</h3>
              <p className="text-muted-foreground mb-4">
                {Object.values(filters).some(Boolean) 
                  ? "No fields match your current filters. Try adjusting your search criteria."
                  : "Get started by adding your first field to the system."
                }
              </p>
              {!Object.values(filters).some(Boolean) && (
                <Link href="/fields/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Field
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {fields.data.length > 0 && fields.last_page > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {fields.from} to {fields.to} of {fields.total} fields
            </p>
            <div className="flex space-x-2">
              {fields.links.prev && (
                <Link href={fields.links.prev}>
                  <Button variant="outline" size="sm">Previous</Button>
                </Link>
              )}
              <span className="flex items-center px-3 py-2 text-sm">
                Page {fields.current_page} of {fields.last_page}
              </span>
              {fields.links.next && (
                <Link href={fields.links.next}>
                  <Button variant="outline" size="sm">Next</Button>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </AgroVueLayout>
  );
}
