import { Link } from '@inertiajs/react';
import { User, DashboardData } from '@/types/agrovue';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  AlertTriangle, 
  DollarSign, 
  TrendingUp, 
  Activity,
  Bell,
  FileText,
  Satellite,
  Users,
  BarChart3,
  Calendar,
  ChevronRight
} from 'lucide-react';

interface MobileDashboardProps {
  user: User;
  dashboardData: DashboardData;
}

export default function MobileDashboard({ user, dashboardData }: MobileDashboardProps) {
  const renderQuickStats = () => {
    const stats = [];

    switch (user.role) {
      case 'farmer':
        stats.push(
          { label: 'My Fields', value: dashboardData.myFields || 0, icon: MapPin, color: 'text-green-600' },
          { label: 'Active Alerts', value: dashboardData.myAlerts?.length || 0, icon: AlertTriangle, color: 'text-orange-600' },
          { label: 'Claims', value: dashboardData.myClaims?.length || 0, icon: DollarSign, color: 'text-blue-600' }
        );
        break;
      
      case 'government_admin':
        stats.push(
          { label: 'Total Fields', value: dashboardData.totalFields || 0, icon: MapPin, color: 'text-green-600' },
          { label: 'Pending Claims', value: dashboardData.pendingClaims || 0, icon: DollarSign, color: 'text-blue-600' },
          { label: 'Flagged Claims', value: dashboardData.flaggedClaims || 0, icon: AlertTriangle, color: 'text-red-600' }
        );
        break;
      
      case 'bank_analyst':
        stats.push(
          { label: 'Loan Apps', value: dashboardData.loanApplications || 0, icon: FileText, color: 'text-blue-600' },
          { label: 'High Risk', value: dashboardData.highRiskClaims || 0, icon: AlertTriangle, color: 'text-red-600' },
          { label: 'Portfolio Health', value: `${dashboardData.portfolioHealth || 0}%`, icon: TrendingUp, color: 'text-green-600' }
        );
        break;
      
      case 'ngo_coordinator':
        stats.push(
          { label: 'Affected Fields', value: dashboardData.affectedFields || 0, icon: AlertTriangle, color: 'text-orange-600' },
          { label: 'Org Fields', value: dashboardData.organizationFields || 0, icon: MapPin, color: 'text-green-600' },
          { label: 'Disasters', value: dashboardData.disasterAlerts?.length || 0, icon: Activity, color: 'text-red-600' }
        );
        break;
    }

    return (
      <div className="grid grid-cols-3 gap-3 mb-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-3 text-center">
              <stat.icon className={`h-5 w-5 mx-auto mb-2 ${stat.color}`} />
              <div className="text-lg font-bold">{stat.value}</div>
              <div className="text-xs text-gray-500">{stat.label}</div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderRecentActivity = () => {
    const activities = [
      { 
        icon: Satellite, 
        title: 'Satellite data updated', 
        time: '2 min ago',
        color: 'text-blue-600'
      },
      { 
        icon: AlertTriangle, 
        title: 'Drought alert generated', 
        time: '1 hour ago',
        color: 'text-orange-600'
      },
      { 
        icon: DollarSign, 
        title: 'Subsidy claim approved', 
        time: '3 hours ago',
        color: 'text-green-600'
      },
    ];

    return (
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center">
            <Activity className="h-4 w-4 mr-2" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {activities.map((activity, index) => (
            <div key={index} className="flex items-center space-x-3">
              <div className={`p-1.5 rounded-full bg-gray-100`}>
                <activity.icon className={`h-3 w-3 ${activity.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{activity.title}</p>
                <p className="text-xs text-gray-500">{activity.time}</p>
              </div>
            </div>
          ))}
          <Link href="/alerts">
            <Button variant="outline" size="sm" className="w-full mt-3">
              View All Activity
            </Button>
          </Link>
        </CardContent>
      </Card>
    );
  };

  const renderQuickActions = () => {
    const actions = [];

    switch (user.role) {
      case 'farmer':
        actions.push(
          { label: 'View Fields', href: '/fields', icon: MapPin, color: 'bg-green-50 text-green-600' },
          { label: 'Check Alerts', href: '/alerts', icon: Bell, color: 'bg-orange-50 text-orange-600' },
          { label: 'Apply Subsidy', href: '/subsidy-claims/create', icon: DollarSign, color: 'bg-blue-50 text-blue-600' }
        );
        break;
      
      case 'government_admin':
        actions.push(
          { label: 'Review Claims', href: '/subsidy-claims', icon: FileText, color: 'bg-blue-50 text-blue-600' },
          { label: 'Generate Report', href: '/reports/create', icon: BarChart3, color: 'bg-purple-50 text-purple-600' },
          { label: 'View Alerts', href: '/alerts', icon: AlertTriangle, color: 'bg-red-50 text-red-600' }
        );
        break;
      
      case 'bank_analyst':
        actions.push(
          { label: 'Risk Analysis', href: '/subsidy-claims?filter=high_risk', icon: AlertTriangle, color: 'bg-red-50 text-red-600' },
          { label: 'Loan Reports', href: '/reports', icon: FileText, color: 'bg-blue-50 text-blue-600' },
          { label: 'Field Verification', href: '/fields', icon: MapPin, color: 'bg-green-50 text-green-600' }
        );
        break;
      
      case 'ngo_coordinator':
        actions.push(
          { label: 'Disaster Alerts', href: '/alerts?type=disaster', icon: AlertTriangle, color: 'bg-red-50 text-red-600' },
          { label: 'Impact Reports', href: '/reports', icon: FileText, color: 'bg-blue-50 text-blue-600' },
          { label: 'Field Monitoring', href: '/fields', icon: MapPin, color: 'bg-green-50 text-green-600' }
        );
        break;
    }

    return (
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {actions.map((action, index) => (
            <Link key={index} href={action.href}>
              <div className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${action.color}`}>
                    <action.icon className="h-4 w-4" />
                  </div>
                  <span className="font-medium">{action.label}</span>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </div>
            </Link>
          ))}
        </CardContent>
      </Card>
    );
  };

  const renderAlerts = () => {
    const alerts = dashboardData.myAlerts || dashboardData.activeAlerts || [];
    
    if (alerts.length === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center justify-between">
            <span className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Active Alerts
            </span>
            <Badge variant="destructive">{alerts.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {alerts.slice(0, 3).map((alert, index) => (
            <div key={index} className="flex items-start space-x-3 p-2 bg-orange-50 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-orange-900">{alert.title}</p>
                <p className="text-xs text-orange-700 truncate">{alert.description}</p>
                <p className="text-xs text-orange-600 mt-1">
                  {new Date(alert.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
          {alerts.length > 3 && (
            <Link href="/alerts">
              <Button variant="outline" size="sm" className="w-full">
                View {alerts.length - 3} More Alerts
              </Button>
            </Link>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6 pb-20"> {/* Extra padding for bottom navigation */}
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg">
        <h1 className="text-lg font-bold text-gray-900">
          Welcome back, {user.name.split(' ')[0]}!
        </h1>
        <p className="text-sm text-gray-600 mt-1">
          {user.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </p>
        {user.organization && (
          <Badge variant="outline" className="mt-2">
            {user.organization.name}
          </Badge>
        )}
      </div>

      {/* Quick Stats */}
      {renderQuickStats()}

      {/* Active Alerts */}
      {renderAlerts()}

      {/* Quick Actions */}
      {renderQuickActions()}

      {/* Recent Activity */}
      {renderRecentActivity()}

      {/* Weather Widget (if farmer) */}
      {user.role === 'farmer' && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Today's Weather
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">28°C</div>
                <div className="text-sm text-gray-500">Partly Cloudy</div>
              </div>
              <div className="text-right text-sm">
                <div>Humidity: 65%</div>
                <div>Rainfall: 0mm</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
