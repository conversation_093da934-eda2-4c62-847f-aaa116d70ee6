import { Link } from '@inertiajs/react';
import { Field } from '@/types/agrovue';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Satellite, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Eye,
  AlertTriangle,
  Calendar,
  Ruler
} from 'lucide-react';

interface MobileFieldCardProps {
  field: Field;
  showActions?: boolean;
}

export default function MobileFieldCard({ field, showActions = true }: MobileFieldCardProps) {
  const getHealthStatusColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'fair': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'disputed': return 'bg-red-100 text-red-800';
      case 'verified': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (changeType?: string) => {
    switch (changeType) {
      case 'improvement': return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'decline': return <TrendingDown className="h-3 w-3 text-red-600" />;
      default: return <Minus className="h-3 w-3 text-gray-400" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const hasActiveAlerts = field.alerts && field.alerts.some(alert => alert.status === 'active');

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-base truncate">{field.name}</h3>
            <div className="flex items-center text-sm text-gray-500 mt-1">
              <MapPin className="h-3 w-3 mr-1" />
              <span className="truncate">{field.lga}, {field.state}</span>
            </div>
          </div>
          <div className="flex flex-col items-end space-y-1 ml-2">
            <Badge className={getStatusColor(field.status)} variant="outline">
              {field.status}
            </Badge>
            {hasActiveAlerts && (
              <div className="flex items-center text-orange-600">
                <AlertTriangle className="h-3 w-3 mr-1" />
                <span className="text-xs">Alert</span>
              </div>
            )}
          </div>
        </div>

        {/* Field Details Grid */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="flex items-center space-x-2">
            <Ruler className="h-4 w-4 text-gray-400" />
            <div>
              <p className="text-xs text-gray-500">Area</p>
              <p className="text-sm font-medium">{field.area_hectares} ha</p>
            </div>
          </div>
          
          <div>
            <p className="text-xs text-gray-500">Crop</p>
            <p className="text-sm font-medium capitalize">{field.crop_type}</p>
          </div>

          {field.latest_ndvi && (
            <div>
              <p className="text-xs text-gray-500">NDVI</p>
              <div className="flex items-center space-x-1">
                <span className="text-sm font-medium">{field.latest_ndvi.toFixed(3)}</span>
                {field.satellite_data?.[0]?.change_detection && 
                  getTrendIcon(field.satellite_data[0].change_detection.change_type)
                }
              </div>
            </div>
          )}

          {field.health_status && (
            <div>
              <p className="text-xs text-gray-500">Health</p>
              <Badge className={getHealthStatusColor(field.health_status)} variant="outline">
                {field.health_status}
              </Badge>
            </div>
          )}
        </div>

        {/* Satellite Update Info */}
        {field.last_satellite_update && (
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3 p-2 bg-gray-50 rounded">
            <div className="flex items-center">
              <Satellite className="h-3 w-3 mr-1" />
              <span>Last update</span>
            </div>
            <span>{formatDate(field.last_satellite_update)}</span>
          </div>
        )}

        {/* Planting/Harvest Dates */}
        {(field.planting_date || field.expected_harvest_date) && (
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            {field.planting_date && (
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                <span>Planted: {formatDate(field.planting_date)}</span>
              </div>
            )}
            {field.expected_harvest_date && (
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                <span>Harvest: {formatDate(field.expected_harvest_date)}</span>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex space-x-2 pt-2 border-t">
            <Link href={`/fields/${field.id}`} className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                <Eye className="h-3 w-3 mr-1" />
                View Details
              </Button>
            </Link>
            {field.satellite_data && field.satellite_data.length > 0 && (
              <Link href={`/fields/${field.id}/satellite-data`}>
                <Button variant="outline" size="sm">
                  <Satellite className="h-3 w-3" />
                </Button>
              </Link>
            )}
          </div>
        )}

        {/* Quick Stats for Mobile */}
        <div className="mt-3 pt-3 border-t">
          <div className="flex justify-between text-xs">
            <span className="text-gray-500">Field ID: {field.field_id}</span>
            {field.expected_yield_tons && (
              <span className="text-gray-500">
                Expected: {field.expected_yield_tons}t
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for list views
export function CompactMobileFieldCard({ field }: { field: Field }) {
  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'bg-green-500';
      case 'good': return 'bg-blue-500';
      case 'fair': return 'bg-yellow-500';
      case 'poor': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Link href={`/fields/${field.id}`}>
      <Card className="w-full hover:shadow-md transition-shadow">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                {field.health_status && (
                  <div className={`w-2 h-2 rounded-full ${getHealthColor(field.health_status)}`} />
                )}
                <h4 className="font-medium text-sm truncate">{field.name}</h4>
              </div>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <MapPin className="h-3 w-3 mr-1" />
                <span className="truncate">{field.lga}, {field.state}</span>
                <span className="mx-2">•</span>
                <span>{field.area_hectares} ha</span>
              </div>
            </div>
            
            <div className="flex flex-col items-end space-y-1">
              {field.latest_ndvi && (
                <span className="text-xs font-medium">{field.latest_ndvi.toFixed(3)}</span>
              )}
              <Badge variant="outline" className="text-xs">
                {field.crop_type}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
