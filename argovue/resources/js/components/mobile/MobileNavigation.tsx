import { useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { 
  Menu, 
  Home, 
  MapPin, 
  DollarSign, 
  AlertTriangle, 
  FileText, 
  User,
  Bell,
  Settings,
  LogOut,
  Satellite
} from 'lucide-react';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  organization?: {
    name: string;
  };
}

interface PageProps {
  auth: {
    user: User;
  };
}

interface MobileNavigationProps {
  currentPath?: string;
}

export default function MobileNavigation({ currentPath }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { auth } = usePage<PageProps>().props;
  const user = auth.user;

  const navigation = [
    { 
      name: 'Dashboard', 
      href: '/dashboard', 
      icon: Home, 
      description: 'Overview and statistics'
    },
    { 
      name: 'Fields', 
      href: '/fields', 
      icon: MapPin, 
      description: 'Manage your fields'
    },
    { 
      name: 'Subsidy Claims', 
      href: '/subsidy-claims', 
      icon: DollarSign, 
      description: 'Track subsidy applications',
      roles: ['government_admin', 'farmer', 'bank_analyst'] 
    },
    { 
      name: 'Alerts', 
      href: '/alerts', 
      icon: AlertTriangle, 
      description: 'Active notifications'
    },
    { 
      name: 'Reports', 
      href: '/reports', 
      icon: FileText, 
      description: 'Generate and view reports'
    },
  ];

  const filteredNavigation = navigation.filter(item => 
    !item.roles || item.roles.includes(user.role)
  );

  const isCurrentPath = (href: string) => {
    return currentPath === href || currentPath?.startsWith(href + '/');
  };

  const handleLogout = () => {
    window.location.href = '/logout';
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden sticky top-0 z-50 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="sm">
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80 p-0">
            <MobileSidebar 
              user={user} 
              navigation={filteredNavigation} 
              currentPath={currentPath}
              onNavigate={() => setIsOpen(false)}
              onLogout={handleLogout}
            />
          </SheetContent>
        </Sheet>

        {/* Logo */}
        <Link href="/dashboard" className="flex items-center space-x-2">
          <Satellite className="h-6 w-6 text-green-600" />
          <span className="text-lg font-bold text-gray-900">AgroVue</span>
        </Link>

        {/* Right side */}
        <div className="flex flex-1 justify-end">
          <Button variant="ghost" size="sm">
            <Bell className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Bottom Navigation for Mobile */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200">
        <div className="grid grid-cols-4 gap-1 p-2">
          {filteredNavigation.slice(0, 4).map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors ${
                isCurrentPath(item.href)
                  ? 'bg-green-50 text-green-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <item.icon className="h-5 w-5 mb-1" />
              <span className="text-xs font-medium">{item.name}</span>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
}

interface MobileSidebarProps {
  user: User;
  navigation: Array<{
    name: string;
    href: string;
    icon: any;
    description: string;
    roles?: string[];
  }>;
  currentPath?: string;
  onNavigate: () => void;
  onLogout: () => void;
}

function MobileSidebar({ user, navigation, currentPath, onNavigate, onLogout }: MobileSidebarProps) {
  const isCurrentPath = (href: string) => {
    return currentPath === href || currentPath?.startsWith(href + '/');
  };

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex h-16 items-center px-6 border-b">
        <Link href="/dashboard" className="flex items-center space-x-2" onClick={onNavigate}>
          <Satellite className="h-8 w-8 text-green-600" />
          <span className="text-xl font-bold text-gray-900">AgroVue</span>
        </Link>
      </div>

      {/* User Info */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 rounded-full bg-green-600 flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </p>
          </div>
        </div>
        {user.organization && (
          <Badge variant="outline" className="mt-2 text-xs">
            {user.organization.name}
          </Badge>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            onClick={onNavigate}
            className={`flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors ${
              isCurrentPath(item.href)
                ? 'bg-green-50 text-green-700 border-l-4 border-green-600'
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            <item.icon className="mr-3 h-5 w-5" />
            <div className="flex-1">
              <div>{item.name}</div>
              <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
            </div>
          </Link>
        ))}
      </nav>

      {/* Footer Actions */}
      <div className="border-t p-4 space-y-2">
        <Link 
          href="/profile" 
          onClick={onNavigate}
          className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg"
        >
          <Settings className="mr-3 h-4 w-4" />
          Settings
        </Link>
        <button 
          onClick={() => {
            onNavigate();
            onLogout();
          }}
          className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg w-full text-left"
        >
          <LogOut className="mr-3 h-4 w-4" />
          Sign out
        </button>
      </div>
    </div>
  );
}
