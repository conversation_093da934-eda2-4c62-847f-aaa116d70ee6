export interface User {
  id: number;
  name: string;
  email: string;
  role: 'government_admin' | 'ngo_coordinator' | 'farmer' | 'bank_analyst' | 'corporate';
  phone?: string;
  language: string;
  organization_id?: number;
  organization?: Organization;
  permissions?: string[];
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: number;
  name: string;
  type: 'government' | 'ngo' | 'bank' | 'corporate' | 'cooperative';
  registration_number?: string;
  description?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postal_code?: string;
  };
  website?: string;
  regions_covered?: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Field {
  id: number;
  name: string;
  field_id: string;
  owner_id: number;
  owner?: User;
  organization_id?: number;
  organization?: Organization;
  area_hectares: number;
  crop_type: 'rice' | 'maize' | 'cassava' | 'millet' | 'sorghum' | 'yam' | 'beans' | 'groundnut' | 'other';
  crop_variety?: string;
  coordinates: any; // GeoJSON
  center_latitude: number;
  center_longitude: number;
  state: string;
  lga: string;
  ward?: string;
  status: 'active' | 'inactive' | 'disputed' | 'verified';
  planting_date?: string;
  expected_harvest_date?: string;
  expected_yield_tons?: number;
  soil_data?: any;
  notes?: string;
  is_mapped: boolean;
  last_satellite_update?: string;
  latest_ndvi?: number;
  health_status?: 'excellent' | 'good' | 'fair' | 'poor' | 'unknown';
  satellite_data?: SatelliteData[];
  created_at: string;
  updated_at: string;
}

export interface SatelliteData {
  id: number;
  field_id: number;
  field?: Field;
  capture_date: string;
  satellite_source: string;
  ndvi_value?: number;
  evi_value?: number;
  savi_value?: number;
  cloud_coverage?: number;
  rainfall_mm?: number;
  temperature_celsius?: number;
  soil_moisture?: number;
  image_url?: string;
  thumbnail_url?: string;
  analysis_data?: any;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  anomaly_detected: boolean;
  anomaly_description?: string;
  vegetation_health?: 'excellent' | 'good' | 'fair' | 'poor' | 'unknown';
  created_at: string;
  updated_at: string;
}

export interface SubsidyClaim {
  id: number;
  claim_number: string;
  field_id: number;
  field?: Field;
  claimant_id: number;
  claimant?: User;
  reviewer_id?: number;
  reviewer?: User;
  program_name: string;
  subsidy_type: 'seeds' | 'fertilizer' | 'irrigation' | 'equipment' | 'cash' | 'other';
  claimed_amount: number;
  approved_amount?: number;
  claimed_area_hectares: number;
  verified_area_hectares?: number;
  status: 'pending' | 'under_review' | 'verified' | 'flagged' | 'approved' | 'rejected' | 'paid';
  claim_description: string;
  supporting_documents?: string[];
  application_date: string;
  review_date?: string;
  approval_date?: string;
  reviewer_notes?: string;
  fraud_risk_score?: number;
  fraud_risk_level?: 'high' | 'medium' | 'low' | 'very_low' | 'unknown';
  verification_data?: any;
  field_visit_required: boolean;
  field_visit_date?: string;
  field_agent_id?: number;
  field_agent?: User;
  field_visit_report?: any;
  status_color?: string;
  created_at: string;
  updated_at: string;
}

export interface Alert {
  id: number;
  title: string;
  description: string;
  type: 'drought' | 'flood' | 'pest' | 'disease' | 'fraud' | 'yield_anomaly' | 'weather' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved' | 'dismissed';
  field_id?: number;
  field?: Field;
  created_by: number;
  creator?: User;
  assigned_to?: number;
  assignee?: User;
  affected_regions?: string[];
  affected_fields?: number[];
  latitude?: number;
  longitude?: number;
  metadata?: any;
  alert_date: string;
  acknowledged_at?: string;
  resolved_at?: string;
  resolution_notes?: string;
  sms_sent: boolean;
  email_sent: boolean;
  notification_log?: any;
  severity_color?: string;
  status_color?: string;
  created_at: string;
  updated_at: string;
}

export interface Report {
  id: number;
  title: string;
  description?: string;
  type: 'yield_forecast' | 'compliance' | 'field_performance' | 'subsidy_analysis' | 'disaster_impact' | 'custom';
  generated_by: number;
  generator?: User;
  organization_id?: number;
  organization?: Organization;
  filters?: any;
  data?: any;
  file_path?: string;
  file_type?: string;
  file_size?: number;
  file_size_human?: string;
  period_start?: string;
  period_end?: string;
  regions_covered?: string[];
  status: 'generating' | 'completed' | 'failed' | 'expired';
  error_message?: string;
  generated_at?: string;
  expires_at?: string;
  download_count: number;
  status_color?: string;
  created_at: string;
  updated_at: string;
}

export interface DashboardData {
  totalFields: number;
  activeAlerts: number;
  recentReports: Report[];
  // Role-specific data
  pendingClaims?: number;
  flaggedClaims?: number;
  totalSubsidyAmount?: number;
  recentClaims?: SubsidyClaim[];
  criticalAlerts?: Alert[];
  affectedFields?: number;
  disasterAlerts?: Alert[];
  organizationFields?: number;
  myFields?: number;
  myAlerts?: Alert[];
  myClaims?: SubsidyClaim[];
  fieldHealth?: Array<{
    id: number;
    name: string;
    health: string;
    latest_ndvi?: number;
  }>;
  loanApplications?: number;
  highRiskClaims?: number;
  totalLoanValue?: number;
  portfolioHealth?: number;
  riskAnalysis?: Array<{
    risk_level: string;
    count: number;
  }>;
}

export interface PageProps {
  user: User;
  dashboardData?: DashboardData;
  fields?: Field[];
  field?: Field;
  subsidyClaims?: SubsidyClaim[];
  subsidyClaim?: SubsidyClaim;
  alerts?: Alert[];
  alert?: Alert;
  reports?: Report[];
  report?: Report;
  organizations?: Organization[];
  [key: string]: any;
}
