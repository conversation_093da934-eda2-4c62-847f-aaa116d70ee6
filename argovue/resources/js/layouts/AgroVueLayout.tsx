import { PropsWithChildren } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import { User } from '@/types/agrovue';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppHeader } from '@/components/app-header';
import { AppContent } from '@/components/app-content';
import { Breadcrumbs } from '@/components/breadcrumbs';
import { 
  BarChart3, 
  Map, 
  AlertTriangle, 
  FileText, 
  Wheat, 
  DollarSign,
  Settings,
  Users,
  MessageSquare
} from 'lucide-react';

interface AgroVueLayoutProps extends PropsWithChildren {
  title?: string;
  breadcrumbs?: Array<{ label: string; href?: string }>;
}

export default function AgroVueLayout({ children, title, breadcrumbs }: AgroVueLayoutProps) {
  const { user } = usePage<{ user: User }>().props;

  const getNavigationItems = () => {
    const baseItems = [
      {
        title: 'Dashboard',
        url: '/dashboard',
        icon: BarChart3,
        isActive: route().current('dashboard'),
      },
      {
        title: 'Field Monitoring',
        url: '/fields',
        icon: Map,
        isActive: route().current('fields.*'),
      },
      {
        title: 'Alerts',
        url: '/alerts',
        icon: AlertTriangle,
        isActive: route().current('alerts.*'),
      },
      {
        title: 'Reports',
        url: '/reports',
        icon: FileText,
        isActive: route().current('reports.*'),
      },
    ];

    // Role-specific navigation items
    const roleSpecificItems: Record<string, any[]> = {
      government_admin: [
        {
          title: 'Subsidy Claims',
          url: '/subsidy-claims',
          icon: DollarSign,
          isActive: route().current('subsidy-claims.*'),
        },
        {
          title: 'Organizations',
          url: '/organizations',
          icon: Users,
          isActive: route().current('organizations.*'),
        },
      ],
      bank_analyst: [
        {
          title: 'Loan Analysis',
          url: '/subsidy-claims',
          icon: DollarSign,
          isActive: route().current('subsidy-claims.*'),
        },
      ],
      farmer: [
        {
          title: 'My Fields',
          url: '/fields?owner=me',
          icon: Wheat,
          isActive: route().current('fields.*') && route().params.owner === 'me',
        },
        {
          title: 'My Claims',
          url: '/subsidy-claims?claimant=me',
          icon: DollarSign,
          isActive: route().current('subsidy-claims.*') && route().params.claimant === 'me',
        },
        {
          title: 'AI Advisory',
          url: '/advisory',
          icon: MessageSquare,
          isActive: route().current('advisory.*'),
        },
      ],
      ngo_coordinator: [
        {
          title: 'Disaster Monitoring',
          url: '/alerts?type=disaster',
          icon: AlertTriangle,
          isActive: route().current('alerts.*') && route().params.type === 'disaster',
        },
      ],
    };

    return [
      ...baseItems,
      ...(roleSpecificItems[user.role] || []),
      {
        title: 'Settings',
        url: '/settings',
        icon: Settings,
        isActive: route().current('settings.*'),
      },
    ];
  };

  return (
    <AppShell>
      <Head title={title ? `${title} - AgroVue` : 'AgroVue'} />
      
      <AppSidebar 
        user={user}
        navigation={getNavigationItems()}
      />
      
      <div className="flex flex-1 flex-col">
        <AppHeader user={user} />
        
        <AppContent>
          {breadcrumbs && (
            <div className="mb-6">
              <Breadcrumbs items={breadcrumbs} />
            </div>
          )}
          
          {children}
        </AppContent>
      </div>
    </AppShell>
  );
}

// Helper function to check current route (assuming Ziggy is available)
function route() {
  return {
    current: (name: string) => {
      // This would be implemented with Ziggy route helper
      return window.location.pathname.includes(name.replace('.*', '').replace('.', '/'));
    },
    params: {} as Record<string, any>,
  };
}
