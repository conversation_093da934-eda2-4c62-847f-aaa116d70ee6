<?php

namespace Database\Factories;

use App\Models\Field;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class FieldFactory extends Factory
{
    protected $model = Field::class;

    public function definition(): array
    {
        $cropTypes = ['rice', 'maize', 'cassava', 'millet', 'sorghum', 'yam', 'beans', 'groundnut'];
        $cropType = $this->faker->randomElement($cropTypes);
        
        $states = ['Lagos', 'Kano', 'Kaduna', 'Rivers', 'Oyo', 'Ogun', 'FCT'];
        $state = $this->faker->randomElement($states);
        
        $lgas = $this->getLgasForState($state);
        $lga = $this->faker->randomElement($lgas);
        
        // Generate realistic coordinates for Nigeria
        $latitude = $this->faker->randomFloat(6, 4.0, 14.0);
        $longitude = $this->faker->randomFloat(6, 2.5, 14.5);
        
        // Create a small polygon around the center point
        $offset = 0.01; // Small offset for polygon
        $coordinates = [
            'type' => 'Polygon',
            'coordinates' => [[
                [$longitude - $offset, $latitude - $offset],
                [$longitude + $offset, $latitude - $offset],
                [$longitude + $offset, $latitude + $offset],
                [$longitude - $offset, $latitude + $offset],
                [$longitude - $offset, $latitude - $offset], // Close the polygon
            ]]
        ];

        $areaHectares = $this->faker->randomFloat(2, 0.5, 100);
        $plantingDate = $this->faker->dateTimeBetween('-6 months', 'now');
        $harvestDate = (clone $plantingDate)->modify('+4 months');

        return [
            'name' => $this->generateFieldName($cropType),
            'field_id' => 'FLD-' . str_pad($this->faker->unique()->numberBetween(1, 999999), 6, '0', STR_PAD_LEFT),
            'owner_id' => User::factory(),
            'organization_id' => $this->faker->optional(0.3)->randomElement([null, Organization::factory()]),
            'area_hectares' => $areaHectares,
            'crop_type' => $cropType,
            'crop_variety' => $this->getCropVariety($cropType),
            'coordinates' => $coordinates,
            'center_latitude' => $latitude,
            'center_longitude' => $longitude,
            'state' => $state,
            'lga' => $lga,
            'ward' => $this->faker->optional(0.7)->word(),
            'status' => $this->faker->randomElement(['active', 'inactive', 'disputed', 'verified']),
            'planting_date' => $plantingDate,
            'expected_harvest_date' => $harvestDate,
            'expected_yield_tons' => $this->calculateExpectedYield($cropType, $areaHectares),
            'soil_data' => [
                'ph' => $this->faker->randomFloat(1, 5.5, 8.0),
                'nitrogen' => $this->faker->randomElement(['low', 'medium', 'high']),
                'phosphorus' => $this->faker->randomElement(['low', 'medium', 'high']),
                'potassium' => $this->faker->randomElement(['low', 'medium', 'high']),
                'organic_matter' => $this->faker->randomFloat(1, 1.0, 5.0),
            ],
            'notes' => $this->faker->optional(0.6)->sentence(10),
            'is_mapped' => true,
            'last_satellite_update' => $this->faker->optional(0.8)->dateTimeBetween('-30 days', 'now'),
            'health_status' => $this->faker->randomElement(['excellent', 'good', 'fair', 'poor', 'critical']),
            'latest_ndvi' => $this->faker->randomFloat(4, 0.1, 0.9),
        ];
    }

    private function generateFieldName(string $cropType): string
    {
        $adjectives = ['Green', 'Golden', 'Sunrise', 'Valley', 'River', 'Hill', 'Forest', 'Meadow'];
        $nouns = ['Farm', 'Estate', 'Plantation', 'Fields', 'Ranch', 'Gardens'];
        
        $adjective = $this->faker->randomElement($adjectives);
        $noun = $this->faker->randomElement($nouns);
        $crop = ucfirst($cropType);
        
        return "{$adjective} {$crop} {$noun}";
    }

    private function getCropVariety(string $cropType): string
    {
        $varieties = [
            'rice' => ['FARO 44', 'FARO 52', 'NERICA 1', 'NERICA 2', 'IR 841'],
            'maize' => ['Oba Super 2', 'Sammaz 15', 'Sammaz 17', 'BR 9943-DMRSR'],
            'cassava' => ['TMS 30572', 'TMS 4(2)1425', 'TMS 92/0326', 'TMS 98/0505'],
            'millet' => ['SOSAT C88', 'Ex-Borno', 'Gero', 'Maiwa'],
            'sorghum' => ['CSR-01', 'ICSV 400', 'Kaura', 'Farafara'],
            'yam' => ['TDr 89/02665', 'TDr 95/19177', 'Obiaoturugo', 'Nwopoko'],
            'beans' => ['IT90K-277-2', 'IT97K-499-35', 'Sampea 6', 'Sampea 7'],
            'groundnut' => ['SAMNUT 10', 'SAMNUT 11', 'SAMNUT 22', 'SAMNUT 23'],
        ];

        return $this->faker->randomElement($varieties[$cropType] ?? ['Local Variety']);
    }

    private function calculateExpectedYield(string $cropType, float $areaHectares): float
    {
        $yieldPerHectare = [
            'rice' => 3.5,
            'maize' => 2.8,
            'cassava' => 12.0,
            'millet' => 1.2,
            'sorghum' => 1.5,
            'yam' => 15.0,
            'beans' => 1.8,
            'groundnut' => 2.0,
        ];

        $baseYield = $yieldPerHectare[$cropType] ?? 2.0;
        $variation = $this->faker->randomFloat(2, 0.8, 1.2); // ±20% variation
        
        return round($areaHectares * $baseYield * $variation, 2);
    }

    private function getLgasForState(string $state): array
    {
        $lgasByState = [
            'Lagos' => ['Ikeja', 'Lagos Island', 'Surulere', 'Alimosho', 'Agege'],
            'Kano' => ['Kano Municipal', 'Nassarawa', 'Fagge', 'Dala', 'Gwale'],
            'Kaduna' => ['Kaduna North', 'Kaduna South', 'Chikun', 'Igabi', 'Zaria'],
            'Rivers' => ['Port Harcourt', 'Obio-Akpor', 'Eleme', 'Ikwerre', 'Emohua'],
            'Oyo' => ['Ibadan North', 'Ibadan South-West', 'Egbeda', 'Akinyele', 'Lagelu'],
            'Ogun' => ['Abeokuta North', 'Abeokuta South', 'Ado-Odo/Ota', 'Ewekoro', 'Ifo'],
            'FCT' => ['Abuja Municipal', 'Gwagwalada', 'Kuje', 'Bwari', 'Kwali'],
        ];

        return $lgasByState[$state] ?? ['Municipal'];
    }

    public function withOwner(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'owner_id' => $user->id,
        ]);
    }

    public function withOrganization(Organization $organization): static
    {
        return $this->state(fn (array $attributes) => [
            'organization_id' => $organization->id,
        ]);
    }

    public function rice(): static
    {
        return $this->state(fn (array $attributes) => [
            'crop_type' => 'rice',
            'crop_variety' => 'FARO 44',
        ]);
    }

    public function maize(): static
    {
        return $this->state(fn (array $attributes) => [
            'crop_type' => 'maize',
            'crop_variety' => 'Oba Super 2',
        ]);
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'verified',
        ]);
    }
}
