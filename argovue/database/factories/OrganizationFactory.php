<?php

namespace Database\Factories;

use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrganizationFactory extends Factory
{
    protected $model = Organization::class;

    public function definition(): array
    {
        $types = ['government', 'ngo', 'bank', 'corporate'];
        $type = $this->faker->randomElement($types);
        
        return [
            'name' => $this->getOrganizationName($type),
            'type' => $type,
            'registration_number' => strtoupper($type) . '-' . $this->faker->unique()->numberBetween(1000, 9999),
            'description' => $this->faker->sentence(10),
            'contact_email' => $this->faker->companyEmail(),
            'contact_phone' => '+234-' . $this->faker->randomElement(['1', '8', '9']) . '-' . $this->faker->numerify('###-####'),
            'address' => [
                'street' => $this->faker->streetAddress(),
                'city' => $this->faker->randomElement(['Lagos', 'Abuja', 'Kano', 'Port Harcourt', 'Ibadan']),
                'state' => $this->faker->randomElement(['Lagos', 'FCT', 'Kano', 'Rivers', 'Oyo']),
                'country' => 'Nigeria',
                'postal_code' => $this->faker->numerify('######'),
            ],
            'regions_covered' => $this->faker->randomElements(['Lagos', 'FCT', 'Kano', 'Rivers', 'Oyo', 'Kaduna', 'Ogun'], $this->faker->numberBetween(1, 4)),
            'is_active' => true,
        ];
    }

    private function getOrganizationName(string $type): string
    {
        switch ($type) {
            case 'government':
                return $this->faker->randomElement([
                    'Federal Ministry of Agriculture',
                    'State Agricultural Development Program',
                    'National Agricultural Insurance Corporation',
                    'Agricultural Development Bank',
                ]);
            case 'ngo':
                return $this->faker->randomElement([
                    'Agricultural Development Foundation',
                    'Rural Farmers Association',
                    'Sustainable Agriculture Initiative',
                    'Food Security Network',
                ]);
            case 'bank':
                return $this->faker->randomElement([
                    'Agricultural Development Bank',
                    'Rural Finance Corporation',
                    'Farmers Credit Union',
                    'Microfinance Bank',
                ]);
            case 'corporate':
                return $this->faker->randomElement([
                    'AgriTech Solutions Ltd',
                    'Farm Supply Corporation',
                    'Agricultural Equipment Company',
                    'Crop Processing Industries',
                ]);
            default:
                return $this->faker->company();
        }
    }

    public function government(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'government',
            'name' => 'Federal Ministry of Agriculture',
        ]);
    }

    public function ngo(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'ngo',
            'name' => 'Agricultural Development Foundation',
        ]);
    }

    public function bank(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'bank',
            'name' => 'Agricultural Development Bank',
        ]);
    }

    public function corporate(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'corporate',
            'name' => 'AgriTech Solutions Ltd',
        ]);
    }
}
