<?php

namespace Database\Factories;

use App\Models\SatelliteData;
use App\Models\Field;
use Illuminate\Database\Eloquent\Factories\Factory;

class SatelliteDataFactory extends Factory
{
    protected $model = SatelliteData::class;

    public function definition(): array
    {
        $ndviAverage = $this->faker->randomFloat(4, 0.1, 0.9);
        $ndviVariation = 0.1;
        
        return [
            'field_id' => Field::factory(),
            'satellite_source' => $this->faker->randomElement(['sentinel-2', 'landsat-8', 'landsat-9', 'modis']),
            'capture_date' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'cloud_coverage' => $this->faker->randomFloat(1, 0, 30),
            'ndvi_average' => $ndviAverage,
            'ndvi_min' => max(0, $ndviAverage - $ndviVariation),
            'ndvi_max' => min(1, $ndviAverage + $ndviVariation),
            'ndvi_std' => $this->faker->randomFloat(4, 0.01, 0.15),
            'evi_average' => $this->faker->optional(0.7)->randomFloat(4, 0.1, 0.8),
            'savi_average' => $this->faker->optional(0.7)->randomFloat(4, 0.1, 0.7),
            'vegetation_health' => $this->getVegetationHealth($ndviAverage),
            'change_detection' => $this->faker->optional(0.6)->randomElement([
                [
                    'previous_ndvi' => $this->faker->randomFloat(4, 0.1, 0.9),
                    'change_percentage' => $this->faker->randomFloat(2, -20, 20),
                    'change_type' => $this->faker->randomElement(['improvement', 'decline', 'stable']),
                ],
            ]),
            'anomalies_detected' => $this->faker->optional(0.3)->randomElements([
                'vegetation_decline',
                'vegetation_surge',
                'severe_vegetation_loss',
                'high_vegetation_variability',
            ], $this->faker->numberBetween(0, 2)),
            'image_url' => $this->faker->optional(0.5)->imageUrl(640, 480, 'nature'),
            'metadata' => [
                'resolution' => $this->faker->randomElement(['10m', '20m', '30m']),
                'bands_used' => $this->faker->randomElements(['B2', 'B3', 'B4', 'B8', 'B11', 'B12'], $this->faker->numberBetween(2, 4)),
                'processing_level' => $this->faker->randomElement(['L1C', 'L2A', 'L1T']),
                'scene_id' => $this->generateSceneId(),
            ],
            'processed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    private function getVegetationHealth(float $ndvi): string
    {
        if ($ndvi >= 0.7) return 'excellent';
        if ($ndvi >= 0.5) return 'good';
        if ($ndvi >= 0.3) return 'fair';
        if ($ndvi >= 0.1) return 'poor';
        return 'critical';
    }

    private function generateSceneId(): string
    {
        $sources = [
            'sentinel-2' => 'S2A_MSIL1C_' . $this->faker->date('Ymd') . 'T' . $this->faker->time('His') . '_N0204_R' . $this->faker->numerify('###') . '_T' . $this->faker->numerify('##') . 'MKV_' . $this->faker->date('Ymd') . 'T' . $this->faker->time('His'),
            'landsat-8' => 'LC08_L1TP_' . $this->faker->numerify('######') . '_' . $this->faker->date('Ymd') . '_' . $this->faker->date('Ymd') . '_01_T1',
            'landsat-9' => 'LC09_L1TP_' . $this->faker->numerify('######') . '_' . $this->faker->date('Ymd') . '_' . $this->faker->date('Ymd') . '_01_T1',
            'modis' => 'MOD13Q1.A' . $this->faker->date('Y') . $this->faker->numerify('###') . '.h' . $this->faker->numerify('##') . 'v' . $this->faker->numerify('##') . '.006.' . $this->faker->date('Ymd') . $this->faker->numerify('######'),
        ];

        $source = $this->faker->randomElement(array_keys($sources));
        return $sources[$source];
    }

    public function sentinel2(): static
    {
        return $this->state(fn (array $attributes) => [
            'satellite_source' => 'sentinel-2',
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'resolution' => '10m',
                'processing_level' => 'L2A',
            ]),
        ]);
    }

    public function landsat8(): static
    {
        return $this->state(fn (array $attributes) => [
            'satellite_source' => 'landsat-8',
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'resolution' => '30m',
                'processing_level' => 'L1T',
            ]),
        ]);
    }

    public function excellent(): static
    {
        return $this->state(fn (array $attributes) => [
            'ndvi_average' => $this->faker->randomFloat(4, 0.7, 0.9),
            'vegetation_health' => 'excellent',
        ]);
    }

    public function good(): static
    {
        return $this->state(fn (array $attributes) => [
            'ndvi_average' => $this->faker->randomFloat(4, 0.5, 0.69),
            'vegetation_health' => 'good',
        ]);
    }

    public function poor(): static
    {
        return $this->state(fn (array $attributes) => [
            'ndvi_average' => $this->faker->randomFloat(4, 0.1, 0.29),
            'vegetation_health' => 'poor',
        ]);
    }

    public function critical(): static
    {
        return $this->state(fn (array $attributes) => [
            'ndvi_average' => $this->faker->randomFloat(4, 0.01, 0.09),
            'vegetation_health' => 'critical',
            'anomalies_detected' => ['severe_vegetation_loss'],
        ]);
    }

    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'capture_date' => $this->faker->dateTimeBetween('-7 days', 'now'),
            'processed_at' => $this->faker->dateTimeBetween('-3 days', 'now'),
        ]);
    }

    public function withLowCloudCoverage(): static
    {
        return $this->state(fn (array $attributes) => [
            'cloud_coverage' => $this->faker->randomFloat(1, 0, 10),
        ]);
    }

    public function withHighCloudCoverage(): static
    {
        return $this->state(fn (array $attributes) => [
            'cloud_coverage' => $this->faker->randomFloat(1, 50, 90),
        ]);
    }

    public function withAnomalies(): static
    {
        return $this->state(fn (array $attributes) => [
            'anomalies_detected' => $this->faker->randomElements([
                'vegetation_decline',
                'vegetation_surge',
                'severe_vegetation_loss',
                'high_vegetation_variability',
            ], $this->faker->numberBetween(1, 3)),
        ]);
    }

    public function withChangeDetection(string $changeType = null): static
    {
        $changeType = $changeType ?? $this->faker->randomElement(['improvement', 'decline', 'stable']);
        
        $changePercentage = match($changeType) {
            'improvement' => $this->faker->randomFloat(2, 5, 25),
            'decline' => $this->faker->randomFloat(2, -25, -5),
            'stable' => $this->faker->randomFloat(2, -4, 4),
        };

        return $this->state(fn (array $attributes) => [
            'change_detection' => [
                'previous_ndvi' => $this->faker->randomFloat(4, 0.1, 0.9),
                'change_percentage' => $changePercentage,
                'change_type' => $changeType,
            ],
        ]);
    }
}
