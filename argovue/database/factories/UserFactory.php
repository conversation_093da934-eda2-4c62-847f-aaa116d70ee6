<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $role = fake()->randomElement(['farmer', 'government_admin', 'ngo_coordinator', 'bank_analyst', 'corporate']);

        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'role' => $role,
            'phone' => '+234-' . fake()->randomElement(['8', '9', '7']) . '-' . fake()->numerify('###-####'),
            'language' => 'en',
            'organization_id' => fake()->optional(0.6)->randomElement([1, 2, 3, 4, 5]),
            'permissions' => $this->getPermissionsForRole($role),
            'is_active' => true,
            'remember_token' => Str::random(10),
        ];
    }

    private function getPermissionsForRole(string $role): array
    {
        return match($role) {
            'government_admin' => ['view_all_fields', 'manage_subsidies', 'generate_reports', 'approve_claims'],
            'farmer' => ['manage_own_fields', 'apply_subsidies', 'view_own_data'],
            'ngo_coordinator' => ['view_disaster_alerts', 'coordinate_relief', 'generate_impact_reports'],
            'bank_analyst' => ['assess_loan_risk', 'verify_activities', 'view_financial_data'],
            'corporate' => ['view_supply_chain', 'audit_compliance', 'generate_sustainability_reports'],
            default => ['view_own_data'],
        };
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    public function farmer(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'farmer',
            'permissions' => ['manage_own_fields', 'apply_subsidies', 'view_own_data'],
            'organization_id' => null,
        ]);
    }

    public function governmentAdmin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'government_admin',
            'permissions' => ['view_all_fields', 'manage_subsidies', 'generate_reports', 'approve_claims'],
        ]);
    }

    public function ngoCoordinator(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'ngo_coordinator',
            'permissions' => ['view_disaster_alerts', 'coordinate_relief', 'generate_impact_reports'],
        ]);
    }

    public function bankAnalyst(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'bank_analyst',
            'permissions' => ['assess_loan_risk', 'verify_activities', 'view_financial_data'],
        ]);
    }

    public function corporate(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'corporate',
            'permissions' => ['view_supply_chain', 'audit_compliance', 'generate_sustainability_reports'],
        ]);
    }
}
