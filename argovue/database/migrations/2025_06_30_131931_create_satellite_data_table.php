<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('satellite_data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('field_id')->constrained()->onDelete('cascade');
            $table->date('capture_date');
            $table->string('satellite_source')->default('sentinel-2'); // sentinel-2, landsat-8, etc.
            $table->decimal('ndvi_value', 5, 4)->nullable(); // Normalized Difference Vegetation Index
            $table->decimal('evi_value', 5, 4)->nullable(); // Enhanced Vegetation Index
            $table->decimal('savi_value', 5, 4)->nullable(); // Soil Adjusted Vegetation Index
            $table->decimal('cloud_coverage', 5, 2)->nullable(); // Percentage
            $table->decimal('rainfall_mm', 8, 2)->nullable();
            $table->decimal('temperature_celsius', 5, 2)->nullable();
            $table->decimal('soil_moisture', 5, 4)->nullable();
            $table->string('image_url')->nullable(); // URL to satellite image
            $table->string('thumbnail_url')->nullable();
            $table->json('analysis_data')->nullable(); // Additional analysis results
            $table->enum('quality', ['excellent', 'good', 'fair', 'poor'])->default('good');
            $table->boolean('anomaly_detected')->default(false);
            $table->text('anomaly_description')->nullable();
            $table->timestamps();

            $table->index(['field_id', 'capture_date']);
            $table->index(['capture_date', 'quality']);
            $table->index(['ndvi_value', 'anomaly_detected']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('satellite_data');
    }
};
