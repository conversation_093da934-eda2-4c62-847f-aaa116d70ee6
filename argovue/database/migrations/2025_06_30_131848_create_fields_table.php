<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('field_id')->unique(); // Unique identifier for the field
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('area_hectares', 10, 4);
            $table->enum('crop_type', ['rice', 'maize', 'cassava', 'millet', 'sorghum', 'yam', 'beans', 'groundnut', 'other']);
            $table->string('crop_variety')->nullable();
            $table->json('coordinates'); // GeoJSON polygon
            $table->decimal('center_latitude', 10, 8);
            $table->decimal('center_longitude', 11, 8);
            $table->string('state');
            $table->string('lga'); // Local Government Area
            $table->string('ward')->nullable();
            $table->enum('status', ['active', 'inactive', 'disputed', 'verified'])->default('active');
            $table->date('planting_date')->nullable();
            $table->date('expected_harvest_date')->nullable();
            $table->decimal('expected_yield_tons', 8, 2)->nullable();
            $table->json('soil_data')->nullable(); // pH, nutrients, etc.
            $table->text('notes')->nullable();
            $table->boolean('is_mapped')->default(false);
            $table->timestamp('last_satellite_update')->nullable();
            $table->timestamps();

            $table->index(['state', 'lga']);
            $table->index(['crop_type', 'status']);
            $table->index(['owner_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
