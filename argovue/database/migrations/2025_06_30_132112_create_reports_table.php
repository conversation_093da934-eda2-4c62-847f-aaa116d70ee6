<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['yield_forecast', 'compliance', 'field_performance', 'subsidy_analysis', 'disaster_impact', 'custom']);
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->json('filters')->nullable(); // Report generation filters
            $table->json('data')->nullable(); // Report data/results
            $table->string('file_path')->nullable(); // Path to generated file (PDF, CSV, etc.)
            $table->string('file_type')->nullable(); // pdf, csv, xlsx, shapefile
            $table->integer('file_size')->nullable(); // File size in bytes
            $table->date('period_start')->nullable();
            $table->date('period_end')->nullable();
            $table->json('regions_covered')->nullable(); // States/LGAs covered
            $table->enum('status', ['generating', 'completed', 'failed', 'expired'])->default('generating');
            $table->text('error_message')->nullable();
            $table->timestamp('generated_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->integer('download_count')->default(0);
            $table->timestamps();

            $table->index(['type', 'status']);
            $table->index(['generated_by', 'created_at']);
            $table->index(['organization_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
