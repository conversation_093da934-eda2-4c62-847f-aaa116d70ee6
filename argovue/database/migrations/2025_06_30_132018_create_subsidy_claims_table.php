<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subsidy_claims', function (Blueprint $table) {
            $table->id();
            $table->string('claim_number')->unique();
            $table->foreignId('field_id')->constrained()->onDelete('cascade');
            $table->foreignId('claimant_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('reviewer_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('program_name'); // e.g., "Anchor Borrowers Programme"
            $table->enum('subsidy_type', ['seeds', 'fertilizer', 'irrigation', 'equipment', 'cash', 'other']);
            $table->decimal('claimed_amount', 12, 2);
            $table->decimal('approved_amount', 12, 2)->nullable();
            $table->decimal('claimed_area_hectares', 10, 4);
            $table->decimal('verified_area_hectares', 10, 4)->nullable();
            $table->enum('status', ['pending', 'under_review', 'verified', 'flagged', 'approved', 'rejected', 'paid'])->default('pending');
            $table->text('claim_description');
            $table->json('supporting_documents')->nullable(); // URLs to uploaded documents
            $table->date('application_date');
            $table->date('review_date')->nullable();
            $table->date('approval_date')->nullable();
            $table->text('reviewer_notes')->nullable();
            $table->decimal('fraud_risk_score', 5, 2)->nullable(); // 0-100 scale
            $table->json('verification_data')->nullable(); // Satellite verification results
            $table->boolean('field_visit_required')->default(false);
            $table->date('field_visit_date')->nullable();
            $table->foreignId('field_agent_id')->nullable()->constrained('users')->onDelete('set null');
            $table->json('field_visit_report')->nullable();
            $table->timestamps();

            $table->index(['status', 'application_date']);
            $table->index(['claimant_id', 'status']);
            $table->index(['fraud_risk_score', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subsidy_claims');
    }
};
