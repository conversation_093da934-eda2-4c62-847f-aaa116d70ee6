<?php

namespace Database\Seeders;

use App\Models\Organization;
use App\Models\User;
use App\Models\Field;
use App\Models\SatelliteData;
use App\Models\SubsidyClaim;
use App\Models\Alert;
use App\Models\Report;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AgroVueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Organizations
        $govOrg = Organization::create([
            'name' => 'Federal Ministry of Agriculture',
            'type' => 'government',
            'registration_number' => 'FMA-001',
            'description' => 'Federal government agricultural ministry',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+234-9-123-4567',
            'address' => [
                'street' => 'Federal Secretariat Complex',
                'city' => 'Abuja',
                'state' => 'FCT',
                'country' => 'Nigeria',
                'postal_code' => '900001'
            ],
            'regions_covered' => ['FCT', 'Lagos', 'Kano', 'Rivers'],
            'is_active' => true,
        ]);

        $ngoOrg = Organization::create([
            'name' => 'Agricultural Development NGO',
            'type' => 'ngo',
            'registration_number' => 'NGO-002',
            'description' => 'Supporting smallholder farmers across Nigeria',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+234-1-234-5678',
            'address' => [
                'street' => '123 Development Street',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'postal_code' => '100001'
            ],
            'regions_covered' => ['Lagos', 'Ogun', 'Oyo'],
            'is_active' => true,
        ]);

        $bankOrg = Organization::create([
            'name' => 'Agricultural Development Bank',
            'type' => 'bank',
            'registration_number' => 'ADB-003',
            'description' => 'Specialized agricultural financing institution',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+234-1-345-6789',
            'address' => [
                'street' => '456 Banking Avenue',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'postal_code' => '100002'
            ],
            'regions_covered' => ['Lagos', 'Kano', 'Rivers', 'FCT'],
            'is_active' => true,
        ]);

        // Create Users
        $adminUser = User::create([
            'name' => 'Government Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'government_admin',
            'phone' => '+234-9-123-4567',
            'language' => 'en',
            'organization_id' => $govOrg->id,
            'permissions' => ['view_all_fields', 'manage_subsidies', 'generate_reports'],
            'is_active' => true,
        ]);

        $ngoUser = User::create([
            'name' => 'NGO Coordinator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'ngo_coordinator',
            'phone' => '+234-1-234-5678',
            'language' => 'en',
            'organization_id' => $ngoOrg->id,
            'permissions' => ['view_disaster_alerts', 'coordinate_relief'],
            'is_active' => true,
        ]);

        $farmerUser = User::create([
            'name' => 'John Farmer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'farmer',
            'phone' => '+234-8-123-4567',
            'language' => 'en',
            'organization_id' => null,
            'permissions' => ['manage_own_fields', 'apply_subsidies'],
            'is_active' => true,
        ]);

        $bankUser = User::create([
            'name' => 'Bank Analyst',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'bank_analyst',
            'phone' => '+234-1-345-6789',
            'language' => 'en',
            'organization_id' => $bankOrg->id,
            'permissions' => ['assess_loan_risk', 'verify_activities'],
            'is_active' => true,
        ]);

        // Create Fields
        $field1 = Field::create([
            'name' => 'Sunrise Rice Farm',
            'field_id' => 'FLD-001',
            'owner_id' => $farmerUser->id,
            'organization_id' => null,
            'area_hectares' => 25.5,
            'crop_type' => 'rice',
            'crop_variety' => 'FARO 44',
            'coordinates' => [
                'type' => 'Polygon',
                'coordinates' => [[[7.4, 9.1], [7.5, 9.1], [7.5, 9.2], [7.4, 9.2], [7.4, 9.1]]]
            ],
            'center_latitude' => 9.15,
            'center_longitude' => 7.45,
            'state' => 'Kaduna',
            'lga' => 'Zaria',
            'ward' => 'Sabon Gari',
            'status' => 'active',
            'planting_date' => '2024-06-15',
            'expected_harvest_date' => '2024-10-15',
            'expected_yield_tons' => 76.5,
            'soil_data' => [
                'ph' => 6.5,
                'nitrogen' => 'medium',
                'phosphorus' => 'high',
                'potassium' => 'medium'
            ],
            'notes' => 'Well-irrigated field with good drainage',
            'is_mapped' => true,
            'last_satellite_update' => now(),
        ]);

        $field2 = Field::create([
            'name' => 'Golden Maize Estate',
            'field_id' => 'FLD-002',
            'owner_id' => $farmerUser->id,
            'organization_id' => null,
            'area_hectares' => 15.0,
            'crop_type' => 'maize',
            'crop_variety' => 'Oba Super 2',
            'coordinates' => [
                'type' => 'Polygon',
                'coordinates' => [[[7.3, 9.0], [7.4, 9.0], [7.4, 9.1], [7.3, 9.1], [7.3, 9.0]]]
            ],
            'center_latitude' => 9.05,
            'center_longitude' => 7.35,
            'state' => 'Kaduna',
            'lga' => 'Zaria',
            'ward' => 'Tudun Wada',
            'status' => 'active',
            'planting_date' => '2024-05-01',
            'expected_harvest_date' => '2024-08-30',
            'expected_yield_tons' => 45.0,
            'soil_data' => [
                'ph' => 6.8,
                'nitrogen' => 'high',
                'phosphorus' => 'medium',
                'potassium' => 'high'
            ],
            'notes' => 'Rainfed cultivation with supplementary irrigation',
            'is_mapped' => true,
            'last_satellite_update' => now(),
        ]);
    }
}
