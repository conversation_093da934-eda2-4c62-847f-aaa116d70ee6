import{r as f,j as L,t as Sn,U as ne}from"./app-VriuOT-w.js";import{P as te,d as Cn,R as An,r as Rn}from"./index-C_pbH-b0.js";import{u as Z,S as mt}from"./button-DmNtpphn.js";function Ie(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Qo(e,t){const n=f.createContext(t),r=i=>{const{children:s,...c}=i,u=f.useMemo(()=>c,Object.values(c));return L.jsx(n.Provider,{value:u,children:s})};r.displayName=e+"Provider";function o(i){const s=f.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function kt(e,t=[]){let n=[];function r(i,s){const c=f.createContext(s),u=n.length;n=[...n,s];const a=d=>{var y;const{scope:p,children:h,...g}=d,l=((y=p==null?void 0:p[e])==null?void 0:y[u])||c,v=f.useMemo(()=>g,Object.values(g));return L.jsx(l.Provider,{value:v,children:h})};a.displayName=i+"Provider";function m(d,p){var l;const h=((l=p==null?void 0:p[e])==null?void 0:l[u])||c,g=f.useContext(h);if(g)return g;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[a,m]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(c){const u=(c==null?void 0:c[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...c,[e]:u}}),[c,u])}};return o.scopeName=e,[r,Pn(o,...t)]}function Pn(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((c,{useScope:u,scopeName:a})=>{const d=u(i)[`__scope${a}`];return{...c,...d}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var re=globalThis!=null&&globalThis.document?f.useLayoutEffect:()=>{},On=Sn.useId||(()=>{}),Tn=0;function Jo(e){const[t,n]=f.useState(On());return re(()=>{n(r=>r??String(Tn++))},[e]),e||(t?`radix-${t}`:"")}function G(e){const t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function ei({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Mn({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,c=G(n),u=f.useCallback(a=>{if(i){const d=typeof a=="function"?a(e):a;d!==e&&c(d)}else o(a)},[i,e,o,c]);return[s,u]}function Mn({defaultProp:e,onChange:t}){const n=f.useState(e),[r]=n,o=f.useRef(r),i=G(t);return f.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function Nn(e,t=globalThis==null?void 0:globalThis.document){const n=G(e);f.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ln="DismissableLayer",Ye="dismissableLayer.update",Dn="dismissableLayer.pointerDownOutside",kn="dismissableLayer.focusOutside",ht,Ft=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Fn=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:c,...u}=e,a=f.useContext(Ft),[m,d]=f.useState(null),p=(m==null?void 0:m.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=f.useState({}),g=Z(t,S=>d(S)),l=Array.from(a.layers),[v]=[...a.layersWithOutsidePointerEventsDisabled].slice(-1),y=l.indexOf(v),w=m?l.indexOf(m):-1,b=a.layersWithOutsidePointerEventsDisabled.size>0,x=w>=y,E=_n(S=>{const R=S.target,T=[...a.branches].some(P=>P.contains(R));!x||T||(o==null||o(S),s==null||s(S),S.defaultPrevented||c==null||c())},p),C=Bn(S=>{const R=S.target;[...a.branches].some(P=>P.contains(R))||(i==null||i(S),s==null||s(S),S.defaultPrevented||c==null||c())},p);return Nn(S=>{w===a.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},p),f.useEffect(()=>{if(m)return n&&(a.layersWithOutsidePointerEventsDisabled.size===0&&(ht=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),a.layersWithOutsidePointerEventsDisabled.add(m)),a.layers.add(m),pt(),()=>{n&&a.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=ht)}},[m,p,n,a]),f.useEffect(()=>()=>{m&&(a.layers.delete(m),a.layersWithOutsidePointerEventsDisabled.delete(m),pt())},[m,a]),f.useEffect(()=>{const S=()=>h({});return document.addEventListener(Ye,S),()=>document.removeEventListener(Ye,S)},[]),L.jsx(te.div,{...u,ref:g,style:{pointerEvents:b?x?"auto":"none":void 0,...e.style},onFocusCapture:Ie(e.onFocusCapture,C.onFocusCapture),onBlurCapture:Ie(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:Ie(e.onPointerDownCapture,E.onPointerDownCapture)})});Fn.displayName=Ln;var In="DismissableLayerBranch",Wn=f.forwardRef((e,t)=>{const n=f.useContext(Ft),r=f.useRef(null),o=Z(t,r);return f.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),L.jsx(te.div,{...e,ref:o})});Wn.displayName=In;function _n(e,t=globalThis==null?void 0:globalThis.document){const n=G(e),r=f.useRef(!1),o=f.useRef(()=>{});return f.useEffect(()=>{const i=c=>{if(c.target&&!r.current){let u=function(){It(Dn,n,a,{discrete:!0})};const a={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Bn(e,t=globalThis==null?void 0:globalThis.document){const n=G(e),r=f.useRef(!1);return f.useEffect(()=>{const o=i=>{i.target&&!r.current&&It(kn,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function pt(){const e=new CustomEvent(Ye);document.dispatchEvent(e)}function It(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Cn(o,i):o.dispatchEvent(i)}var We="focusScope.autoFocusOnMount",_e="focusScope.autoFocusOnUnmount",vt={bubbles:!1,cancelable:!0},$n="FocusScope",jn=f.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[c,u]=f.useState(null),a=G(o),m=G(i),d=f.useRef(null),p=Z(t,l=>u(l)),h=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let l=function(b){if(h.paused||!c)return;const x=b.target;c.contains(x)?d.current=x:q(d.current,{select:!0})},v=function(b){if(h.paused||!c)return;const x=b.relatedTarget;x!==null&&(c.contains(x)||q(d.current,{select:!0}))},y=function(b){if(document.activeElement===document.body)for(const E of b)E.removedNodes.length>0&&q(c)};document.addEventListener("focusin",l),document.addEventListener("focusout",v);const w=new MutationObserver(y);return c&&w.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",l),document.removeEventListener("focusout",v),w.disconnect()}}},[r,c,h.paused]),f.useEffect(()=>{if(c){yt.add(h);const l=document.activeElement;if(!c.contains(l)){const y=new CustomEvent(We,vt);c.addEventListener(We,a),c.dispatchEvent(y),y.defaultPrevented||(Hn(Xn(Wt(c)),{select:!0}),document.activeElement===l&&q(c))}return()=>{c.removeEventListener(We,a),setTimeout(()=>{const y=new CustomEvent(_e,vt);c.addEventListener(_e,m),c.dispatchEvent(y),y.defaultPrevented||q(l??document.body,{select:!0}),c.removeEventListener(_e,m),yt.remove(h)},0)}}},[c,a,m,h]);const g=f.useCallback(l=>{if(!n&&!r||h.paused)return;const v=l.key==="Tab"&&!l.altKey&&!l.ctrlKey&&!l.metaKey,y=document.activeElement;if(v&&y){const w=l.currentTarget,[b,x]=Un(w);b&&x?!l.shiftKey&&y===x?(l.preventDefault(),n&&q(b,{select:!0})):l.shiftKey&&y===b&&(l.preventDefault(),n&&q(x,{select:!0})):y===w&&l.preventDefault()}},[n,r,h.paused]);return L.jsx(te.div,{tabIndex:-1,...s,ref:p,onKeyDown:g})});jn.displayName=$n;function Hn(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(q(r,{select:t}),document.activeElement!==n)return}function Un(e){const t=Wt(e),n=gt(t,e),r=gt(t.reverse(),e);return[n,r]}function Wt(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function gt(e,t){for(const n of e)if(!zn(n,{upTo:t}))return n}function zn(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Vn(e){return e instanceof HTMLInputElement&&"select"in e}function q(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Vn(e)&&t&&e.select()}}var yt=Yn();function Yn(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=wt(e,t),e.unshift(t)},remove(t){var n;e=wt(e,t),(n=e[0])==null||n.resume()}}}function wt(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Xn(e){return e.filter(t=>t.tagName!=="A")}var Kn="Portal",qn=f.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,i]=f.useState(!1);re(()=>i(!0),[]);const s=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return s?An.createPortal(L.jsx(te.div,{...r,ref:t}),s):null});qn.displayName=Kn;function Zn(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var Gn=e=>{const{present:t,children:n}=e,r=Qn(t),o=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),i=Z(r.ref,Jn(o));return typeof n=="function"||r.isPresent?f.cloneElement(o,{ref:i}):null};Gn.displayName="Presence";function Qn(e){const[t,n]=f.useState(),r=f.useRef({}),o=f.useRef(e),i=f.useRef("none"),s=e?"mounted":"unmounted",[c,u]=Zn(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const a=we(r.current);i.current=c==="mounted"?a:"none"},[c]),re(()=>{const a=r.current,m=o.current;if(m!==e){const p=i.current,h=we(a);e?u("MOUNT"):h==="none"||(a==null?void 0:a.display)==="none"?u("UNMOUNT"):u(m&&p!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),re(()=>{if(t){let a;const m=t.ownerDocument.defaultView??window,d=h=>{const l=we(r.current).includes(h.animationName);if(h.target===t&&l&&(u("ANIMATION_END"),!o.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",a=m.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},p=h=>{h.target===t&&(i.current=we(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{m.clearTimeout(a),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:f.useCallback(a=>{a&&(r.current=getComputedStyle(a)),n(a)},[])}}function we(e){return(e==null?void 0:e.animationName)||"none"}function Jn(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Be=0;function ti(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??xt()),document.body.insertAdjacentElement("beforeend",e[1]??xt()),Be++,()=>{Be===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Be--}},[])}function xt(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var U=function(){return U=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},U.apply(this,arguments)};function _t(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function er(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Ae="right-scroll-bar-position",Re="width-before-scroll-bar",tr="with-scroll-bars-hidden",nr="--removed-body-scroll-bar-size";function $e(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function rr(e,t){var n=f.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var or=typeof window<"u"?f.useLayoutEffect:f.useEffect,bt=new WeakMap;function ir(e,t){var n=rr(null,function(r){return e.forEach(function(o){return $e(o,r)})});return or(function(){var r=bt.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(c){i.has(c)||$e(c,null)}),i.forEach(function(c){o.has(c)||$e(c,s)})}bt.set(n,e)},[e]),n}function sr(e){return e}function cr(e,t){t===void 0&&(t=sr);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(c){return c!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(c){return i(c)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var c=n;n=[],c.forEach(i),s=n}var u=function(){var m=s;s=[],m.forEach(i)},a=function(){return Promise.resolve().then(u)};a(),n={push:function(m){s.push(m),a()},filter:function(m){return s=s.filter(m),n}}}};return o}function ar(e){e===void 0&&(e={});var t=cr(null);return t.options=U({async:!0,ssr:!1},e),t}var Bt=function(e){var t=e.sideCar,n=_t(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return f.createElement(r,U({},n))};Bt.isSideCarExport=!0;function lr(e,t){return e.useMedium(t),Bt}var $t=ar(),je=function(){},Le=f.forwardRef(function(e,t){var n=f.useRef(null),r=f.useState({onScrollCapture:je,onWheelCapture:je,onTouchMoveCapture:je}),o=r[0],i=r[1],s=e.forwardProps,c=e.children,u=e.className,a=e.removeScrollBar,m=e.enabled,d=e.shards,p=e.sideCar,h=e.noIsolation,g=e.inert,l=e.allowPinchZoom,v=e.as,y=v===void 0?"div":v,w=e.gapMode,b=_t(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=p,E=ir([n,t]),C=U(U({},b),o);return f.createElement(f.Fragment,null,m&&f.createElement(x,{sideCar:$t,removeScrollBar:a,shards:d,noIsolation:h,inert:g,setCallbacks:i,allowPinchZoom:!!l,lockRef:n,gapMode:w}),s?f.cloneElement(f.Children.only(c),U(U({},C),{ref:E})):f.createElement(y,U({},C,{className:u,ref:E}),c))});Le.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Le.classNames={fullWidth:Re,zeroRight:Ae};var ur=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function fr(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=ur();return t&&e.setAttribute("nonce",t),e}function dr(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function mr(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var hr=function(){var e=0,t=null;return{add:function(n){e==0&&(t=fr())&&(dr(t,n),mr(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},pr=function(){var e=hr();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},jt=function(){var e=pr(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},vr={left:0,top:0,right:0,gap:0},He=function(e){return parseInt(e||"",10)||0},gr=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[He(n),He(r),He(o)]},yr=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return vr;var t=gr(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},wr=jt(),ae="data-scroll-locked",xr=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(tr,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(ae,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ae,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Re,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Ae," .").concat(Ae,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Re," .").concat(Re,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ae,`] {
    `).concat(nr,": ").concat(c,`px;
  }
`)},Et=function(){var e=parseInt(document.body.getAttribute(ae)||"0",10);return isFinite(e)?e:0},br=function(){f.useEffect(function(){return document.body.setAttribute(ae,(Et()+1).toString()),function(){var e=Et()-1;e<=0?document.body.removeAttribute(ae):document.body.setAttribute(ae,e.toString())}},[])},Er=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;br();var i=f.useMemo(function(){return yr(o)},[o]);return f.createElement(wr,{styles:xr(i,!t,o,n?"":"!important")})},Xe=!1;if(typeof window<"u")try{var xe=Object.defineProperty({},"passive",{get:function(){return Xe=!0,!0}});window.addEventListener("test",xe,xe),window.removeEventListener("test",xe,xe)}catch{Xe=!1}var ie=Xe?{passive:!1}:!1,Sr=function(e){return e.tagName==="TEXTAREA"},Ht=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Sr(e)&&n[t]==="visible")},Cr=function(e){return Ht(e,"overflowY")},Ar=function(e){return Ht(e,"overflowX")},St=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Ut(e,r);if(o){var i=zt(e,r),s=i[1],c=i[2];if(s>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Rr=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Pr=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ut=function(e,t){return e==="v"?Cr(t):Ar(t)},zt=function(e,t){return e==="v"?Rr(t):Pr(t)},Or=function(e,t){return e==="h"&&t==="rtl"?-1:1},Tr=function(e,t,n,r,o){var i=Or(e,window.getComputedStyle(t).direction),s=i*r,c=n.target,u=t.contains(c),a=!1,m=s>0,d=0,p=0;do{var h=zt(e,c),g=h[0],l=h[1],v=h[2],y=l-v-i*g;(g||y)&&Ut(e,c)&&(d+=y,p+=g),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return(m&&Math.abs(d)<1||!m&&Math.abs(p)<1)&&(a=!0),a},be=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ct=function(e){return[e.deltaX,e.deltaY]},At=function(e){return e&&"current"in e?e.current:e},Mr=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Nr=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Lr=0,se=[];function Dr(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(Lr++)[0],i=f.useState(jt)[0],s=f.useRef(e);f.useEffect(function(){s.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var l=er([e.lockRef.current],(e.shards||[]).map(At),!0).filter(Boolean);return l.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),l.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=f.useCallback(function(l,v){if("touches"in l&&l.touches.length===2||l.type==="wheel"&&l.ctrlKey)return!s.current.allowPinchZoom;var y=be(l),w=n.current,b="deltaX"in l?l.deltaX:w[0]-y[0],x="deltaY"in l?l.deltaY:w[1]-y[1],E,C=l.target,S=Math.abs(b)>Math.abs(x)?"h":"v";if("touches"in l&&S==="h"&&C.type==="range")return!1;var R=St(S,C);if(!R)return!0;if(R?E=S:(E=S==="v"?"h":"v",R=St(S,C)),!R)return!1;if(!r.current&&"changedTouches"in l&&(b||x)&&(r.current=E),!E)return!0;var T=r.current||E;return Tr(T,v,l,T==="h"?b:x)},[]),u=f.useCallback(function(l){var v=l;if(!(!se.length||se[se.length-1]!==i)){var y="deltaY"in v?Ct(v):be(v),w=t.current.filter(function(E){return E.name===v.type&&(E.target===v.target||v.target===E.shadowParent)&&Mr(E.delta,y)})[0];if(w&&w.should){v.cancelable&&v.preventDefault();return}if(!w){var b=(s.current.shards||[]).map(At).filter(Boolean).filter(function(E){return E.contains(v.target)}),x=b.length>0?c(v,b[0]):!s.current.noIsolation;x&&v.cancelable&&v.preventDefault()}}},[]),a=f.useCallback(function(l,v,y,w){var b={name:l,delta:v,target:y,should:w,shadowParent:kr(y)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(x){return x!==b})},1)},[]),m=f.useCallback(function(l){n.current=be(l),r.current=void 0},[]),d=f.useCallback(function(l){a(l.type,Ct(l),l.target,c(l,e.lockRef.current))},[]),p=f.useCallback(function(l){a(l.type,be(l),l.target,c(l,e.lockRef.current))},[]);f.useEffect(function(){return se.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,ie),document.addEventListener("touchmove",u,ie),document.addEventListener("touchstart",m,ie),function(){se=se.filter(function(l){return l!==i}),document.removeEventListener("wheel",u,ie),document.removeEventListener("touchmove",u,ie),document.removeEventListener("touchstart",m,ie)}},[]);var h=e.removeScrollBar,g=e.inert;return f.createElement(f.Fragment,null,g?f.createElement(i,{styles:Nr(o)}):null,h?f.createElement(Er,{gapMode:e.gapMode}):null)}function kr(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Fr=lr($t,Dr);var Ir=f.forwardRef(function(e,t){return f.createElement(Le,U({},e,{ref:t,sideCar:Fr}))});Ir.classNames=Le.classNames;var Wr=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ce=new WeakMap,Ee=new WeakMap,Se={},Ue=0,Vt=function(e){return e&&(e.host||Vt(e.parentNode))},_r=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Vt(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Br=function(e,t,n,r){var o=_r(t,Array.isArray(e)?e:[e]);Se[n]||(Se[n]=new WeakMap);var i=Se[n],s=[],c=new Set,u=new Set(o),a=function(d){!d||c.has(d)||(c.add(d),a(d.parentNode))};o.forEach(a);var m=function(d){!d||u.has(d)||Array.prototype.forEach.call(d.children,function(p){if(c.has(p))m(p);else try{var h=p.getAttribute(r),g=h!==null&&h!=="false",l=(ce.get(p)||0)+1,v=(i.get(p)||0)+1;ce.set(p,l),i.set(p,v),s.push(p),l===1&&g&&Ee.set(p,!0),v===1&&p.setAttribute(n,"true"),g||p.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",p,y)}})};return m(t),c.clear(),Ue++,function(){s.forEach(function(d){var p=ce.get(d)-1,h=i.get(d)-1;ce.set(d,p),i.set(d,h),p||(Ee.has(d)||d.removeAttribute(r),Ee.delete(d)),h||d.removeAttribute(n)}),Ue--,Ue||(ce=new WeakMap,ce=new WeakMap,Ee=new WeakMap,Se={})}},ni=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Wr(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Br(r,o,n,"aria-hidden")):function(){return null}};const $r=["top","right","bottom","left"],Q=Math.min,W=Math.max,Oe=Math.round,Ce=Math.floor,z=e=>({x:e,y:e}),jr={left:"right",right:"left",bottom:"top",top:"bottom"},Hr={start:"end",end:"start"};function Ke(e,t,n){return W(e,Q(t,n))}function X(e,t){return typeof e=="function"?e(t):e}function K(e){return e.split("-")[0]}function fe(e){return e.split("-")[1]}function Ge(e){return e==="x"?"y":"x"}function Qe(e){return e==="y"?"height":"width"}function J(e){return["top","bottom"].includes(K(e))?"y":"x"}function Je(e){return Ge(J(e))}function Ur(e,t,n){n===void 0&&(n=!1);const r=fe(e),o=Je(e),i=Qe(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Te(s)),[s,Te(s)]}function zr(e){const t=Te(e);return[qe(e),t,qe(t)]}function qe(e){return e.replace(/start|end/g,t=>Hr[t])}function Vr(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function Yr(e,t,n,r){const o=fe(e);let i=Vr(K(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(qe)))),i}function Te(e){return e.replace(/left|right|bottom|top/g,t=>jr[t])}function Xr(e){return{top:0,right:0,bottom:0,left:0,...e}}function Yt(e){return typeof e!="number"?Xr(e):{top:e,right:e,bottom:e,left:e}}function Me(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Rt(e,t,n){let{reference:r,floating:o}=e;const i=J(t),s=Je(t),c=Qe(s),u=K(t),a=i==="y",m=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,p=r[c]/2-o[c]/2;let h;switch(u){case"top":h={x:m,y:r.y-o.height};break;case"bottom":h={x:m,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-o.width,y:d};break;default:h={x:r.x,y:r.y}}switch(fe(t)){case"start":h[s]-=p*(n&&a?-1:1);break;case"end":h[s]+=p*(n&&a?-1:1);break}return h}const Kr=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,c=i.filter(Boolean),u=await(s.isRTL==null?void 0:s.isRTL(t));let a=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:m,y:d}=Rt(a,r,u),p=r,h={},g=0;for(let l=0;l<c.length;l++){const{name:v,fn:y}=c[l],{x:w,y:b,data:x,reset:E}=await y({x:m,y:d,initialPlacement:r,placement:p,strategy:o,middlewareData:h,rects:a,platform:s,elements:{reference:e,floating:t}});m=w??m,d=b??d,h={...h,[v]:{...h[v],...x}},E&&g<=50&&(g++,typeof E=="object"&&(E.placement&&(p=E.placement),E.rects&&(a=E.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:m,y:d}=Rt(a,p,u)),l=-1)}return{x:m,y:d,placement:p,strategy:o,middlewareData:h}};async function he(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:c,strategy:u}=e,{boundary:a="clippingAncestors",rootBoundary:m="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=X(t,e),g=Yt(h),v=c[p?d==="floating"?"reference":"floating":d],y=Me(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(c.floating)),boundary:a,rootBoundary:m,strategy:u})),w=d==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,b=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c.floating)),x=await(i.isElement==null?void 0:i.isElement(b))?await(i.getScale==null?void 0:i.getScale(b))||{x:1,y:1}:{x:1,y:1},E=Me(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-E.top+g.top)/x.y,bottom:(E.bottom-y.bottom+g.bottom)/x.y,left:(y.left-E.left+g.left)/x.x,right:(E.right-y.right+g.right)/x.x}}const qr=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:c,middlewareData:u}=t,{element:a,padding:m=0}=X(e,t)||{};if(a==null)return{};const d=Yt(m),p={x:n,y:r},h=Je(o),g=Qe(h),l=await s.getDimensions(a),v=h==="y",y=v?"top":"left",w=v?"bottom":"right",b=v?"clientHeight":"clientWidth",x=i.reference[g]+i.reference[h]-p[h]-i.floating[g],E=p[h]-i.reference[h],C=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a));let S=C?C[b]:0;(!S||!await(s.isElement==null?void 0:s.isElement(C)))&&(S=c.floating[b]||i.floating[g]);const R=x/2-E/2,T=S/2-l[g]/2-1,P=Q(d[y],T),F=Q(d[w],T),I=P,M=S-l[g]-F,O=S/2-l[g]/2+R,B=Ke(I,O,M),N=!u.arrow&&fe(o)!=null&&O!==B&&i.reference[g]/2-(O<I?P:F)-l[g]/2<0,D=N?O<I?O-I:O-M:0;return{[h]:p[h]+D,data:{[h]:B,centerOffset:O-B-D,...N&&{alignmentOffset:D}},reset:N}}}),Zr=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:c,platform:u,elements:a}=t,{mainAxis:m=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:l=!0,...v}=X(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const y=K(o),w=J(c),b=K(c)===c,x=await(u.isRTL==null?void 0:u.isRTL(a.floating)),E=p||(b||!l?[Te(c)]:zr(c)),C=g!=="none";!p&&C&&E.push(...Yr(c,l,g,x));const S=[c,...E],R=await he(t,v),T=[];let P=((r=i.flip)==null?void 0:r.overflows)||[];if(m&&T.push(R[y]),d){const O=Ur(o,s,x);T.push(R[O[0]],R[O[1]])}if(P=[...P,{placement:o,overflows:T}],!T.every(O=>O<=0)){var F,I;const O=(((F=i.flip)==null?void 0:F.index)||0)+1,B=S[O];if(B)return{data:{index:O,overflows:P},reset:{placement:B}};let N=(I=P.filter(D=>D.overflows[0]<=0).sort((D,A)=>D.overflows[1]-A.overflows[1])[0])==null?void 0:I.placement;if(!N)switch(h){case"bestFit":{var M;const D=(M=P.filter(A=>{if(C){const k=J(A.placement);return k===w||k==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(k=>k>0).reduce((k,H)=>k+H,0)]).sort((A,k)=>A[1]-k[1])[0])==null?void 0:M[0];D&&(N=D);break}case"initialPlacement":N=c;break}if(o!==N)return{reset:{placement:N}}}return{}}}};function Pt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Ot(e){return $r.some(t=>e[t]>=0)}const Gr=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=X(e,t);switch(r){case"referenceHidden":{const i=await he(t,{...o,elementContext:"reference"}),s=Pt(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Ot(s)}}}case"escaped":{const i=await he(t,{...o,altBoundary:!0}),s=Pt(i,n.floating);return{data:{escapedOffsets:s,escaped:Ot(s)}}}default:return{}}}}};async function Qr(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=K(n),c=fe(n),u=J(n)==="y",a=["left","top"].includes(s)?-1:1,m=i&&u?-1:1,d=X(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:g}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof g=="number"&&(h=c==="end"?g*-1:g),u?{x:h*m,y:p*a}:{x:p*a,y:h*m}}const Jr=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:c}=t,u=await Qr(t,e);return s===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:s}}}}},eo=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:c={fn:v=>{let{x:y,y:w}=v;return{x:y,y:w}}},...u}=X(e,t),a={x:n,y:r},m=await he(t,u),d=J(K(o)),p=Ge(d);let h=a[p],g=a[d];if(i){const v=p==="y"?"top":"left",y=p==="y"?"bottom":"right",w=h+m[v],b=h-m[y];h=Ke(w,h,b)}if(s){const v=d==="y"?"top":"left",y=d==="y"?"bottom":"right",w=g+m[v],b=g-m[y];g=Ke(w,g,b)}const l=c.fn({...t,[p]:h,[d]:g});return{...l,data:{x:l.x-n,y:l.y-r,enabled:{[p]:i,[d]:s}}}}}},to=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:c=0,mainAxis:u=!0,crossAxis:a=!0}=X(e,t),m={x:n,y:r},d=J(o),p=Ge(d);let h=m[p],g=m[d];const l=X(c,t),v=typeof l=="number"?{mainAxis:l,crossAxis:0}:{mainAxis:0,crossAxis:0,...l};if(u){const b=p==="y"?"height":"width",x=i.reference[p]-i.floating[b]+v.mainAxis,E=i.reference[p]+i.reference[b]-v.mainAxis;h<x?h=x:h>E&&(h=E)}if(a){var y,w;const b=p==="y"?"width":"height",x=["top","left"].includes(K(o)),E=i.reference[d]-i.floating[b]+(x&&((y=s.offset)==null?void 0:y[d])||0)+(x?0:v.crossAxis),C=i.reference[d]+i.reference[b]+(x?0:((w=s.offset)==null?void 0:w[d])||0)-(x?v.crossAxis:0);g<E?g=E:g>C&&(g=C)}return{[p]:h,[d]:g}}}},no=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:c}=t,{apply:u=()=>{},...a}=X(e,t),m=await he(t,a),d=K(o),p=fe(o),h=J(o)==="y",{width:g,height:l}=i.floating;let v,y;d==="top"||d==="bottom"?(v=d,y=p===(await(s.isRTL==null?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(y=d,v=p==="end"?"top":"bottom");const w=l-m.top-m.bottom,b=g-m.left-m.right,x=Q(l-m[v],w),E=Q(g-m[y],b),C=!t.middlewareData.shift;let S=x,R=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=b),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=w),C&&!p){const P=W(m.left,0),F=W(m.right,0),I=W(m.top,0),M=W(m.bottom,0);h?R=g-2*(P!==0||F!==0?P+F:W(m.left,m.right)):S=l-2*(I!==0||M!==0?I+M:W(m.top,m.bottom))}await u({...t,availableWidth:R,availableHeight:S});const T=await s.getDimensions(c.floating);return g!==T.width||l!==T.height?{reset:{rects:!0}}:{}}}};function De(){return typeof window<"u"}function de(e){return Xt(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Y(e){var t;return(t=(Xt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Xt(e){return De()?e instanceof Node||e instanceof _(e).Node:!1}function $(e){return De()?e instanceof Element||e instanceof _(e).Element:!1}function V(e){return De()?e instanceof HTMLElement||e instanceof _(e).HTMLElement:!1}function Tt(e){return!De()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof _(e).ShadowRoot}function ve(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ro(e){return["table","td","th"].includes(de(e))}function ke(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function et(e){const t=tt(),n=$(e)?j(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function oo(e){let t=ee(e);for(;V(t)&&!ue(t);){if(et(t))return t;if(ke(t))return null;t=ee(t)}return null}function tt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ue(e){return["html","body","#document"].includes(de(e))}function j(e){return _(e).getComputedStyle(e)}function Fe(e){return $(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if(de(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Tt(e)&&e.host||Y(e);return Tt(t)?t.host:t}function Kt(e){const t=ee(e);return ue(t)?e.ownerDocument?e.ownerDocument.body:e.body:V(t)&&ve(t)?t:Kt(t)}function pe(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Kt(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=_(o);if(i){const c=Ze(s);return t.concat(s,s.visualViewport||[],ve(o)?o:[],c&&n?pe(c):[])}return t.concat(o,pe(o,[],n))}function Ze(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function qt(e){const t=j(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=V(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,c=Oe(n)!==i||Oe(r)!==s;return c&&(n=i,r=s),{width:n,height:r,$:c}}function nt(e){return $(e)?e:e.contextElement}function le(e){const t=nt(e);if(!V(t))return z(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=qt(t);let s=(i?Oe(n.width):n.width)/r,c=(i?Oe(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const io=z(0);function Zt(e){const t=_(e);return!tt()||!t.visualViewport?io:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function so(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==_(e)?!1:t}function oe(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=nt(e);let s=z(1);t&&(r?$(r)&&(s=le(r)):s=le(e));const c=so(i,n,r)?Zt(i):z(0);let u=(o.left+c.x)/s.x,a=(o.top+c.y)/s.y,m=o.width/s.x,d=o.height/s.y;if(i){const p=_(i),h=r&&$(r)?_(r):r;let g=p,l=Ze(g);for(;l&&r&&h!==g;){const v=le(l),y=l.getBoundingClientRect(),w=j(l),b=y.left+(l.clientLeft+parseFloat(w.paddingLeft))*v.x,x=y.top+(l.clientTop+parseFloat(w.paddingTop))*v.y;u*=v.x,a*=v.y,m*=v.x,d*=v.y,u+=b,a+=x,g=_(l),l=Ze(g)}}return Me({width:m,height:d,x:u,y:a})}function rt(e,t){const n=Fe(e).scrollLeft;return t?t.left+n:oe(Y(e)).left+n}function Gt(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:rt(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function co(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Y(r),c=t?ke(t.floating):!1;if(r===s||c&&i)return n;let u={scrollLeft:0,scrollTop:0},a=z(1);const m=z(0),d=V(r);if((d||!d&&!i)&&((de(r)!=="body"||ve(s))&&(u=Fe(r)),V(r))){const h=oe(r);a=le(r),m.x=h.x+r.clientLeft,m.y=h.y+r.clientTop}const p=s&&!d&&!i?Gt(s,u,!0):z(0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-u.scrollLeft*a.x+m.x+p.x,y:n.y*a.y-u.scrollTop*a.y+m.y+p.y}}function ao(e){return Array.from(e.getClientRects())}function lo(e){const t=Y(e),n=Fe(e),r=e.ownerDocument.body,o=W(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=W(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+rt(e);const c=-n.scrollTop;return j(r).direction==="rtl"&&(s+=W(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:c}}function uo(e,t){const n=_(e),r=Y(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,c=0,u=0;if(o){i=o.width,s=o.height;const a=tt();(!a||a&&t==="fixed")&&(c=o.offsetLeft,u=o.offsetTop)}return{width:i,height:s,x:c,y:u}}function fo(e,t){const n=oe(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=V(e)?le(e):z(1),s=e.clientWidth*i.x,c=e.clientHeight*i.y,u=o*i.x,a=r*i.y;return{width:s,height:c,x:u,y:a}}function Mt(e,t,n){let r;if(t==="viewport")r=uo(e,n);else if(t==="document")r=lo(Y(e));else if($(t))r=fo(t,n);else{const o=Zt(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Me(r)}function Qt(e,t){const n=ee(e);return n===t||!$(n)||ue(n)?!1:j(n).position==="fixed"||Qt(n,t)}function mo(e,t){const n=t.get(e);if(n)return n;let r=pe(e,[],!1).filter(c=>$(c)&&de(c)!=="body"),o=null;const i=j(e).position==="fixed";let s=i?ee(e):e;for(;$(s)&&!ue(s);){const c=j(s),u=et(s);!u&&c.position==="fixed"&&(o=null),(i?!u&&!o:!u&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ve(s)&&!u&&Qt(e,s))?r=r.filter(m=>m!==s):o=c,s=ee(s)}return t.set(e,r),r}function ho(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?ke(t)?[]:mo(t,this._c):[].concat(n),r],c=s[0],u=s.reduce((a,m)=>{const d=Mt(t,m,o);return a.top=W(d.top,a.top),a.right=Q(d.right,a.right),a.bottom=Q(d.bottom,a.bottom),a.left=W(d.left,a.left),a},Mt(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function po(e){const{width:t,height:n}=qt(e);return{width:t,height:n}}function vo(e,t,n){const r=V(t),o=Y(t),i=n==="fixed",s=oe(e,!0,i,t);let c={scrollLeft:0,scrollTop:0};const u=z(0);if(r||!r&&!i)if((de(t)!=="body"||ve(o))&&(c=Fe(t)),r){const p=oe(t,!0,i,t);u.x=p.x+t.clientLeft,u.y=p.y+t.clientTop}else o&&(u.x=rt(o));const a=o&&!r&&!i?Gt(o,c):z(0),m=s.left+c.scrollLeft-u.x-a.x,d=s.top+c.scrollTop-u.y-a.y;return{x:m,y:d,width:s.width,height:s.height}}function ze(e){return j(e).position==="static"}function Nt(e,t){if(!V(e)||j(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Y(e)===n&&(n=n.ownerDocument.body),n}function Jt(e,t){const n=_(e);if(ke(e))return n;if(!V(e)){let o=ee(e);for(;o&&!ue(o);){if($(o)&&!ze(o))return o;o=ee(o)}return n}let r=Nt(e,t);for(;r&&ro(r)&&ze(r);)r=Nt(r,t);return r&&ue(r)&&ze(r)&&!et(r)?n:r||oo(e)||n}const go=async function(e){const t=this.getOffsetParent||Jt,n=this.getDimensions,r=await n(e.floating);return{reference:vo(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function yo(e){return j(e).direction==="rtl"}const wo={convertOffsetParentRelativeRectToViewportRelativeRect:co,getDocumentElement:Y,getClippingRect:ho,getOffsetParent:Jt,getElementRects:go,getClientRects:ao,getDimensions:po,getScale:le,isElement:$,isRTL:yo};function en(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function xo(e,t){let n=null,r;const o=Y(e);function i(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function s(c,u){c===void 0&&(c=!1),u===void 0&&(u=1),i();const a=e.getBoundingClientRect(),{left:m,top:d,width:p,height:h}=a;if(c||t(),!p||!h)return;const g=Ce(d),l=Ce(o.clientWidth-(m+p)),v=Ce(o.clientHeight-(d+h)),y=Ce(m),b={rootMargin:-g+"px "+-l+"px "+-v+"px "+-y+"px",threshold:W(0,Q(1,u))||1};let x=!0;function E(C){const S=C[0].intersectionRatio;if(S!==u){if(!x)return s();S?s(!1,S):r=setTimeout(()=>{s(!1,1e-7)},1e3)}S===1&&!en(a,e.getBoundingClientRect())&&s(),x=!1}try{n=new IntersectionObserver(E,{...b,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,b)}n.observe(e)}return s(!0),i}function bo(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,a=nt(e),m=o||i?[...a?pe(a):[],...pe(t)]:[];m.forEach(y=>{o&&y.addEventListener("scroll",n,{passive:!0}),i&&y.addEventListener("resize",n)});const d=a&&c?xo(a,n):null;let p=-1,h=null;s&&(h=new ResizeObserver(y=>{let[w]=y;w&&w.target===a&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var b;(b=h)==null||b.observe(t)})),n()}),a&&!u&&h.observe(a),h.observe(t));let g,l=u?oe(e):null;u&&v();function v(){const y=oe(e);l&&!en(l,y)&&n(),l=y,g=requestAnimationFrame(v)}return n(),()=>{var y;m.forEach(w=>{o&&w.removeEventListener("scroll",n),i&&w.removeEventListener("resize",n)}),d==null||d(),(y=h)==null||y.disconnect(),h=null,u&&cancelAnimationFrame(g)}}const Eo=Jr,So=eo,Co=Zr,Ao=no,Ro=Gr,Lt=qr,Po=to,Oo=(e,t,n)=>{const r=new Map,o={platform:wo,...n},i={...o.platform,_c:r};return Kr(e,t,{...o,platform:i})};var Pe=typeof document<"u"?f.useLayoutEffect:f.useEffect;function Ne(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ne(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Ne(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function tn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Dt(e,t){const n=tn(e);return Math.round(t*n)/n}function Ve(e){const t=f.useRef(e);return Pe(()=>{t.current=e}),t}function To(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:c=!0,whileElementsMounted:u,open:a}=e,[m,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=f.useState(r);Ne(p,r)||h(r);const[g,l]=f.useState(null),[v,y]=f.useState(null),w=f.useCallback(A=>{A!==C.current&&(C.current=A,l(A))},[]),b=f.useCallback(A=>{A!==S.current&&(S.current=A,y(A))},[]),x=i||g,E=s||v,C=f.useRef(null),S=f.useRef(null),R=f.useRef(m),T=u!=null,P=Ve(u),F=Ve(o),I=Ve(a),M=f.useCallback(()=>{if(!C.current||!S.current)return;const A={placement:t,strategy:n,middleware:p};F.current&&(A.platform=F.current),Oo(C.current,S.current,A).then(k=>{const H={...k,isPositioned:I.current!==!1};O.current&&!Ne(R.current,H)&&(R.current=H,Rn.flushSync(()=>{d(H)}))})},[p,t,n,F,I]);Pe(()=>{a===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,d(A=>({...A,isPositioned:!1})))},[a]);const O=f.useRef(!1);Pe(()=>(O.current=!0,()=>{O.current=!1}),[]),Pe(()=>{if(x&&(C.current=x),E&&(S.current=E),x&&E){if(P.current)return P.current(x,E,M);M()}},[x,E,M,P,T]);const B=f.useMemo(()=>({reference:C,floating:S,setReference:w,setFloating:b}),[w,b]),N=f.useMemo(()=>({reference:x,floating:E}),[x,E]),D=f.useMemo(()=>{const A={position:n,left:0,top:0};if(!N.floating)return A;const k=Dt(N.floating,m.x),H=Dt(N.floating,m.y);return c?{...A,transform:"translate("+k+"px, "+H+"px)",...tn(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:k,top:H}},[n,c,N.floating,m.x,m.y]);return f.useMemo(()=>({...m,update:M,refs:B,elements:N,floatingStyles:D}),[m,M,B,N,D])}const Mo=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Lt({element:r.current,padding:o}).fn(n):{}:r?Lt({element:r,padding:o}).fn(n):{}}}},No=(e,t)=>({...Eo(e),options:[e,t]}),Lo=(e,t)=>({...So(e),options:[e,t]}),Do=(e,t)=>({...Po(e),options:[e,t]}),ko=(e,t)=>({...Co(e),options:[e,t]}),Fo=(e,t)=>({...Ao(e),options:[e,t]}),Io=(e,t)=>({...Ro(e),options:[e,t]}),Wo=(e,t)=>({...Mo(e),options:[e,t]});var _o="Arrow",nn=f.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return L.jsx(te.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:L.jsx("polygon",{points:"0,0 30,0 15,10"})})});nn.displayName=_o;var Bo=nn;function $o(e){const[t,n]=f.useState(void 0);return re(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,c;if("borderBoxSize"in i){const u=i.borderBoxSize,a=Array.isArray(u)?u[0]:u;s=a.inlineSize,c=a.blockSize}else s=e.offsetWidth,c=e.offsetHeight;n({width:s,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var ot="Popper",[rn,ri]=kt(ot),[jo,on]=rn(ot),sn=e=>{const{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return L.jsx(jo,{scope:t,anchor:r,onAnchorChange:o,children:n})};sn.displayName=ot;var cn="PopperAnchor",an=f.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=on(cn,n),s=f.useRef(null),c=Z(t,s);return f.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:L.jsx(te.div,{...o,ref:c})});an.displayName=cn;var it="PopperContent",[Ho,Uo]=rn(it),ln=f.forwardRef((e,t)=>{var st,ct,at,lt,ut,ft;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:a=[],collisionPadding:m=0,sticky:d="partial",hideWhenDetached:p=!1,updatePositionStrategy:h="optimized",onPlaced:g,...l}=e,v=on(it,n),[y,w]=f.useState(null),b=Z(t,me=>w(me)),[x,E]=f.useState(null),C=$o(x),S=(C==null?void 0:C.width)??0,R=(C==null?void 0:C.height)??0,T=r+(i!=="center"?"-"+i:""),P=typeof m=="number"?m:{top:0,right:0,bottom:0,left:0,...m},F=Array.isArray(a)?a:[a],I=F.length>0,M={padding:P,boundary:F.filter(Vo),altBoundary:I},{refs:O,floatingStyles:B,placement:N,isPositioned:D,middlewareData:A}=To({strategy:"fixed",placement:T,whileElementsMounted:(...me)=>bo(...me,{animationFrame:h==="always"}),elements:{reference:v.anchor},middleware:[No({mainAxis:o+R,alignmentAxis:s}),u&&Lo({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?Do():void 0,...M}),u&&ko({...M}),Fo({...M,apply:({elements:me,rects:dt,availableWidth:wn,availableHeight:xn})=>{const{width:bn,height:En}=dt.reference,ye=me.floating.style;ye.setProperty("--radix-popper-available-width",`${wn}px`),ye.setProperty("--radix-popper-available-height",`${xn}px`),ye.setProperty("--radix-popper-anchor-width",`${bn}px`),ye.setProperty("--radix-popper-anchor-height",`${En}px`)}}),x&&Wo({element:x,padding:c}),Yo({arrowWidth:S,arrowHeight:R}),p&&Io({strategy:"referenceHidden",...M})]}),[k,H]=dn(N),ge=G(g);re(()=>{D&&(ge==null||ge())},[D,ge]);const hn=(st=A.arrow)==null?void 0:st.x,pn=(ct=A.arrow)==null?void 0:ct.y,vn=((at=A.arrow)==null?void 0:at.centerOffset)!==0,[gn,yn]=f.useState();return re(()=>{y&&yn(window.getComputedStyle(y).zIndex)},[y]),L.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...B,transform:D?B.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:gn,"--radix-popper-transform-origin":[(lt=A.transformOrigin)==null?void 0:lt.x,(ut=A.transformOrigin)==null?void 0:ut.y].join(" "),...((ft=A.hide)==null?void 0:ft.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:L.jsx(Ho,{scope:n,placedSide:k,onArrowChange:E,arrowX:hn,arrowY:pn,shouldHideArrow:vn,children:L.jsx(te.div,{"data-side":k,"data-align":H,...l,ref:b,style:{...l.style,animation:D?void 0:"none"}})})})});ln.displayName=it;var un="PopperArrow",zo={top:"bottom",right:"left",bottom:"top",left:"right"},fn=f.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=Uo(un,r),s=zo[i.placedSide];return L.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:L.jsx(Bo,{...o,ref:n,style:{...o.style,display:"block"}})})});fn.displayName=un;function Vo(e){return e!==null}var Yo=e=>({name:"transformOrigin",options:e,fn(t){var v,y,w;const{placement:n,rects:r,middlewareData:o}=t,s=((v=o.arrow)==null?void 0:v.centerOffset)!==0,c=s?0:e.arrowWidth,u=s?0:e.arrowHeight,[a,m]=dn(n),d={start:"0%",center:"50%",end:"100%"}[m],p=(((y=o.arrow)==null?void 0:y.x)??0)+c/2,h=(((w=o.arrow)==null?void 0:w.y)??0)+u/2;let g="",l="";return a==="bottom"?(g=s?d:`${p}px`,l=`${-u}px`):a==="top"?(g=s?d:`${p}px`,l=`${r.floating.height+u}px`):a==="right"?(g=`${-u}px`,l=s?d:`${h}px`):a==="left"&&(g=`${r.floating.width+u}px`,l=s?d:`${h}px`),{data:{x:g,y:l}}}});function dn(e){const[t,n="center"]=e.split("-");return[t,n]}var oi=sn,ii=an,si=ln,ci=fn,Xo="VisuallyHidden",mn=f.forwardRef((e,t)=>L.jsx(te.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));mn.displayName=Xo;var ai=mn;function li(e){const t=e+"CollectionProvider",[n,r]=kt(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=h=>{const{scope:g,children:l}=h,v=ne.useRef(null),y=ne.useRef(new Map).current;return L.jsx(o,{scope:g,itemMap:y,collectionRef:v,children:l})};s.displayName=t;const c=e+"CollectionSlot",u=ne.forwardRef((h,g)=>{const{scope:l,children:v}=h,y=i(c,l),w=Z(g,y.collectionRef);return L.jsx(mt,{ref:w,children:v})});u.displayName=c;const a=e+"CollectionItemSlot",m="data-radix-collection-item",d=ne.forwardRef((h,g)=>{const{scope:l,children:v,...y}=h,w=ne.useRef(null),b=Z(g,w),x=i(a,l);return ne.useEffect(()=>(x.itemMap.set(w,{ref:w,...y}),()=>void x.itemMap.delete(w))),L.jsx(mt,{[m]:"",ref:b,children:v})});d.displayName=a;function p(h){const g=i(e+"CollectionConsumer",h);return ne.useCallback(()=>{const v=g.collectionRef.current;if(!v)return[];const y=Array.from(v.querySelectorAll(`[${m}]`));return Array.from(g.itemMap.values()).sort((x,E)=>y.indexOf(x.ref.current)-y.indexOf(E.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:s,Slot:u,ItemSlot:d},p,r]}var Ko=f.createContext(void 0);function ui(e){const t=f.useContext(Ko);return e||t||"ltr"}export{ii as A,si as C,Fn as D,jn as F,Gn as P,ai as R,mn as V,ei as a,li as b,kt as c,Jo as d,G as e,Ie as f,re as g,$o as h,ri as i,oi as j,qn as k,ni as l,ti as m,Ir as n,ci as o,Qo as p,ui as u};
