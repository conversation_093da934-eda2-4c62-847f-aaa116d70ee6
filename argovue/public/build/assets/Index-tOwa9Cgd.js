import{r as i,j as e,L as P,$ as l,S as f}from"./app-BRSV0K6a.js";import{A as $,S as B,M as b}from"./AgroVueLayout-sZVsSF45.js";import{c as n,B as r}from"./button-DeV0q5_x.js";import{C as d,a as w,b as _,c as o,d as L}from"./card-De7NLU4G.js";import{I as V}from"./input-C84b3OWV.js";import{S,a as C,b as k,c as F,d as a}from"./select-Dn6ix02i.js";import{B as M}from"./badge-BPiyidbe.js";import{S as E,E as H}from"./satellite-Px9uRWBE.js";import{T as O}from"./trending-up-Def9vQkf.js";/* empty css            */import"./app-content-DoSKBbyq.js";import"./index-SRmR8U3Z.js";import"./index-D2bTj4tq.js";import"./app-logo-icon-uNlWQ8Ti.js";import"./index-CwMx2vkM.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]],G=n("Filter",q);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M5 12h14",key:"1ays0h"}]],U=n("Minus",R);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],T=n("Plus",Y);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],J=n("SquarePen",K);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q=[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]],W=n("TrendingDown",Q);function he({fields:t,filters:c}){const[x,m]=i.useState(c.search||""),[h,u]=i.useState(c.crop_type||""),[j,p]=i.useState(c.status||""),g=()=>{f.get("/fields",{search:x,crop_type:h,status:j},{preserveState:!0,replace:!0})},A=()=>{m(""),u(""),p(""),f.get("/fields",{},{preserveState:!0,replace:!0})},D=s=>{switch(s){case"excellent":return"bg-green-100 text-green-800";case"good":return"bg-blue-100 text-blue-800";case"fair":return"bg-yellow-100 text-yellow-800";case"poor":return"bg-orange-100 text-orange-800";case"critical":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},I=s=>{switch(s){case"active":return"bg-green-100 text-green-800";case"inactive":return"bg-gray-100 text-gray-800";case"disputed":return"bg-red-100 text-red-800";case"verified":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},z=s=>{switch(s){case"improvement":return e.jsx(O,{className:"h-4 w-4 text-green-600"});case"decline":return e.jsx(W,{className:"h-4 w-4 text-red-600"});default:return e.jsx(U,{className:"h-4 w-4 text-gray-400"})}};return e.jsxs($,{title:"Fields Management",breadcrumbs:[{label:"Dashboard",href:"/dashboard"},{label:"Fields"}],children:[e.jsx(P,{title:"Fields"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Fields"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage and monitor agricultural fields with satellite intelligence"})]}),e.jsx(l,{href:"/fields/create",children:e.jsxs(r,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Add Field"]})})]}),e.jsxs(d,{children:[e.jsx(w,{children:e.jsxs(_,{className:"flex items-center",children:[e.jsx(G,{className:"h-5 w-5 mr-2"}),"Filters"]})}),e.jsx(o,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Search"}),e.jsxs("div",{className:"relative",children:[e.jsx(B,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(V,{placeholder:"Search fields...",value:x,onChange:s=>m(s.target.value),className:"pl-10",onKeyPress:s=>s.key==="Enter"&&g()})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Crop Type"}),e.jsxs(S,{value:h,onValueChange:u,children:[e.jsx(C,{children:e.jsx(k,{placeholder:"All crops"})}),e.jsxs(F,{children:[e.jsx(a,{value:"",children:"All crops"}),e.jsx(a,{value:"rice",children:"Rice"}),e.jsx(a,{value:"maize",children:"Maize"}),e.jsx(a,{value:"cassava",children:"Cassava"}),e.jsx(a,{value:"millet",children:"Millet"}),e.jsx(a,{value:"sorghum",children:"Sorghum"}),e.jsx(a,{value:"yam",children:"Yam"}),e.jsx(a,{value:"beans",children:"Beans"}),e.jsx(a,{value:"groundnut",children:"Groundnut"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Status"}),e.jsxs(S,{value:j,onValueChange:p,children:[e.jsx(C,{children:e.jsx(k,{placeholder:"All statuses"})}),e.jsxs(F,{children:[e.jsx(a,{value:"",children:"All statuses"}),e.jsx(a,{value:"active",children:"Active"}),e.jsx(a,{value:"inactive",children:"Inactive"}),e.jsx(a,{value:"disputed",children:"Disputed"}),e.jsx(a,{value:"verified",children:"Verified"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Actions"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(r,{onClick:g,className:"flex-1",children:"Apply"}),e.jsx(r,{variant:"outline",onClick:A,children:"Clear"})]})]})]})})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.data.map(s=>{var N,v,y;return e.jsxs(d,{className:"hover:shadow-lg transition-shadow",children:[e.jsx(w,{className:"pb-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx(_,{className:"text-lg",children:s.name}),e.jsxs(L,{className:"flex items-center mt-1",children:[e.jsx(b,{className:"h-4 w-4 mr-1"}),s.lga,", ",s.state]})]}),e.jsx(M,{className:I(s.status),children:s.status})]})}),e.jsxs(o,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-muted-foreground",children:"Area:"}),e.jsxs("p",{className:"font-medium",children:[s.area_hectares," ha"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-muted-foreground",children:"Crop:"}),e.jsx("p",{className:"font-medium capitalize",children:s.crop_type})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-muted-foreground",children:"Owner:"}),e.jsx("p",{className:"font-medium",children:(N=s.owner)==null?void 0:N.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-muted-foreground",children:"Field ID:"}),e.jsx("p",{className:"font-medium",children:s.field_id})]})]}),s.health_status&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Health:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{className:D(s.health_status),children:s.health_status}),((y=(v=s.satellite_data)==null?void 0:v[0])==null?void 0:y.change_detection)&&z(s.satellite_data[0].change_detection.change_type)]})]}),s.latest_ndvi&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Latest NDVI:"}),e.jsx("span",{className:"font-medium",children:s.latest_ndvi.toFixed(3)})]}),s.last_satellite_update&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Last Update:"}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(E,{className:"h-3 w-3 text-muted-foreground"}),e.jsx("span",{className:"text-sm",children:new Date(s.last_satellite_update).toLocaleDateString()})]})]}),e.jsxs("div",{className:"flex space-x-2 pt-2",children:[e.jsx(l,{href:`/fields/${s.id}`,className:"flex-1",children:e.jsxs(r,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(H,{className:"h-4 w-4 mr-1"}),"View"]})}),e.jsx(l,{href:`/fields/${s.id}/edit`,children:e.jsx(r,{variant:"outline",size:"sm",children:e.jsx(J,{className:"h-4 w-4"})})})]})]})]},s.id)})}),t.data.length===0&&e.jsx(d,{children:e.jsxs(o,{className:"text-center py-12",children:[e.jsx(b,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No fields found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:Object.values(c).some(Boolean)?"No fields match your current filters. Try adjusting your search criteria.":"Get started by adding your first field to the system."}),!Object.values(c).some(Boolean)&&e.jsx(l,{href:"/fields/create",children:e.jsxs(r,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Add Your First Field"]})})]})}),t.data.length>0&&t.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Showing ",t.from," to ",t.to," of ",t.total," fields"]}),e.jsxs("div",{className:"flex space-x-2",children:[t.links.prev&&e.jsx(l,{href:t.links.prev,children:e.jsx(r,{variant:"outline",size:"sm",children:"Previous"})}),e.jsxs("span",{className:"flex items-center px-3 py-2 text-sm",children:["Page ",t.current_page," of ",t.last_page]}),t.links.next&&e.jsx(l,{href:t.links.next,children:e.jsx(r,{variant:"outline",size:"sm",children:"Next"})})]})]})]})]})}export{he as default};
