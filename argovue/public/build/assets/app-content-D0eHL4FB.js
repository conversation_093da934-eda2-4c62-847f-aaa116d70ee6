import{r as i,j as o,K as ke,$ as ne,S as Bn}from"./app-VriuOT-w.js";import{c as F,u as T,S as ae,d as C,e as Un,b as zn,B as Hn,a as st}from"./button-DmNtpphn.js";import{a as me,c as Z,d as U,f as x,P as $,k as Le,l as it,n as ct,m as lt,F as dt,D as Fe,p as Vn,i as ge,j as ut,A as ft,o as pt,C as mt,R as Wn,b as gt,u as ht,e as he,g as xt}from"./index-CN3oWmC9.js";import{P as M,d as Yn}from"./index-C_pbH-b0.js";import{A as Xn}from"./app-logo-icon-Cr4Vd7YU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qn=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],Zn=F("BookOpen",qn);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jn=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Qn=F("ChevronRight",Jn);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],to=F("ChevronsUpDown",eo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const no=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],oo=F("Folder",no);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ro=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]],ao=F("LayoutGrid",ro);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const so=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],io=F("LogOut",so);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const co=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],lo=F("PanelLeft",co);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uo=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],fo=F("Settings",uo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const po=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],mo=F("X",po),Ne=768;function vt(){const[e,t]=i.useState();return i.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Ne-1}px)`),r=()=>{t(window.innerWidth<Ne)};return n.addEventListener("change",r),t(window.innerWidth<Ne),()=>n.removeEventListener("change",r)},[]),!!e}var $e="Dialog",[bt,Ss]=Z($e),[go,S]=bt($e),wt=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:s,modal:c=!0}=e,l=i.useRef(null),d=i.useRef(null),[p=!1,u]=me({prop:r,defaultProp:a,onChange:s});return o.jsx(go,{scope:t,triggerRef:l,contentRef:d,contentId:U(),titleId:U(),descriptionId:U(),open:p,onOpenChange:u,onOpenToggle:i.useCallback(()=>u(f=>!f),[u]),modal:c,children:n})};wt.displayName=$e;var Ct="DialogTrigger",_t=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=S(Ct,n),s=T(t,a.triggerRef);return o.jsx(M.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Be(a.open),...r,ref:s,onClick:x(e.onClick,a.onOpenToggle)})});_t.displayName=Ct;var Ge="DialogPortal",[ho,yt]=bt(Ge,{forceMount:void 0}),Mt=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:a}=e,s=S(Ge,t);return o.jsx(ho,{scope:t,forceMount:n,children:i.Children.map(r,c=>o.jsx($,{present:n||s.open,children:o.jsx(Le,{asChild:!0,container:a,children:c})}))})};Mt.displayName=Ge;var ue="DialogOverlay",jt=i.forwardRef((e,t)=>{const n=yt(ue,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,s=S(ue,e.__scopeDialog);return s.modal?o.jsx($,{present:r||s.open,children:o.jsx(xo,{...a,ref:t})}):null});jt.displayName=ue;var xo=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=S(ue,n);return o.jsx(ct,{as:ae,allowPinchZoom:!0,shards:[a.contentRef],children:o.jsx(M.div,{"data-state":Be(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),z="DialogContent",Rt=i.forwardRef((e,t)=>{const n=yt(z,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,s=S(z,e.__scopeDialog);return o.jsx($,{present:r||s.open,children:s.modal?o.jsx(vo,{...a,ref:t}):o.jsx(bo,{...a,ref:t})})});Rt.displayName=z;var vo=i.forwardRef((e,t)=>{const n=S(z,e.__scopeDialog),r=i.useRef(null),a=T(t,n.contentRef,r);return i.useEffect(()=>{const s=r.current;if(s)return it(s)},[]),o.jsx(Et,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:x(e.onCloseAutoFocus,s=>{var c;s.preventDefault(),(c=n.triggerRef.current)==null||c.focus()}),onPointerDownOutside:x(e.onPointerDownOutside,s=>{const c=s.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0;(c.button===2||l)&&s.preventDefault()}),onFocusOutside:x(e.onFocusOutside,s=>s.preventDefault())})}),bo=i.forwardRef((e,t)=>{const n=S(z,e.__scopeDialog),r=i.useRef(!1),a=i.useRef(!1);return o.jsx(Et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var c,l;(c=e.onCloseAutoFocus)==null||c.call(e,s),s.defaultPrevented||(r.current||(l=n.triggerRef.current)==null||l.focus(),s.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:s=>{var d,p;(d=e.onInteractOutside)==null||d.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const c=s.target;((p=n.triggerRef.current)==null?void 0:p.contains(c))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&a.current&&s.preventDefault()}})}),Et=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:s,...c}=e,l=S(z,n),d=i.useRef(null),p=T(t,d);return lt(),o.jsxs(o.Fragment,{children:[o.jsx(dt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:s,children:o.jsx(Fe,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":Be(l.open),...c,ref:p,onDismiss:()=>l.onOpenChange(!1)})}),o.jsxs(o.Fragment,{children:[o.jsx(wo,{titleId:l.titleId}),o.jsx(_o,{contentRef:d,descriptionId:l.descriptionId})]})]})}),Ke="DialogTitle",It=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=S(Ke,n);return o.jsx(M.h2,{id:a.titleId,...r,ref:t})});It.displayName=Ke;var Dt="DialogDescription",Nt=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=S(Dt,n);return o.jsx(M.p,{id:a.descriptionId,...r,ref:t})});Nt.displayName=Dt;var Tt="DialogClose",St=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,a=S(Tt,n);return o.jsx(M.button,{type:"button",...r,ref:t,onClick:x(e.onClick,()=>a.onOpenChange(!1))})});St.displayName=Tt;function Be(e){return e?"open":"closed"}var Pt="DialogTitleWarning",[Ps,At]=Vn(Pt,{contentName:z,titleName:Ke,docsSlug:"dialog"}),wo=({titleId:e})=>{const t=At(Pt),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Co="DialogDescriptionWarning",_o=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${At(Co).contentName}}.`;return i.useEffect(()=>{var s;const a=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},yo=wt,Mo=_t,jo=Mt,Ro=jt,Eo=Rt,Io=It,Do=Nt,No=St;function To({...e}){return o.jsx(yo,{"data-slot":"sheet",...e})}function As({...e}){return o.jsx(Mo,{"data-slot":"sheet-trigger",...e})}function So({...e}){return o.jsx(jo,{"data-slot":"sheet-portal",...e})}function Po({className:e,...t}){return o.jsx(Ro,{"data-slot":"sheet-overlay",className:C("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function Ao({className:e,children:t,side:n="right",...r}){return o.jsxs(So,{children:[o.jsx(Po,{}),o.jsxs(Eo,{"data-slot":"sheet-content",className:C("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,o.jsxs(No,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[o.jsx(mo,{className:"size-4"}),o.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Oo({className:e,...t}){return o.jsx("div",{"data-slot":"sheet-header",className:C("flex flex-col gap-1.5 p-4",e),...t})}function ko({className:e,...t}){return o.jsx(Io,{"data-slot":"sheet-title",className:C("text-foreground font-semibold",e),...t})}function Lo({className:e,...t}){return o.jsx(Do,{"data-slot":"sheet-description",className:C("text-muted-foreground text-sm",e),...t})}var[xe,Os]=Z("Tooltip",[ge]),ve=ge(),Ot="TooltipProvider",Fo=700,Se="tooltip.open",[$o,Ue]=xe(Ot),kt=e=>{const{__scopeTooltip:t,delayDuration:n=Fo,skipDelayDuration:r=300,disableHoverableContent:a=!1,children:s}=e,[c,l]=i.useState(!0),d=i.useRef(!1),p=i.useRef(0);return i.useEffect(()=>{const u=p.current;return()=>window.clearTimeout(u)},[]),o.jsx($o,{scope:t,isOpenDelayed:c,delayDuration:n,onOpen:i.useCallback(()=>{window.clearTimeout(p.current),l(!1)},[]),onClose:i.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:d,onPointerInTransitChange:i.useCallback(u=>{d.current=u},[]),disableHoverableContent:a,children:s})};kt.displayName=Ot;var be="Tooltip",[Go,se]=xe(be),Lt=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:a=!1,onOpenChange:s,disableHoverableContent:c,delayDuration:l}=e,d=Ue(be,e.__scopeTooltip),p=ve(t),[u,f]=i.useState(null),m=U(),h=i.useRef(0),g=c??d.disableHoverableContent,_=l??d.delayDuration,y=i.useRef(!1),[b=!1,w]=me({prop:r,defaultProp:a,onChange:A=>{A?(d.onOpen(),document.dispatchEvent(new CustomEvent(Se))):d.onClose(),s==null||s(A)}}),E=i.useMemo(()=>b?y.current?"delayed-open":"instant-open":"closed",[b]),P=i.useCallback(()=>{window.clearTimeout(h.current),h.current=0,y.current=!1,w(!0)},[w]),N=i.useCallback(()=>{window.clearTimeout(h.current),h.current=0,w(!1)},[w]),K=i.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{y.current=!0,w(!0),h.current=0},_)},[_,w]);return i.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),o.jsx(ut,{...p,children:o.jsx(Go,{scope:t,contentId:m,open:b,stateAttribute:E,trigger:u,onTriggerChange:f,onTriggerEnter:i.useCallback(()=>{d.isOpenDelayed?K():P()},[d.isOpenDelayed,K,P]),onTriggerLeave:i.useCallback(()=>{g?N():(window.clearTimeout(h.current),h.current=0)},[N,g]),onOpen:P,onClose:N,disableHoverableContent:g,children:n})})};Lt.displayName=be;var Pe="TooltipTrigger",Ft=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,a=se(Pe,n),s=Ue(Pe,n),c=ve(n),l=i.useRef(null),d=T(t,l,a.onTriggerChange),p=i.useRef(!1),u=i.useRef(!1),f=i.useCallback(()=>p.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),o.jsx(ft,{asChild:!0,...c,children:o.jsx(M.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...r,ref:d,onPointerMove:x(e.onPointerMove,m=>{m.pointerType!=="touch"&&!u.current&&!s.isPointerInTransitRef.current&&(a.onTriggerEnter(),u.current=!0)}),onPointerLeave:x(e.onPointerLeave,()=>{a.onTriggerLeave(),u.current=!1}),onPointerDown:x(e.onPointerDown,()=>{p.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:x(e.onFocus,()=>{p.current||a.onOpen()}),onBlur:x(e.onBlur,a.onClose),onClick:x(e.onClick,a.onClose)})})});Ft.displayName=Pe;var ze="TooltipPortal",[Ko,Bo]=xe(ze,{forceMount:void 0}),$t=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:a}=e,s=se(ze,t);return o.jsx(Ko,{scope:t,forceMount:n,children:o.jsx($,{present:n||s.open,children:o.jsx(Le,{asChild:!0,container:a,children:r})})})};$t.displayName=ze;var q="TooltipContent",Gt=i.forwardRef((e,t)=>{const n=Bo(q,e.__scopeTooltip),{forceMount:r=n.forceMount,side:a="top",...s}=e,c=se(q,e.__scopeTooltip);return o.jsx($,{present:r||c.open,children:c.disableHoverableContent?o.jsx(Kt,{side:a,...s,ref:t}):o.jsx(Uo,{side:a,...s,ref:t})})}),Uo=i.forwardRef((e,t)=>{const n=se(q,e.__scopeTooltip),r=Ue(q,e.__scopeTooltip),a=i.useRef(null),s=T(t,a),[c,l]=i.useState(null),{trigger:d,onClose:p}=n,u=a.current,{onPointerInTransitChange:f}=r,m=i.useCallback(()=>{l(null),f(!1)},[f]),h=i.useCallback((g,_)=>{const y=g.currentTarget,b={x:g.clientX,y:g.clientY},w=Vo(b,y.getBoundingClientRect()),E=Wo(b,w),P=Yo(_.getBoundingClientRect()),N=qo([...E,...P]);l(N),f(!0)},[f]);return i.useEffect(()=>()=>m(),[m]),i.useEffect(()=>{if(d&&u){const g=y=>h(y,u),_=y=>h(y,d);return d.addEventListener("pointerleave",g),u.addEventListener("pointerleave",_),()=>{d.removeEventListener("pointerleave",g),u.removeEventListener("pointerleave",_)}}},[d,u,h,m]),i.useEffect(()=>{if(c){const g=_=>{const y=_.target,b={x:_.clientX,y:_.clientY},w=(d==null?void 0:d.contains(y))||(u==null?void 0:u.contains(y)),E=!Xo(b,c);w?m():E&&(m(),p())};return document.addEventListener("pointermove",g),()=>document.removeEventListener("pointermove",g)}},[d,u,c,p,m]),o.jsx(Kt,{...e,ref:s})}),[zo,Ho]=xe(be,{isInside:!1}),Kt=i.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":a,onEscapeKeyDown:s,onPointerDownOutside:c,...l}=e,d=se(q,n),p=ve(n),{onClose:u}=d;return i.useEffect(()=>(document.addEventListener(Se,u),()=>document.removeEventListener(Se,u)),[u]),i.useEffect(()=>{if(d.trigger){const f=m=>{const h=m.target;h!=null&&h.contains(d.trigger)&&u()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[d.trigger,u]),o.jsx(Fe,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:f=>f.preventDefault(),onDismiss:u,children:o.jsxs(mt,{"data-state":d.stateAttribute,...p,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[o.jsx(Un,{children:r}),o.jsx(zo,{scope:n,isInside:!0,children:o.jsx(Wn,{id:d.contentId,role:"tooltip",children:a||r})})]})})});Gt.displayName=q;var Bt="TooltipArrow",Ut=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,a=ve(n);return Ho(Bt,n).isInside?null:o.jsx(pt,{...a,...r,ref:t})});Ut.displayName=Bt;function Vo(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,a,s)){case s:return"left";case a:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Wo(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Yo(e){const{top:t,right:n,bottom:r,left:a}=e;return[{x:a,y:t},{x:n,y:t},{x:n,y:r},{x:a,y:r}]}function Xo(e,t){const{x:n,y:r}=e;let a=!1;for(let s=0,c=t.length-1;s<t.length;c=s++){const l=t[s].x,d=t[s].y,p=t[c].x,u=t[c].y;d>r!=u>r&&n<(p-l)*(r-d)/(u-d)+l&&(a=!a)}return a}function qo(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Zo(t)}function Zo(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const a=e[r];for(;t.length>=2;){const s=t[t.length-1],c=t[t.length-2];if((s.x-c.x)*(a.y-c.y)>=(s.y-c.y)*(a.x-c.x))t.pop();else break}t.push(a)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const a=e[r];for(;n.length>=2;){const s=n[n.length-1],c=n[n.length-2];if((s.x-c.x)*(a.y-c.y)>=(s.y-c.y)*(a.x-c.x))n.pop();else break}n.push(a)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Jo=kt,Qo=Lt,er=Ft,tr=$t,nr=Gt,or=Ut;function zt({delayDuration:e=0,...t}){return o.jsx(Jo,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function rr({...e}){return o.jsx(zt,{children:o.jsx(Qo,{"data-slot":"tooltip",...e})})}function ar({...e}){return o.jsx(er,{"data-slot":"tooltip-trigger",...e})}function sr({className:e,sideOffset:t=4,children:n,...r}){return o.jsx(tr,{children:o.jsxs(nr,{"data-slot":"tooltip-content",sideOffset:t,className:C("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...r,children:[n,o.jsx(or,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const ir="sidebar_state",cr=60*60*24*7,lr="16rem",dr="18rem",ur="3rem",fr="b",Ht=i.createContext(null);function we(){const e=i.useContext(Ht);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function pr({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:a,children:s,...c}){const l=vt(),[d,p]=i.useState(!1),[u,f]=i.useState(e),m=t??u,h=i.useCallback(b=>{const w=typeof b=="function"?b(m):b;n?n(w):f(w),document.cookie=`${ir}=${w}; path=/; max-age=${cr}`},[n,m]),g=i.useCallback(()=>l?p(b=>!b):h(b=>!b),[l,h,p]);i.useEffect(()=>{const b=w=>{w.key===fr&&(w.metaKey||w.ctrlKey)&&(w.preventDefault(),g())};return window.addEventListener("keydown",b),()=>window.removeEventListener("keydown",b)},[g]);const _=m?"expanded":"collapsed",y=i.useMemo(()=>({state:_,open:m,setOpen:h,isMobile:l,openMobile:d,setOpenMobile:p,toggleSidebar:g}),[_,m,h,l,d,p,g]);return o.jsx(Ht.Provider,{value:y,children:o.jsx(zt,{delayDuration:0,children:o.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":lr,"--sidebar-width-icon":ur,...a},className:C("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...c,children:s})})})}function mr({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:a,...s}){const{isMobile:c,state:l,openMobile:d,setOpenMobile:p}=we();return n==="none"?o.jsx("div",{"data-slot":"sidebar",className:C("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...s,children:a}):c?o.jsxs(To,{open:d,onOpenChange:p,...s,children:[o.jsxs(Oo,{className:"sr-only",children:[o.jsx(ko,{children:"Sidebar"}),o.jsx(Lo,{children:"Displays the mobile sidebar."})]}),o.jsx(Ao,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":dr},side:e,children:o.jsx("div",{className:"flex h-full w-full flex-col",children:a})})]}):o.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":l==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[o.jsx("div",{className:C("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),o.jsx("div",{className:C("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...s,children:o.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:a})})]})}function ks({className:e,onClick:t,...n}){const{toggleSidebar:r}=we();return o.jsxs(Hn,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:C("h-7 w-7",e),onClick:a=>{t==null||t(a),r()},...n,children:[o.jsx(lo,{}),o.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function gr({className:e,...t}){return o.jsx("main",{"data-slot":"sidebar-inset",className:C("bg-background relative flex max-w-full min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function hr({className:e,...t}){return o.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:C("flex flex-col gap-2 p-2",e),...t})}function xr({className:e,...t}){return o.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:C("flex flex-col gap-2 p-2",e),...t})}function vr({className:e,...t}){return o.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:C("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function Vt({className:e,...t}){return o.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:C("relative flex w-full min-w-0 flex-col p-2",e),...t})}function br({className:e,asChild:t=!1,...n}){const r=t?ae:"div";return o.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:C("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function wr({className:e,...t}){return o.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:C("w-full text-sm",e),...t})}function Ce({className:e,...t}){return o.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:C("flex w-full min-w-0 flex-col gap-1",e),...t})}function _e({className:e,...t}){return o.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:C("group/menu-item relative",e),...t})}const Cr=zn("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function ye({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:a,className:s,...c}){const l=e?ae:"button",{isMobile:d,state:p}=we(),u=o.jsx(l,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:C(Cr({variant:n,size:r}),s),...c});return a?(typeof a=="string"&&(a={children:a}),o.jsxs(rr,{children:[o.jsx(ar,{asChild:!0,children:u}),o.jsx(sr,{side:"right",align:"center",hidden:p!=="collapsed"||d,...a})]})):u}function Ls({children:e,variant:t="header"}){const n=ke().props.sidebarOpen;return t==="header"?o.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):o.jsx(pr,{defaultOpen:n,children:e})}function _r({iconNode:e,className:t,...n}){return o.jsx(e,{className:C("h-4 w-4",t),...n})}function yr({items:e,className:t,...n}){return o.jsx(Vt,{...n,className:`group-data-[collapsible=icon]:p-0 ${t||""}`,children:o.jsx(wr,{children:o.jsx(Ce,{children:e.map(r=>o.jsx(_e,{children:o.jsx(ye,{asChild:!0,className:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100",children:o.jsxs("a",{href:r.href,target:"_blank",rel:"noopener noreferrer",children:[r.icon&&o.jsx(_r,{iconNode:r.icon,className:"h-5 w-5"}),o.jsx("span",{children:r.title})]})})},r.title))})})})}function Mr({items:e=[]}){const t=ke();return o.jsxs(Vt,{className:"px-2 py-0",children:[o.jsx(br,{children:"Platform"}),o.jsx(Ce,{children:e.map(n=>o.jsx(_e,{children:o.jsx(ye,{asChild:!0,isActive:t.url.startsWith(n.href),tooltip:{children:n.title},children:o.jsxs(ne,{href:n.href,prefetch:!0,children:[n.icon&&o.jsx(n.icon,{}),o.jsx("span",{children:n.title})]})})},n.title))})]})}var Te="rovingFocusGroup.onEntryFocus",jr={bubbles:!1,cancelable:!0},Me="RovingFocusGroup",[Ae,Wt,Rr]=gt(Me),[Er,Yt]=Z(Me,[Rr]),[Ir,Dr]=Er(Me),Xt=i.forwardRef((e,t)=>o.jsx(Ae.Provider,{scope:e.__scopeRovingFocusGroup,children:o.jsx(Ae.Slot,{scope:e.__scopeRovingFocusGroup,children:o.jsx(Nr,{...e,ref:t})})}));Xt.displayName=Me;var Nr=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:a=!1,dir:s,currentTabStopId:c,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:d,onEntryFocus:p,preventScrollOnEntryFocus:u=!1,...f}=e,m=i.useRef(null),h=T(t,m),g=ht(s),[_=null,y]=me({prop:c,defaultProp:l,onChange:d}),[b,w]=i.useState(!1),E=he(p),P=Wt(n),N=i.useRef(!1),[K,A]=i.useState(0);return i.useEffect(()=>{const j=m.current;if(j)return j.addEventListener(Te,E),()=>j.removeEventListener(Te,E)},[E]),o.jsx(Ir,{scope:n,orientation:r,dir:g,loop:a,currentTabStopId:_,onItemFocus:i.useCallback(j=>y(j),[y]),onItemShiftTab:i.useCallback(()=>w(!0),[]),onFocusableItemAdd:i.useCallback(()=>A(j=>j+1),[]),onFocusableItemRemove:i.useCallback(()=>A(j=>j-1),[]),children:o.jsx(M.div,{tabIndex:b||K===0?-1:0,"data-orientation":r,...f,ref:h,style:{outline:"none",...e.style},onMouseDown:x(e.onMouseDown,()=>{N.current=!0}),onFocus:x(e.onFocus,j=>{const W=!N.current;if(j.target===j.currentTarget&&W&&!b){const B=new CustomEvent(Te,jr);if(j.currentTarget.dispatchEvent(B),!B.defaultPrevented){const J=P().filter(k=>k.focusable),Q=J.find(k=>k.active),le=J.find(k=>k.id===_),Ee=[Q,le,...J].filter(Boolean).map(k=>k.ref.current);Jt(Ee,u)}}N.current=!1}),onBlur:x(e.onBlur,()=>w(!1))})})}),qt="RovingFocusGroupItem",Zt=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:a=!1,tabStopId:s,...c}=e,l=U(),d=s||l,p=Dr(qt,n),u=p.currentTabStopId===d,f=Wt(n),{onFocusableItemAdd:m,onFocusableItemRemove:h}=p;return i.useEffect(()=>{if(r)return m(),()=>h()},[r,m,h]),o.jsx(Ae.ItemSlot,{scope:n,id:d,focusable:r,active:a,children:o.jsx(M.span,{tabIndex:u?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:x(e.onMouseDown,g=>{r?p.onItemFocus(d):g.preventDefault()}),onFocus:x(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:x(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){p.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const _=Pr(g,p.orientation,p.dir);if(_!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let b=f().filter(w=>w.focusable).map(w=>w.ref.current);if(_==="last")b.reverse();else if(_==="prev"||_==="next"){_==="prev"&&b.reverse();const w=b.indexOf(g.currentTarget);b=p.loop?Ar(b,w+1):b.slice(w+1)}setTimeout(()=>Jt(b))}})})})});Zt.displayName=qt;var Tr={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Sr(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Pr(e,t,n){const r=Sr(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Tr[r]}function Jt(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Ar(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Or=Xt,kr=Zt,Oe=["Enter"," "],Lr=["ArrowDown","PageUp","Home"],Qt=["ArrowUp","PageDown","End"],Fr=[...Lr,...Qt],$r={ltr:[...Oe,"ArrowRight"],rtl:[...Oe,"ArrowLeft"]},Gr={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ie="Menu",[oe,Kr,Br]=gt(ie),[H,en]=Z(ie,[Br,ge,Yt]),je=ge(),tn=Yt(),[Ur,V]=H(ie),[zr,ce]=H(ie),nn=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:a,onOpenChange:s,modal:c=!0}=e,l=je(t),[d,p]=i.useState(null),u=i.useRef(!1),f=he(s),m=ht(a);return i.useEffect(()=>{const h=()=>{u.current=!0,document.addEventListener("pointerdown",g,{capture:!0,once:!0}),document.addEventListener("pointermove",g,{capture:!0,once:!0})},g=()=>u.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",g,{capture:!0}),document.removeEventListener("pointermove",g,{capture:!0})}},[]),o.jsx(ut,{...l,children:o.jsx(Ur,{scope:t,open:n,onOpenChange:f,content:d,onContentChange:p,children:o.jsx(zr,{scope:t,onClose:i.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:u,dir:m,modal:c,children:r})})})};nn.displayName=ie;var Hr="MenuAnchor",He=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,a=je(n);return o.jsx(ft,{...a,...r,ref:t})});He.displayName=Hr;var Ve="MenuPortal",[Vr,on]=H(Ve,{forceMount:void 0}),rn=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:a}=e,s=V(Ve,t);return o.jsx(Vr,{scope:t,forceMount:n,children:o.jsx($,{present:n||s.open,children:o.jsx(Le,{asChild:!0,container:a,children:r})})})};rn.displayName=Ve;var D="MenuContent",[Wr,We]=H(D),an=i.forwardRef((e,t)=>{const n=on(D,e.__scopeMenu),{forceMount:r=n.forceMount,...a}=e,s=V(D,e.__scopeMenu),c=ce(D,e.__scopeMenu);return o.jsx(oe.Provider,{scope:e.__scopeMenu,children:o.jsx($,{present:r||s.open,children:o.jsx(oe.Slot,{scope:e.__scopeMenu,children:c.modal?o.jsx(Yr,{...a,ref:t}):o.jsx(Xr,{...a,ref:t})})})})}),Yr=i.forwardRef((e,t)=>{const n=V(D,e.__scopeMenu),r=i.useRef(null),a=T(t,r);return i.useEffect(()=>{const s=r.current;if(s)return it(s)},[]),o.jsx(Ye,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:x(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Xr=i.forwardRef((e,t)=>{const n=V(D,e.__scopeMenu);return o.jsx(Ye,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Ye=i.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:a,onOpenAutoFocus:s,onCloseAutoFocus:c,disableOutsidePointerEvents:l,onEntryFocus:d,onEscapeKeyDown:p,onPointerDownOutside:u,onFocusOutside:f,onInteractOutside:m,onDismiss:h,disableOutsideScroll:g,..._}=e,y=V(D,n),b=ce(D,n),w=je(n),E=tn(n),P=Kr(n),[N,K]=i.useState(null),A=i.useRef(null),j=T(t,A,y.onContentChange),W=i.useRef(0),B=i.useRef(""),J=i.useRef(0),Q=i.useRef(null),le=i.useRef("right"),de=i.useRef(0),Ee=g?ct:i.Fragment,k=g?{as:ae,allowPinchZoom:!0}:void 0,Kn=v=>{var X,tt;const I=B.current+v,O=P().filter(L=>!L.disabled),G=document.activeElement,Ie=(X=O.find(L=>L.ref.current===G))==null?void 0:X.textValue,De=O.map(L=>L.textValue),et=ia(De,I,Ie),ee=(tt=O.find(L=>L.textValue===et))==null?void 0:tt.ref.current;(function L(nt){B.current=nt,window.clearTimeout(W.current),nt!==""&&(W.current=window.setTimeout(()=>L(""),1e3))})(I),ee&&setTimeout(()=>ee.focus())};i.useEffect(()=>()=>window.clearTimeout(W.current),[]),lt();const Y=i.useCallback(v=>{var O,G;return le.current===((O=Q.current)==null?void 0:O.side)&&la(v,(G=Q.current)==null?void 0:G.area)},[]);return o.jsx(Wr,{scope:n,searchRef:B,onItemEnter:i.useCallback(v=>{Y(v)&&v.preventDefault()},[Y]),onItemLeave:i.useCallback(v=>{var I;Y(v)||((I=A.current)==null||I.focus(),K(null))},[Y]),onTriggerLeave:i.useCallback(v=>{Y(v)&&v.preventDefault()},[Y]),pointerGraceTimerRef:J,onPointerGraceIntentChange:i.useCallback(v=>{Q.current=v},[]),children:o.jsx(Ee,{...k,children:o.jsx(dt,{asChild:!0,trapped:a,onMountAutoFocus:x(s,v=>{var I;v.preventDefault(),(I=A.current)==null||I.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:o.jsx(Fe,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:p,onPointerDownOutside:u,onFocusOutside:f,onInteractOutside:m,onDismiss:h,children:o.jsx(Or,{asChild:!0,...E,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:N,onCurrentTabStopIdChange:K,onEntryFocus:x(d,v=>{b.isUsingKeyboardRef.current||v.preventDefault()}),preventScrollOnEntryFocus:!0,children:o.jsx(mt,{role:"menu","aria-orientation":"vertical","data-state":_n(y.open),"data-radix-menu-content":"",dir:b.dir,...w,..._,ref:j,style:{outline:"none",..._.style},onKeyDown:x(_.onKeyDown,v=>{const O=v.target.closest("[data-radix-menu-content]")===v.currentTarget,G=v.ctrlKey||v.altKey||v.metaKey,Ie=v.key.length===1;O&&(v.key==="Tab"&&v.preventDefault(),!G&&Ie&&Kn(v.key));const De=A.current;if(v.target!==De||!Fr.includes(v.key))return;v.preventDefault();const ee=P().filter(X=>!X.disabled).map(X=>X.ref.current);Qt.includes(v.key)&&ee.reverse(),aa(ee)}),onBlur:x(e.onBlur,v=>{v.currentTarget.contains(v.target)||(window.clearTimeout(W.current),B.current="")}),onPointerMove:x(e.onPointerMove,re(v=>{const I=v.target,O=de.current!==v.clientX;if(v.currentTarget.contains(I)&&O){const G=v.clientX>de.current?"right":"left";le.current=G,de.current=v.clientX}}))})})})})})})});an.displayName=D;var qr="MenuGroup",Xe=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return o.jsx(M.div,{role:"group",...r,ref:t})});Xe.displayName=qr;var Zr="MenuLabel",sn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return o.jsx(M.div,{...r,ref:t})});sn.displayName=Zr;var fe="MenuItem",ot="menu.itemSelect",Re=i.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...a}=e,s=i.useRef(null),c=ce(fe,e.__scopeMenu),l=We(fe,e.__scopeMenu),d=T(t,s),p=i.useRef(!1),u=()=>{const f=s.current;if(!n&&f){const m=new CustomEvent(ot,{bubbles:!0,cancelable:!0});f.addEventListener(ot,h=>r==null?void 0:r(h),{once:!0}),Yn(f,m),m.defaultPrevented?p.current=!1:c.onClose()}};return o.jsx(cn,{...a,ref:d,disabled:n,onClick:x(e.onClick,u),onPointerDown:f=>{var m;(m=e.onPointerDown)==null||m.call(e,f),p.current=!0},onPointerUp:x(e.onPointerUp,f=>{var m;p.current||(m=f.currentTarget)==null||m.click()}),onKeyDown:x(e.onKeyDown,f=>{const m=l.searchRef.current!=="";n||m&&f.key===" "||Oe.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})})});Re.displayName=fe;var cn=i.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:a,...s}=e,c=We(fe,n),l=tn(n),d=i.useRef(null),p=T(t,d),[u,f]=i.useState(!1),[m,h]=i.useState("");return i.useEffect(()=>{const g=d.current;g&&h((g.textContent??"").trim())},[s.children]),o.jsx(oe.ItemSlot,{scope:n,disabled:r,textValue:a??m,children:o.jsx(kr,{asChild:!0,...l,focusable:!r,children:o.jsx(M.div,{role:"menuitem","data-highlighted":u?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:p,onPointerMove:x(e.onPointerMove,re(g=>{r?c.onItemLeave(g):(c.onItemEnter(g),g.defaultPrevented||g.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:x(e.onPointerLeave,re(g=>c.onItemLeave(g))),onFocus:x(e.onFocus,()=>f(!0)),onBlur:x(e.onBlur,()=>f(!1))})})})}),Jr="MenuCheckboxItem",ln=i.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...a}=e;return o.jsx(mn,{scope:e.__scopeMenu,checked:n,children:o.jsx(Re,{role:"menuitemcheckbox","aria-checked":pe(n)?"mixed":n,...a,ref:t,"data-state":Ze(n),onSelect:x(a.onSelect,()=>r==null?void 0:r(pe(n)?!0:!n),{checkForDefaultPrevented:!1})})})});ln.displayName=Jr;var dn="MenuRadioGroup",[Qr,ea]=H(dn,{value:void 0,onValueChange:()=>{}}),un=i.forwardRef((e,t)=>{const{value:n,onValueChange:r,...a}=e,s=he(r);return o.jsx(Qr,{scope:e.__scopeMenu,value:n,onValueChange:s,children:o.jsx(Xe,{...a,ref:t})})});un.displayName=dn;var fn="MenuRadioItem",pn=i.forwardRef((e,t)=>{const{value:n,...r}=e,a=ea(fn,e.__scopeMenu),s=n===a.value;return o.jsx(mn,{scope:e.__scopeMenu,checked:s,children:o.jsx(Re,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":Ze(s),onSelect:x(r.onSelect,()=>{var c;return(c=a.onValueChange)==null?void 0:c.call(a,n)},{checkForDefaultPrevented:!1})})})});pn.displayName=fn;var qe="MenuItemIndicator",[mn,ta]=H(qe,{checked:!1}),gn=i.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...a}=e,s=ta(qe,n);return o.jsx($,{present:r||pe(s.checked)||s.checked===!0,children:o.jsx(M.span,{...a,ref:t,"data-state":Ze(s.checked)})})});gn.displayName=qe;var na="MenuSeparator",hn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return o.jsx(M.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});hn.displayName=na;var oa="MenuArrow",xn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,a=je(n);return o.jsx(pt,{...a,...r,ref:t})});xn.displayName=oa;var ra="MenuSub",[Fs,vn]=H(ra),te="MenuSubTrigger",bn=i.forwardRef((e,t)=>{const n=V(te,e.__scopeMenu),r=ce(te,e.__scopeMenu),a=vn(te,e.__scopeMenu),s=We(te,e.__scopeMenu),c=i.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:d}=s,p={__scopeMenu:e.__scopeMenu},u=i.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return i.useEffect(()=>u,[u]),i.useEffect(()=>{const f=l.current;return()=>{window.clearTimeout(f),d(null)}},[l,d]),o.jsx(He,{asChild:!0,...p,children:o.jsx(cn,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":_n(n.open),...e,ref:st(t,a.onTriggerChange),onClick:f=>{var m;(m=e.onClick)==null||m.call(e,f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:x(e.onPointerMove,re(f=>{s.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!n.open&&!c.current&&(s.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),u()},100))})),onPointerLeave:x(e.onPointerLeave,re(f=>{var h,g;u();const m=(h=n.content)==null?void 0:h.getBoundingClientRect();if(m){const _=(g=n.content)==null?void 0:g.dataset.side,y=_==="right",b=y?-5:5,w=m[y?"left":"right"],E=m[y?"right":"left"];s.onPointerGraceIntentChange({area:[{x:f.clientX+b,y:f.clientY},{x:w,y:m.top},{x:E,y:m.top},{x:E,y:m.bottom},{x:w,y:m.bottom}],side:_}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(f),f.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:x(e.onKeyDown,f=>{var h;const m=s.searchRef.current!=="";e.disabled||m&&f.key===" "||$r[r.dir].includes(f.key)&&(n.onOpenChange(!0),(h=n.content)==null||h.focus(),f.preventDefault())})})})});bn.displayName=te;var wn="MenuSubContent",Cn=i.forwardRef((e,t)=>{const n=on(D,e.__scopeMenu),{forceMount:r=n.forceMount,...a}=e,s=V(D,e.__scopeMenu),c=ce(D,e.__scopeMenu),l=vn(wn,e.__scopeMenu),d=i.useRef(null),p=T(t,d);return o.jsx(oe.Provider,{scope:e.__scopeMenu,children:o.jsx($,{present:r||s.open,children:o.jsx(oe.Slot,{scope:e.__scopeMenu,children:o.jsx(Ye,{id:l.contentId,"aria-labelledby":l.triggerId,...a,ref:p,align:"start",side:c.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:u=>{var f;c.isUsingKeyboardRef.current&&((f=d.current)==null||f.focus()),u.preventDefault()},onCloseAutoFocus:u=>u.preventDefault(),onFocusOutside:x(e.onFocusOutside,u=>{u.target!==l.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:x(e.onEscapeKeyDown,u=>{c.onClose(),u.preventDefault()}),onKeyDown:x(e.onKeyDown,u=>{var h;const f=u.currentTarget.contains(u.target),m=Gr[c.dir].includes(u.key);f&&m&&(s.onOpenChange(!1),(h=l.trigger)==null||h.focus(),u.preventDefault())})})})})})});Cn.displayName=wn;function _n(e){return e?"open":"closed"}function pe(e){return e==="indeterminate"}function Ze(e){return pe(e)?"indeterminate":e?"checked":"unchecked"}function aa(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function sa(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function ia(e,t,n){const a=t.length>1&&Array.from(t).every(p=>p===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let c=sa(e,Math.max(s,0));a.length===1&&(c=c.filter(p=>p!==n));const d=c.find(p=>p.toLowerCase().startsWith(a.toLowerCase()));return d!==n?d:void 0}function ca(e,t){const{x:n,y:r}=e;let a=!1;for(let s=0,c=t.length-1;s<t.length;c=s++){const l=t[s].x,d=t[s].y,p=t[c].x,u=t[c].y;d>r!=u>r&&n<(p-l)*(r-d)/(u-d)+l&&(a=!a)}return a}function la(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return ca(n,t)}function re(e){return t=>t.pointerType==="mouse"?e(t):void 0}var da=nn,ua=He,fa=rn,pa=an,ma=Xe,ga=sn,ha=Re,xa=ln,va=un,ba=pn,wa=gn,Ca=hn,_a=xn,ya=bn,Ma=Cn,Je="DropdownMenu",[ja,$s]=Z(Je,[en]),R=en(),[Ra,yn]=ja(Je),Mn=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:a,defaultOpen:s,onOpenChange:c,modal:l=!0}=e,d=R(t),p=i.useRef(null),[u=!1,f]=me({prop:a,defaultProp:s,onChange:c});return o.jsx(Ra,{scope:t,triggerId:U(),triggerRef:p,contentId:U(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(m=>!m),[f]),modal:l,children:o.jsx(da,{...d,open:u,onOpenChange:f,dir:r,modal:l,children:n})})};Mn.displayName=Je;var jn="DropdownMenuTrigger",Rn=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,s=yn(jn,n),c=R(n);return o.jsx(ua,{asChild:!0,...c,children:o.jsx(M.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:st(t,s.triggerRef),onPointerDown:x(e.onPointerDown,l=>{!r&&l.button===0&&l.ctrlKey===!1&&(s.onOpenToggle(),s.open||l.preventDefault())}),onKeyDown:x(e.onKeyDown,l=>{r||(["Enter"," "].includes(l.key)&&s.onOpenToggle(),l.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())})})})});Rn.displayName=jn;var Ea="DropdownMenuPortal",En=e=>{const{__scopeDropdownMenu:t,...n}=e,r=R(t);return o.jsx(fa,{...r,...n})};En.displayName=Ea;var In="DropdownMenuContent",Dn=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=yn(In,n),s=R(n),c=i.useRef(!1);return o.jsx(pa,{id:a.contentId,"aria-labelledby":a.triggerId,...s,...r,ref:t,onCloseAutoFocus:x(e.onCloseAutoFocus,l=>{var d;c.current||(d=a.triggerRef.current)==null||d.focus(),c.current=!1,l.preventDefault()}),onInteractOutside:x(e.onInteractOutside,l=>{const d=l.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0,u=d.button===2||p;(!a.modal||u)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Dn.displayName=In;var Ia="DropdownMenuGroup",Nn=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(ma,{...a,...r,ref:t})});Nn.displayName=Ia;var Da="DropdownMenuLabel",Tn=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(ga,{...a,...r,ref:t})});Tn.displayName=Da;var Na="DropdownMenuItem",Sn=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(ha,{...a,...r,ref:t})});Sn.displayName=Na;var Ta="DropdownMenuCheckboxItem",Sa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(xa,{...a,...r,ref:t})});Sa.displayName=Ta;var Pa="DropdownMenuRadioGroup",Aa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(va,{...a,...r,ref:t})});Aa.displayName=Pa;var Oa="DropdownMenuRadioItem",ka=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(ba,{...a,...r,ref:t})});ka.displayName=Oa;var La="DropdownMenuItemIndicator",Fa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(wa,{...a,...r,ref:t})});Fa.displayName=La;var $a="DropdownMenuSeparator",Pn=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(Ca,{...a,...r,ref:t})});Pn.displayName=$a;var Ga="DropdownMenuArrow",Ka=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(_a,{...a,...r,ref:t})});Ka.displayName=Ga;var Ba="DropdownMenuSubTrigger",Ua=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(ya,{...a,...r,ref:t})});Ua.displayName=Ba;var za="DropdownMenuSubContent",Ha=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=R(n);return o.jsx(Ma,{...a,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ha.displayName=za;var Va=Mn,Wa=Rn,Ya=En,Xa=Dn,qa=Nn,Za=Tn,Ja=Sn,Qa=Pn;function es({...e}){return o.jsx(Va,{"data-slot":"dropdown-menu",...e})}function ts({...e}){return o.jsx(Wa,{"data-slot":"dropdown-menu-trigger",...e})}function ns({className:e,sideOffset:t=4,...n}){return o.jsx(Ya,{children:o.jsx(Xa,{"data-slot":"dropdown-menu-content",sideOffset:t,className:C("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function os({...e}){return o.jsx(qa,{"data-slot":"dropdown-menu-group",...e})}function rt({className:e,inset:t,variant:n="default",...r}){return o.jsx(Ja,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:C("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function rs({className:e,inset:t,...n}){return o.jsx(Za,{"data-slot":"dropdown-menu-label","data-inset":t,className:C("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function at({className:e,...t}){return o.jsx(Qa,{"data-slot":"dropdown-menu-separator",className:C("bg-border -mx-1 my-1 h-px",e),...t})}var Qe="Avatar",[as,Gs]=Z(Qe),[ss,An]=as(Qe),On=i.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[a,s]=i.useState("idle");return o.jsx(ss,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:s,children:o.jsx(M.span,{...r,ref:t})})});On.displayName=Qe;var kn="AvatarImage",Ln=i.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:a=()=>{},...s}=e,c=An(kn,n),l=is(r,s.referrerPolicy),d=he(p=>{a(p),c.onImageLoadingStatusChange(p)});return xt(()=>{l!=="idle"&&d(l)},[l,d]),l==="loaded"?o.jsx(M.img,{...s,ref:t,src:r}):null});Ln.displayName=kn;var Fn="AvatarFallback",$n=i.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...a}=e,s=An(Fn,n),[c,l]=i.useState(r===void 0);return i.useEffect(()=>{if(r!==void 0){const d=window.setTimeout(()=>l(!0),r);return()=>window.clearTimeout(d)}},[r]),c&&s.imageLoadingStatus!=="loaded"?o.jsx(M.span,{...a,ref:t}):null});$n.displayName=Fn;function is(e,t){const[n,r]=i.useState("idle");return xt(()=>{if(!e){r("error");return}let a=!0;const s=new window.Image,c=l=>()=>{a&&r(l)};return r("loading"),s.onload=c("loaded"),s.onerror=c("error"),s.src=e,t&&(s.referrerPolicy=t),()=>{a=!1}},[e,t]),n}var cs=On,ls=Ln,ds=$n;function us({className:e,...t}){return o.jsx(cs,{"data-slot":"avatar",className:C("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function fs({className:e,...t}){return o.jsx(ls,{"data-slot":"avatar-image",className:C("aspect-square size-full",e),...t})}function ps({className:e,...t}){return o.jsx(ds,{"data-slot":"avatar-fallback",className:C("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function ms(){return i.useCallback(e=>{const t=e.trim().split(" ");if(t.length===0)return"";if(t.length===1)return t[0].charAt(0).toUpperCase();const n=t[0].charAt(0),r=t[t.length-1].charAt(0);return`${n}${r}`.toUpperCase()},[])}function Gn({user:e,showEmail:t=!1}){const n=ms();return o.jsxs(o.Fragment,{children:[o.jsxs(us,{className:"h-8 w-8 overflow-hidden rounded-full",children:[o.jsx(fs,{src:e.avatar,alt:e.name}),o.jsx(ps,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:n(e.name)})]}),o.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[o.jsx("span",{className:"truncate font-medium",children:e.name}),t&&o.jsx("span",{className:"truncate text-xs text-muted-foreground",children:e.email})]})]})}function gs(){return i.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function hs({user:e}){const t=gs(),n=()=>{t(),Bn.flushAll()};return o.jsxs(o.Fragment,{children:[o.jsx(rs,{className:"p-0 font-normal",children:o.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:o.jsx(Gn,{user:e,showEmail:!0})})}),o.jsx(at,{}),o.jsx(os,{children:o.jsx(rt,{asChild:!0,children:o.jsxs(ne,{className:"block w-full",href:route("profile.edit"),as:"button",prefetch:!0,onClick:t,children:[o.jsx(fo,{className:"mr-2"}),"Settings"]})})}),o.jsx(at,{}),o.jsx(rt,{asChild:!0,children:o.jsxs(ne,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:n,children:[o.jsx(io,{className:"mr-2"}),"Log out"]})})]})}function xs(){const{auth:e}=ke().props,{state:t}=we(),n=vt();return o.jsx(Ce,{children:o.jsx(_e,{children:o.jsxs(es,{children:[o.jsx(ts,{asChild:!0,children:o.jsxs(ye,{size:"lg",className:"group text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent",children:[o.jsx(Gn,{user:e.user}),o.jsx(to,{className:"ml-auto size-4"})]})}),o.jsx(ns,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:n?"bottom":t==="collapsed"?"left":"bottom",children:o.jsx(hs,{user:e.user})})]})})})}function vs(){return o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground",children:o.jsx(Xn,{className:"size-5 fill-current text-white dark:text-black"})}),o.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm",children:o.jsx("span",{className:"mb-0.5 truncate leading-tight font-semibold",children:"Laravel Starter Kit"})})]})}const bs=[{title:"Dashboard",href:"/dashboard",icon:ao}],ws=[{title:"Repository",href:"https://github.com/laravel/react-starter-kit",icon:oo},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#react",icon:Zn}];function Ks({user:e,navigation:t}){const n=t||bs;return o.jsxs(mr,{collapsible:"icon",variant:"inset",children:[o.jsx(hr,{children:o.jsx(Ce,{children:o.jsx(_e,{children:o.jsx(ye,{size:"lg",asChild:!0,children:o.jsx(ne,{href:"/dashboard",prefetch:!0,children:o.jsx(vs,{})})})})})}),o.jsx(vr,{children:o.jsx(Mr,{items:n})}),o.jsxs(xr,{children:[o.jsx(yr,{items:ws,className:"mt-auto"}),o.jsx(xs,{user:e})]})]})}function Cs({...e}){return o.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function _s({className:e,...t}){return o.jsx("ol",{"data-slot":"breadcrumb-list",className:C("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function ys({className:e,...t}){return o.jsx("li",{"data-slot":"breadcrumb-item",className:C("inline-flex items-center gap-1.5",e),...t})}function Ms({asChild:e,className:t,...n}){const r=e?ae:"a";return o.jsx(r,{"data-slot":"breadcrumb-link",className:C("hover:text-foreground transition-colors",t),...n})}function js({className:e,...t}){return o.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:C("text-foreground font-normal",e),...t})}function Rs({children:e,className:t,...n}){return o.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:C("[&>svg]:size-3.5",t),...n,children:e??o.jsx(Qn,{})})}function Bs({breadcrumbs:e}){return o.jsx(o.Fragment,{children:e.length>0&&o.jsx(Cs,{children:o.jsx(_s,{children:e.map((t,n)=>{const r=n===e.length-1;return o.jsxs(i.Fragment,{children:[o.jsx(ys,{children:r?o.jsx(js,{children:t.title}):o.jsx(Ms,{asChild:!0,children:o.jsx(ne,{href:t.href,children:t.title})})}),!r&&o.jsx(Rs,{})]},n)})})})})}function Us({variant:e="header",children:t,...n}){return e==="sidebar"?o.jsx(gr,{...n,children:t}):o.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}export{vs as A,Zn as B,Eo as C,es as D,oo as F,_r as I,ao as L,Ro as O,jo as P,yo as R,To as S,zt as T,hs as U,mo as X,As as a,Ao as b,ko as c,Oo as d,rr as e,ar as f,sr as g,ts as h,us as i,fs as j,ps as k,ns as l,Bs as m,Ls as n,Ks as o,Us as p,fo as q,Mo as r,No as s,Io as t,ms as u,Do as v,ks as w};
