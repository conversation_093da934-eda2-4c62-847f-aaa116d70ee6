import{j as r}from"./app-BRSV0K6a.js";import{d as e}from"./button-DeV0q5_x.js";function o({className:a,...t}){return r.jsx("div",{"data-slot":"card",className:e("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n({className:a,...t}){return r.jsx("div",{"data-slot":"card-header",className:e("flex flex-col gap-1.5 px-6",a),...t})}function c({className:a,...t}){return r.jsx("div",{"data-slot":"card-title",className:e("leading-none font-semibold",a),...t})}function i({className:a,...t}){return r.jsx("div",{"data-slot":"card-description",className:e("text-muted-foreground text-sm",a),...t})}function l({className:a,...t}){return r.jsx("div",{"data-slot":"card-content",className:e("px-6",a),...t})}export{o as C,n as a,c as b,l as c,i as d};
