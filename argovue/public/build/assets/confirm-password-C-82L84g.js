import{m,j as s,L as d}from"./app-VriuOT-w.js";import{I as l}from"./input-error-CbHOjh7Q.js";import{B as c}from"./button-DmNtpphn.js";import{I as u}from"./input-SLDmXLDt.js";import{L as f}from"./label-ab2XlmWX.js";import{A as w,L as h}from"./auth-layout-x3U1QdNr.js";/* empty css            */import"./index-C_pbH-b0.js";import"./app-logo-icon-Cr4Vd7YU.js";function I(){const{data:a,setData:e,post:t,processing:o,errors:i,reset:p}=m({password:""}),n=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>p("password")})};return s.jsxs(w,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(d,{title:"Confirm password"}),s.jsx("form",{onSubmit:n,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(f,{htmlFor:"password",children:"Password"}),s.jsx(u,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:a.password,autoFocus:!0,onChange:r=>e("password",r.target.value)}),s.jsx(l,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(c,{className:"w-full",disabled:o,children:[o&&s.jsx(h,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{I as default};
