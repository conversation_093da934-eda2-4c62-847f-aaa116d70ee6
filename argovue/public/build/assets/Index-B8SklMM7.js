import{j as e,L as w}from"./app-VriuOT-w.js";import{A as v,M as j,D as u,W as y}from"./AgroVueLayout-DP_upes5.js";import{C as s,a as l,b as t,c as r,d as A}from"./card-CAEcdXY_.js";import{B as f,F as g,T as n}from"./badge-BSQoYK3G.js";import{c as C,B as x}from"./button-DmNtpphn.js";/* empty css            */import"./app-content-D0eHL4FB.js";import"./index-CN3oWmC9.js";import"./index-C_pbH-b0.js";import"./app-logo-icon-Cr4Vd7YU.js";import"./index-Cp-5hxoe.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],L=C("Activity",F);function H({user:c,dashboardData:a}){var m;const p=()=>{var i,d,o,h;return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"Total Fields"}),e.jsx(y,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:((i=a.totalFields)==null?void 0:i.toLocaleString())||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Registered fields"})]})]}),e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"Pending Claims"}),e.jsx(g,{className:"h-4 w-4 text-yellow-500"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:((d=a.pendingClaims)==null?void 0:d.toLocaleString())||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Awaiting review"})]})]}),e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"Flagged Claims"}),e.jsx(n,{className:"h-4 w-4 text-red-500"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:((o=a.flaggedClaims)==null?void 0:o.toLocaleString())||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"High fraud risk"})]})]}),e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"Total Subsidies"}),e.jsx(u,{className:"h-4 w-4 text-green-500"})]}),e.jsxs(r,{children:[e.jsxs("div",{className:"text-2xl font-bold",children:["₦",((h=a.totalSubsidyAmount)==null?void 0:h.toLocaleString())||0]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Approved amount"})]})]})]})},N=()=>{var i,d;return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"My Fields"}),e.jsx(j,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:a.myFields||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Registered fields"})]})]}),e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"Active Alerts"}),e.jsx(n,{className:"h-4 w-4 text-yellow-500"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:((i=a.myAlerts)==null?void 0:i.length)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Require attention"})]})]}),e.jsxs(s,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(t,{className:"text-sm font-medium",children:"Subsidy Claims"}),e.jsx(u,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:((d=a.myClaims)==null?void 0:d.length)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Total applications"})]})]})]})},b=()=>{switch(c.role){case"government_admin":return p();case"farmer":return N();default:return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:e.jsxs(s,{children:[e.jsxs(l,{children:[e.jsx(t,{children:"Welcome to AgroVue"}),e.jsx(A,{children:"Your agricultural intelligence platform"})]}),e.jsx(r,{children:e.jsx("p",{children:"Get started by exploring the features available for your role."})})]})})}};return e.jsxs(v,{title:"Dashboard",breadcrumbs:[{label:"Dashboard"}],children:[e.jsx(w,{title:"Dashboard"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold tracking-tight",children:["Welcome back, ",c.name]}),e.jsxs("p",{className:"text-muted-foreground",children:[c.role.replace("_"," ").replace(/\b\w/g,i=>i.toUpperCase())," Dashboard"]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{variant:"outline",children:((m=c.organization)==null?void 0:m.name)||"Independent"}),e.jsx(f,{variant:"secondary",children:c.role.replace("_"," ").replace(/\b\w/g,i=>i.toUpperCase())})]})]}),b(),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(s,{children:[e.jsx(l,{children:e.jsxs(t,{className:"flex items-center",children:[e.jsx(L,{className:"h-5 w-5 mr-2"}),"Recent Activity"]})}),e.jsx(r,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Field monitoring updated"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"2 minutes ago"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"New satellite data received"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"15 minutes ago"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Alert generated for drought risk"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"1 hour ago"})]})]})]})})]}),e.jsxs(s,{children:[e.jsx(l,{children:e.jsx(t,{children:"Quick Actions"})}),e.jsxs(r,{className:"space-y-3",children:[e.jsxs(x,{className:"w-full justify-start",variant:"outline",children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"View Field Map"]}),e.jsxs(x,{className:"w-full justify-start",variant:"outline",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Generate Report"]}),e.jsxs(x,{className:"w-full justify-start",variant:"outline",children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"View Active Alerts"]})]})]})]})]})]})}export{H as default};
