import{c as _,u as O,a as _e,b as Ae,d as D,B as G}from"./button-DeV0q5_x.js";import{r as c,j as e,K as ue,$ as H,L as Pe}from"./app-BRSV0K6a.js";import{u as Se,S as De,a as Le,b as Fe,c as Oe,d as Ve,L as Ke,I as V,F as ze,B as $e,A as Ue,T as Ge,e as He,f as Be,g as qe,D as We,h as Ze,i as Ye,j as Xe,k as Je,l as Qe,U as et,m as de,n as tt,o as nt,p as ot,q as rt}from"./app-content-DoSKBbyq.js";import{P as I,d as re,R as at}from"./index-D2bTj4tq.js";import{u as st,a as fe,c as it,b as ve,d as me,e as S,P as $,f as R,g as q,D as ct,R as lt}from"./index-SRmR8U3Z.js";import{u as ut}from"./index-CwMx2vkM.js";import{A as dt}from"./app-logo-icon-uNlWQ8Ti.js";import{T as ae,F as ft}from"./badge-BPiyidbe.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vt=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],mt=_("ChartColumn",vt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pt=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],B=_("DollarSign",pt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gt=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],mn=_("MapPin",gt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]],xt=_("Map",ht);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wt=[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]],Nt=_("Menu",wt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Mt=_("MessageSquare",yt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bt=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],Ct=_("Search",bt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jt=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]],Rt=_("Users",jt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=[["path",{d:"M2 22 16 8",key:"60hf96"}],["path",{d:"M3.47 12.53 5 11l1.53 1.53a3.5 3.5 0 0 1 0 4.94L5 19l-1.53-1.53a3.5 3.5 0 0 1 0-4.94Z",key:"1rdhi6"}],["path",{d:"M7.47 8.53 9 7l1.53 1.53a3.5 3.5 0 0 1 0 4.94L9 15l-1.53-1.53a3.5 3.5 0 0 1 0-4.94Z",key:"1sdzmb"}],["path",{d:"M11.47 4.53 13 3l1.53 1.53a3.5 3.5 0 0 1 0 4.94L13 11l-1.53-1.53a3.5 3.5 0 0 1 0-4.94Z",key:"eoatbi"}],["path",{d:"M20 2h2v2a4 4 0 0 1-4 4h-2V6a4 4 0 0 1 4-4Z",key:"19rau1"}],["path",{d:"M11.47 17.47 13 19l-1.53 1.53a3.5 3.5 0 0 1-4.94 0L5 19l1.53-1.53a3.5 3.5 0 0 1 4.94 0Z",key:"tc8ph9"}],["path",{d:"M15.47 13.47 17 15l-1.53 1.53a3.5 3.5 0 0 1-4.94 0L9 15l1.53-1.53a3.5 3.5 0 0 1 4.94 0Z",key:"2m8kc5"}],["path",{d:"M19.47 9.47 21 11l-1.53 1.53a3.5 3.5 0 0 1-4.94 0L13 11l1.53-1.53a3.5 3.5 0 0 1 4.94 0Z",key:"vex3ng"}]],Et=_("Wheat",kt);var F="NavigationMenu",[J,pe,It]=ve(F),[W,Tt,_t]=ve(F),[Q,pn]=it(F,[It,_t]),[At,E]=Q(F),[Pt,St]=Q(F),ge=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,value:a,onValueChange:n,defaultValue:s,delayDuration:i=200,skipDelayDuration:d=300,orientation:l="horizontal",dir:w,...u}=t,[p,M]=c.useState(null),b=O(o,h=>M(h)),x=st(w),g=c.useRef(0),N=c.useRef(0),C=c.useRef(0),[T,v]=c.useState(!0),[m="",f]=fe({prop:a,onChange:h=>{const P=h!=="",U=d>0;P?(window.clearTimeout(C.current),U&&v(!1)):(window.clearTimeout(C.current),C.current=window.setTimeout(()=>v(!0),d)),n==null||n(h)},defaultProp:s}),y=c.useCallback(()=>{window.clearTimeout(N.current),N.current=window.setTimeout(()=>f(""),150)},[f]),k=c.useCallback(h=>{window.clearTimeout(N.current),f(h)},[f]),A=c.useCallback(h=>{m===h?window.clearTimeout(N.current):g.current=window.setTimeout(()=>{window.clearTimeout(N.current),f(h)},i)},[m,f,i]);return c.useEffect(()=>()=>{window.clearTimeout(g.current),window.clearTimeout(N.current),window.clearTimeout(C.current)},[]),e.jsx(xe,{scope:r,isRootMenu:!0,value:m,dir:x,orientation:l,rootNavigationMenu:p,onTriggerEnter:h=>{window.clearTimeout(g.current),T?A(h):k(h)},onTriggerLeave:()=>{window.clearTimeout(g.current),y()},onContentEnter:()=>window.clearTimeout(N.current),onContentLeave:y,onItemSelect:h=>{f(P=>P===h?"":h)},onItemDismiss:()=>f(""),children:e.jsx(I.nav,{"aria-label":"Main","data-orientation":l,dir:x,...u,ref:b})})});ge.displayName=F;var he="NavigationMenuSub",Dt=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,value:a,onValueChange:n,defaultValue:s,orientation:i="horizontal",...d}=t,l=E(he,r),[w="",u]=fe({prop:a,onChange:n,defaultProp:s});return e.jsx(xe,{scope:r,isRootMenu:!1,value:w,dir:l.dir,orientation:i,rootNavigationMenu:l.rootNavigationMenu,onTriggerEnter:p=>u(p),onItemSelect:p=>u(p),onItemDismiss:()=>u(""),children:e.jsx(I.div,{"data-orientation":i,...d,ref:o})})});Dt.displayName=he;var xe=t=>{const{scope:o,isRootMenu:r,rootNavigationMenu:a,dir:n,orientation:s,children:i,value:d,onItemSelect:l,onItemDismiss:w,onTriggerEnter:u,onTriggerLeave:p,onContentEnter:M,onContentLeave:b}=t,[x,g]=c.useState(null),[N,C]=c.useState(new Map),[T,v]=c.useState(null);return e.jsx(At,{scope:o,isRootMenu:r,rootNavigationMenu:a,value:d,previousValue:ut(d),baseId:me(),dir:n,orientation:s,viewport:x,onViewportChange:g,indicatorTrack:T,onIndicatorTrackChange:v,onTriggerEnter:S(u),onTriggerLeave:S(p),onContentEnter:S(M),onContentLeave:S(b),onItemSelect:S(l),onItemDismiss:S(w),onViewportContentChange:c.useCallback((m,f)=>{C(y=>(y.set(m,f),new Map(y)))},[]),onViewportContentRemove:c.useCallback(m=>{C(f=>f.has(m)?(f.delete(m),new Map(f)):f)},[]),children:e.jsx(J.Provider,{scope:o,children:e.jsx(Pt,{scope:o,items:N,children:i})})})},we="NavigationMenuList",Ne=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,...a}=t,n=E(we,r),s=e.jsx(I.ul,{"data-orientation":n.orientation,...a,ref:o});return e.jsx(I.div,{style:{position:"relative"},ref:n.onIndicatorTrackChange,children:e.jsx(J.Slot,{scope:r,children:n.isRootMenu?e.jsx(Re,{asChild:!0,children:s}):s})})});Ne.displayName=we;var ye="NavigationMenuItem",[Lt,Me]=Q(ye),be=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,value:a,...n}=t,s=me(),i=a||s||"LEGACY_REACT_AUTO_VALUE",d=c.useRef(null),l=c.useRef(null),w=c.useRef(null),u=c.useRef(()=>{}),p=c.useRef(!1),M=c.useCallback((x="start")=>{if(d.current){u.current();const g=Y(d.current);g.length&&ne(x==="start"?g:g.reverse())}},[]),b=c.useCallback(()=>{if(d.current){const x=Y(d.current);x.length&&(u.current=qt(x))}},[]);return e.jsx(Lt,{scope:r,value:i,triggerRef:l,contentRef:d,focusProxyRef:w,wasEscapeCloseRef:p,onEntryKeyDown:M,onFocusProxyEnter:M,onRootContentClose:b,onContentFocusOutside:b,children:e.jsx(I.li,{...n,ref:o})})});be.displayName=ye;var Z="NavigationMenuTrigger",Ft=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,disabled:a,...n}=t,s=E(Z,t.__scopeNavigationMenu),i=Me(Z,t.__scopeNavigationMenu),d=c.useRef(null),l=O(d,i.triggerRef,o),w=Ee(s.baseId,i.value),u=Ie(s.baseId,i.value),p=c.useRef(!1),M=c.useRef(!1),b=i.value===s.value;return e.jsxs(e.Fragment,{children:[e.jsx(J.ItemSlot,{scope:r,value:i.value,children:e.jsx(ke,{asChild:!0,children:e.jsx(I.button,{id:w,disabled:a,"data-disabled":a?"":void 0,"data-state":oe(b),"aria-expanded":b,"aria-controls":u,...n,ref:l,onPointerEnter:R(t.onPointerEnter,()=>{M.current=!1,i.wasEscapeCloseRef.current=!1}),onPointerMove:R(t.onPointerMove,z(()=>{a||M.current||i.wasEscapeCloseRef.current||p.current||(s.onTriggerEnter(i.value),p.current=!0)})),onPointerLeave:R(t.onPointerLeave,z(()=>{a||(s.onTriggerLeave(),p.current=!1)})),onClick:R(t.onClick,()=>{s.onItemSelect(i.value),M.current=b}),onKeyDown:R(t.onKeyDown,x=>{const N={horizontal:"ArrowDown",vertical:s.dir==="rtl"?"ArrowLeft":"ArrowRight"}[s.orientation];b&&x.key===N&&(i.onEntryKeyDown(),x.preventDefault())})})})}),b&&e.jsxs(e.Fragment,{children:[e.jsx(lt,{"aria-hidden":!0,tabIndex:0,ref:i.focusProxyRef,onFocus:x=>{const g=i.contentRef.current,N=x.relatedTarget,C=N===d.current,T=g==null?void 0:g.contains(N);(C||!T)&&i.onFocusProxyEnter(C?"start":"end")}}),s.viewport&&e.jsx("span",{"aria-owns":u})]})]})});Ft.displayName=Z;var Ot="NavigationMenuLink",se="navigationMenu.linkSelect",Vt=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,active:a,onSelect:n,...s}=t;return e.jsx(ke,{asChild:!0,children:e.jsx(I.a,{"data-active":a?"":void 0,"aria-current":a?"page":void 0,...s,ref:o,onClick:R(t.onClick,i=>{const d=i.target,l=new CustomEvent(se,{bubbles:!0,cancelable:!0});if(d.addEventListener(se,w=>n==null?void 0:n(w),{once:!0}),re(d,l),!l.defaultPrevented&&!i.metaKey){const w=new CustomEvent(K,{bubbles:!0,cancelable:!0});re(d,w)}},{checkForDefaultPrevented:!1})})})});Vt.displayName=Ot;var ee="NavigationMenuIndicator",Kt=c.forwardRef((t,o)=>{const{forceMount:r,...a}=t,n=E(ee,t.__scopeNavigationMenu),s=!!n.value;return n.indicatorTrack?at.createPortal(e.jsx($,{present:r||s,children:e.jsx(zt,{...a,ref:o})}),n.indicatorTrack):null});Kt.displayName=ee;var zt=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,...a}=t,n=E(ee,r),s=pe(r),[i,d]=c.useState(null),[l,w]=c.useState(null),u=n.orientation==="horizontal",p=!!n.value;c.useEffect(()=>{var g;const x=(g=s().find(N=>N.value===n.value))==null?void 0:g.ref.current;x&&d(x)},[s,n.value]);const M=()=>{i&&w({size:u?i.offsetWidth:i.offsetHeight,offset:u?i.offsetLeft:i.offsetTop})};return X(i,M),X(n.indicatorTrack,M),l?e.jsx(I.div,{"aria-hidden":!0,"data-state":p?"visible":"hidden","data-orientation":n.orientation,...a,ref:o,style:{position:"absolute",...u?{left:0,width:l.size+"px",transform:`translateX(${l.offset}px)`}:{top:0,height:l.size+"px",transform:`translateY(${l.offset}px)`},...a.style}}):null}),L="NavigationMenuContent",$t=c.forwardRef((t,o)=>{const{forceMount:r,...a}=t,n=E(L,t.__scopeNavigationMenu),s=Me(L,t.__scopeNavigationMenu),i=O(s.contentRef,o),d=s.value===n.value,l={value:s.value,triggerRef:s.triggerRef,focusProxyRef:s.focusProxyRef,wasEscapeCloseRef:s.wasEscapeCloseRef,onContentFocusOutside:s.onContentFocusOutside,onRootContentClose:s.onRootContentClose,...a};return n.viewport?e.jsx(Ut,{forceMount:r,...l,ref:i}):e.jsx($,{present:r||d,children:e.jsx(Ce,{"data-state":oe(d),...l,ref:i,onPointerEnter:R(t.onPointerEnter,n.onContentEnter),onPointerLeave:R(t.onPointerLeave,z(n.onContentLeave)),style:{pointerEvents:!d&&n.isRootMenu?"none":void 0,...l.style}})})});$t.displayName=L;var Ut=c.forwardRef((t,o)=>{const r=E(L,t.__scopeNavigationMenu),{onViewportContentChange:a,onViewportContentRemove:n}=r;return q(()=>{a(t.value,{ref:o,...t})},[t,o,a]),q(()=>()=>n(t.value),[t.value,n]),null}),K="navigationMenu.rootContentDismiss",Ce=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,value:a,triggerRef:n,focusProxyRef:s,wasEscapeCloseRef:i,onRootContentClose:d,onContentFocusOutside:l,...w}=t,u=E(L,r),p=c.useRef(null),M=O(p,o),b=Ee(u.baseId,a),x=Ie(u.baseId,a),g=pe(r),N=c.useRef(null),{onItemDismiss:C}=u;c.useEffect(()=>{const v=p.current;if(u.isRootMenu&&v){const m=()=>{var f;C(),d(),v.contains(document.activeElement)&&((f=n.current)==null||f.focus())};return v.addEventListener(K,m),()=>v.removeEventListener(K,m)}},[u.isRootMenu,t.value,n,C,d]);const T=c.useMemo(()=>{const m=g().map(P=>P.value);u.dir==="rtl"&&m.reverse();const f=m.indexOf(u.value),y=m.indexOf(u.previousValue),k=a===u.value,A=y===m.indexOf(a);if(!k&&!A)return N.current;const h=(()=>{if(f!==y){if(k&&y!==-1)return f>y?"from-end":"from-start";if(A&&f!==-1)return f>y?"to-start":"to-end"}return null})();return N.current=h,h},[u.previousValue,u.value,u.dir,g,a]);return e.jsx(Re,{asChild:!0,children:e.jsx(ct,{id:x,"aria-labelledby":b,"data-motion":T,"data-orientation":u.orientation,...w,ref:M,disableOutsidePointerEvents:!1,onDismiss:()=>{var m;const v=new Event(K,{bubbles:!0,cancelable:!0});(m=p.current)==null||m.dispatchEvent(v)},onFocusOutside:R(t.onFocusOutside,v=>{var f;l();const m=v.target;(f=u.rootNavigationMenu)!=null&&f.contains(m)&&v.preventDefault()}),onPointerDownOutside:R(t.onPointerDownOutside,v=>{var k;const m=v.target,f=g().some(A=>{var h;return(h=A.ref.current)==null?void 0:h.contains(m)}),y=u.isRootMenu&&((k=u.viewport)==null?void 0:k.contains(m));(f||y||!u.isRootMenu)&&v.preventDefault()}),onKeyDown:R(t.onKeyDown,v=>{var y;const m=v.altKey||v.ctrlKey||v.metaKey;if(v.key==="Tab"&&!m){const k=Y(v.currentTarget),A=document.activeElement,h=k.findIndex(Te=>Te===A),U=v.shiftKey?k.slice(0,h).reverse():k.slice(h+1,k.length);ne(U)?v.preventDefault():(y=s.current)==null||y.focus()}}),onEscapeKeyDown:R(t.onEscapeKeyDown,v=>{i.current=!0})})})}),te="NavigationMenuViewport",je=c.forwardRef((t,o)=>{const{forceMount:r,...a}=t,s=!!E(te,t.__scopeNavigationMenu).value;return e.jsx($,{present:r||s,children:e.jsx(Gt,{...a,ref:o})})});je.displayName=te;var Gt=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,children:a,...n}=t,s=E(te,r),i=O(o,s.onViewportChange),d=St(L,t.__scopeNavigationMenu),[l,w]=c.useState(null),[u,p]=c.useState(null),M=l?(l==null?void 0:l.width)+"px":void 0,b=l?(l==null?void 0:l.height)+"px":void 0,x=!!s.value,g=x?s.value:s.previousValue;return X(u,()=>{u&&w({width:u.offsetWidth,height:u.offsetHeight})}),e.jsx(I.div,{"data-state":oe(x),"data-orientation":s.orientation,...n,ref:i,style:{pointerEvents:!x&&s.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":M,"--radix-navigation-menu-viewport-height":b,...n.style},onPointerEnter:R(t.onPointerEnter,s.onContentEnter),onPointerLeave:R(t.onPointerLeave,z(s.onContentLeave)),children:Array.from(d.items).map(([C,{ref:T,forceMount:v,...m}])=>{const f=g===C;return e.jsx($,{present:v||f,children:e.jsx(Ce,{...m,ref:_e(T,y=>{f&&y&&p(y)})})},C)})})}),Ht="FocusGroup",Re=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,...a}=t,n=E(Ht,r);return e.jsx(W.Provider,{scope:r,children:e.jsx(W.Slot,{scope:r,children:e.jsx(I.div,{dir:n.dir,...a,ref:o})})})}),ie=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],Bt="FocusGroupItem",ke=c.forwardRef((t,o)=>{const{__scopeNavigationMenu:r,...a}=t,n=Tt(r),s=E(Bt,r);return e.jsx(W.ItemSlot,{scope:r,children:e.jsx(I.button,{...a,ref:o,onKeyDown:R(t.onKeyDown,i=>{if(["Home","End",...ie].includes(i.key)){let l=n().map(p=>p.ref.current);if([s.dir==="rtl"?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(i.key)&&l.reverse(),ie.includes(i.key)){const p=l.indexOf(i.currentTarget);l=l.slice(p+1)}setTimeout(()=>ne(l)),i.preventDefault()}})})})});function Y(t){const o=[],r=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{const n=a.tagName==="INPUT"&&a.type==="hidden";return a.disabled||a.hidden||n?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)o.push(r.currentNode);return o}function ne(t){const o=document.activeElement;return t.some(r=>r===o?!0:(r.focus(),document.activeElement!==o))}function qt(t){return t.forEach(o=>{o.dataset.tabindex=o.getAttribute("tabindex")||"",o.setAttribute("tabindex","-1")}),()=>{t.forEach(o=>{const r=o.dataset.tabindex;o.setAttribute("tabindex",r)})}}function X(t,o){const r=S(o);q(()=>{let a=0;if(t){const n=new ResizeObserver(()=>{cancelAnimationFrame(a),a=window.requestAnimationFrame(r)});return n.observe(t),()=>{window.cancelAnimationFrame(a),n.unobserve(t)}}},[t,r])}function oe(t){return t?"open":"closed"}function Ee(t,o){return`${t}-trigger-${o}`}function Ie(t,o){return`${t}-content-${o}`}function z(t){return o=>o.pointerType==="mouse"?t(o):void 0}var Wt=ge,Zt=Ne,Yt=be,Xt=je;function Jt({className:t,children:o,viewport:r=!0,...a}){return e.jsxs(Wt,{"data-slot":"navigation-menu","data-viewport":r,className:D("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",t),...a,children:[o,r&&e.jsx(nn,{})]})}function Qt({className:t,...o}){return e.jsx(Zt,{"data-slot":"navigation-menu-list",className:D("group flex flex-1 list-none items-center justify-center gap-1",t),...o})}function en({className:t,...o}){return e.jsx(Yt,{"data-slot":"navigation-menu-item",className:D("relative",t),...o})}const tn=Ae("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[active=true]:bg-accent/50 data-[state=open]:bg-accent/50 data-[active=true]:text-accent-foreground ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 transition-[color,box-shadow] focus-visible:ring-4 focus-visible:outline-1");function nn({className:t,...o}){return e.jsx("div",{className:D("absolute top-full left-0 isolate z-50 flex justify-center"),children:e.jsx(Xt,{"data-slot":"navigation-menu-viewport",className:D("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",t),...o})})}const ce=[{title:"Dashboard",href:"/dashboard",icon:Ke}],le=[{title:"Repository",href:"https://github.com/laravel/react-starter-kit",icon:ze},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#react",icon:$e}],on="text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100";function rn({breadcrumbs:t=[]}){const o=ue(),{auth:r}=o.props,a=Se();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-b border-sidebar-border/80",children:e.jsxs("div",{className:"mx-auto flex h-16 items-center px-4 md:max-w-7xl",children:[e.jsx("div",{className:"lg:hidden",children:e.jsxs(De,{children:[e.jsx(Le,{asChild:!0,children:e.jsx(G,{variant:"ghost",size:"icon",className:"mr-2 h-[34px] w-[34px]",children:e.jsx(Nt,{className:"h-5 w-5"})})}),e.jsxs(Fe,{side:"left",className:"flex h-full w-64 flex-col items-stretch justify-between bg-sidebar",children:[e.jsx(Oe,{className:"sr-only",children:"Navigation Menu"}),e.jsx(Ve,{className:"flex justify-start text-left",children:e.jsx(dt,{className:"h-6 w-6 fill-current text-black dark:text-white"})}),e.jsx("div",{className:"flex h-full flex-1 flex-col space-y-4 p-4",children:e.jsxs("div",{className:"flex h-full flex-col justify-between text-sm",children:[e.jsx("div",{className:"flex flex-col space-y-4",children:ce.map(n=>e.jsxs(H,{href:n.href,className:"flex items-center space-x-2 font-medium",children:[n.icon&&e.jsx(V,{iconNode:n.icon,className:"h-5 w-5"}),e.jsx("span",{children:n.title})]},n.title))}),e.jsx("div",{className:"flex flex-col space-y-4",children:le.map(n=>e.jsxs("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 font-medium",children:[n.icon&&e.jsx(V,{iconNode:n.icon,className:"h-5 w-5"}),e.jsx("span",{children:n.title})]},n.title))})]})})]})]})}),e.jsx(H,{href:"/dashboard",prefetch:!0,className:"flex items-center space-x-2",children:e.jsx(Ue,{})}),e.jsx("div",{className:"ml-6 hidden h-full items-center space-x-6 lg:flex",children:e.jsx(Jt,{className:"flex h-full items-stretch",children:e.jsx(Qt,{className:"flex h-full items-stretch space-x-2",children:ce.map((n,s)=>e.jsxs(en,{className:"relative flex h-full items-center",children:[e.jsxs(H,{href:n.href,className:D(tn(),o.url===n.href&&on,"h-9 cursor-pointer px-3"),children:[n.icon&&e.jsx(V,{iconNode:n.icon,className:"mr-2 h-4 w-4"}),n.title]}),o.url===n.href&&e.jsx("div",{className:"absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"})]},s))})})}),e.jsxs("div",{className:"ml-auto flex items-center space-x-2",children:[e.jsxs("div",{className:"relative flex items-center space-x-1",children:[e.jsx(G,{variant:"ghost",size:"icon",className:"group h-9 w-9 cursor-pointer",children:e.jsx(Ct,{className:"!size-5 opacity-80 group-hover:opacity-100"})}),e.jsx("div",{className:"hidden lg:flex",children:le.map(n=>e.jsx(Ge,{delayDuration:0,children:e.jsxs(He,{children:[e.jsx(Be,{children:e.jsxs("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",className:"group ml-1 inline-flex h-9 w-9 items-center justify-center rounded-md bg-transparent p-0 text-sm font-medium text-accent-foreground ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",children:[e.jsx("span",{className:"sr-only",children:n.title}),n.icon&&e.jsx(V,{iconNode:n.icon,className:"size-5 opacity-80 group-hover:opacity-100"})]})}),e.jsx(qe,{children:e.jsx("p",{children:n.title})})]})},n.title))})]}),e.jsxs(We,{children:[e.jsx(Ze,{asChild:!0,children:e.jsx(G,{variant:"ghost",className:"size-10 rounded-full p-1",children:e.jsxs(Ye,{className:"size-8 overflow-hidden rounded-full",children:[e.jsx(Xe,{src:r.user.avatar,alt:r.user.name}),e.jsx(Je,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:a(r.user.name)})]})})}),e.jsx(Qe,{className:"w-56",align:"end",children:e.jsx(et,{user:r.user})})]})]})]})}),t.length>1&&e.jsx("div",{className:"flex w-full border-b border-sidebar-border/70",children:e.jsx("div",{className:"mx-auto flex h-12 w-full items-center justify-start px-4 text-neutral-500 md:max-w-7xl",children:e.jsx(de,{breadcrumbs:t})})})]})}function gn({children:t,title:o,breadcrumbs:r}){const{user:a}=ue().props,n=()=>{const s=[{title:"Dashboard",url:"/dashboard",icon:mt,isActive:j().current("dashboard")},{title:"Field Monitoring",url:"/fields",icon:xt,isActive:j().current("fields.*")},{title:"Alerts",url:"/alerts",icon:ae,isActive:j().current("alerts.*")},{title:"Reports",url:"/reports",icon:ft,isActive:j().current("reports.*")}],i={government_admin:[{title:"Subsidy Claims",url:"/subsidy-claims",icon:B,isActive:j().current("subsidy-claims.*")},{title:"Organizations",url:"/organizations",icon:Rt,isActive:j().current("organizations.*")}],bank_analyst:[{title:"Loan Analysis",url:"/subsidy-claims",icon:B,isActive:j().current("subsidy-claims.*")}],farmer:[{title:"My Fields",url:"/fields?owner=me",icon:Et,isActive:j().current("fields.*")&&j().params.owner==="me"},{title:"My Claims",url:"/subsidy-claims?claimant=me",icon:B,isActive:j().current("subsidy-claims.*")&&j().params.claimant==="me"},{title:"AI Advisory",url:"/advisory",icon:Mt,isActive:j().current("advisory.*")}],ngo_coordinator:[{title:"Disaster Monitoring",url:"/alerts?type=disaster",icon:ae,isActive:j().current("alerts.*")&&j().params.type==="disaster"}]};return[...s,...i[a.role]||[],{title:"Settings",url:"/settings",icon:rt,isActive:j().current("settings.*")}]};return e.jsxs(tt,{children:[e.jsx(Pe,{title:o?`${o} - AgroVue`:"AgroVue"}),e.jsx(nt,{user:a,navigation:n()}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsx(rn,{user:a}),e.jsxs(ot,{children:[r&&e.jsx("div",{className:"mb-6",children:e.jsx(de,{items:r})}),t]})]})]})}function j(){return{current:t=>window.location.pathname.includes(t.replace(".*","").replace(".","/")),params:{}}}export{gn as A,B as D,mn as M,Ct as S,Et as W};
