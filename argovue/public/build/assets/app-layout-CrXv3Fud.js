import{j as e}from"./app-VriuOT-w.js";import{w as i,m as n,n as p,o as t,p as d}from"./app-content-D0eHL4FB.js";function o({breadcrumbs:r=[]}){return e.jsx("header",{className:"flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/50 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(i,{className:"-ml-1"}),e.jsx(n,{breadcrumbs:r})]})})}function l({children:r,breadcrumbs:a=[]}){return e.jsxs(p,{variant:"sidebar",children:[e.jsx(t,{}),e.jsxs(d,{variant:"sidebar",className:"overflow-x-hidden",children:[e.jsx(o,{breadcrumbs:a}),r]})]})}const m=({children:r,breadcrumbs:a,...s})=>e.jsx(l,{breadcrumbs:a,...s,children:r});export{m as A};
