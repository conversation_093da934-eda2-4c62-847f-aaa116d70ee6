import{r as l,j as e,m as T,L as $,$ as N}from"./app-BRSV0K6a.js";import{c as K,u as U,d as E,b as X,B as I}from"./button-DeV0q5_x.js";import{C as _,a as A,b as D,d as F,c as L}from"./card-De7NLU4G.js";import{I as O}from"./input-C84b3OWV.js";import{L as b}from"./label-CpTq5nI-.js";import{C as J,S as Q,a as W,b as Y,c as Z,d as ee}from"./select-Dn6ix02i.js";import{a as te,c as se,f as B,P as re,h as ae}from"./index-SRmR8U3Z.js";import{u as ne}from"./index-CwMx2vkM.js";import{P as M}from"./index-D2bTj4tq.js";import{S as oe,E as ce}from"./satellite-Px9uRWBE.js";/* empty css            *//**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],le=K("EyeOff",ie);var P="Checkbox",[de,Ie]=se(P),[ue,me]=de(P),V=l.forwardRef((t,a)=>{const{__scopeCheckbox:r,name:c,checked:x,defaultChecked:n,required:o,disabled:d,value:h="on",onCheckedChange:f,form:s,...j}=t,[u,v]=l.useState(null),y=U(a,i=>v(i)),k=l.useRef(!1),R=u?s||!!u.closest("form"):!0,[p=!1,C]=te({prop:x,defaultProp:n,onChange:f}),H=l.useRef(p);return l.useEffect(()=>{const i=u==null?void 0:u.form;if(i){const g=()=>C(H.current);return i.addEventListener("reset",g),()=>i.removeEventListener("reset",g)}},[u,C]),e.jsxs(ue,{scope:r,state:p,disabled:d,children:[e.jsx(M.button,{type:"button",role:"checkbox","aria-checked":m(p)?"mixed":p,"aria-required":o,"data-state":G(p),"data-disabled":d?"":void 0,disabled:d,value:h,...j,ref:y,onKeyDown:B(t.onKeyDown,i=>{i.key==="Enter"&&i.preventDefault()}),onClick:B(t.onClick,i=>{C(g=>m(g)?!0:!g),R&&(k.current=i.isPropagationStopped(),k.current||i.stopPropagation())})}),R&&e.jsx(xe,{control:u,bubbles:!k.current,name:c,value:h,checked:p,required:o,disabled:d,form:s,style:{transform:"translateX(-100%)"},defaultChecked:m(n)?!1:n})]})});V.displayName=P;var z="CheckboxIndicator",q=l.forwardRef((t,a)=>{const{__scopeCheckbox:r,forceMount:c,...x}=t,n=me(z,r);return e.jsx(re,{present:c||m(n.state)||n.state===!0,children:e.jsx(M.span,{"data-state":G(n.state),"data-disabled":n.disabled?"":void 0,...x,ref:a,style:{pointerEvents:"none",...t.style}})})});q.displayName=z;var xe=t=>{const{control:a,checked:r,bubbles:c=!0,defaultChecked:x,...n}=t,o=l.useRef(null),d=ne(r),h=ae(a);l.useEffect(()=>{const s=o.current,j=window.HTMLInputElement.prototype,v=Object.getOwnPropertyDescriptor(j,"checked").set;if(d!==r&&v){const y=new Event("click",{bubbles:c});s.indeterminate=m(r),v.call(s,m(r)?!1:r),s.dispatchEvent(y)}},[d,r,c]);const f=l.useRef(m(r)?!1:r);return e.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:x??f.current,...n,tabIndex:-1,ref:o,style:{...t.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function m(t){return t==="indeterminate"}function G(t){return m(t)?"indeterminate":t?"checked":"unchecked"}var he=V,pe=q;function fe({className:t,...a}){return e.jsx(he,{"data-slot":"checkbox",className:E("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:e.jsx(pe,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:e.jsx(J,{className:"size-3.5"})})})}const ge=X("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function w({className:t,variant:a,...r}){return e.jsx("div",{"data-slot":"alert",role:"alert",className:E(ge({variant:a}),t),...r})}function S({className:t,...a}){return e.jsx("div",{"data-slot":"alert-description",className:E("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}function _e(){const[t,a]=l.useState(!1),{data:r,setData:c,post:x,processing:n,errors:o,reset:d}=T({email:"",password:"",role:"",remember:!1}),h=s=>{s.preventDefault(),x(ve("login"),{onFinish:()=>d("password")})},f=[{value:"government_admin",label:"Government Admin"},{value:"ngo_coordinator",label:"NGO Coordinator"},{value:"farmer",label:"Farmer"},{value:"bank_analyst",label:"Bank Analyst"},{value:"corporate",label:"Corporate User"}];return e.jsxs(e.Fragment,{children:[e.jsx($,{title:"Login - AgroVue"}),e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-yellow-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs(N,{href:"/",className:"inline-flex items-center space-x-2 text-2xl font-bold text-gray-900",children:[e.jsx(oe,{className:"h-8 w-8 text-green-600"}),e.jsx("span",{children:"AgroVue"})]}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Satellite-Powered Agricultural Intelligence"})]}),e.jsxs(_,{children:[e.jsxs(A,{className:"space-y-1",children:[e.jsx(D,{className:"text-2xl text-center",children:"Sign in to your account"}),e.jsx(F,{className:"text-center",children:"Enter your credentials to access the AgroVue platform"})]}),e.jsx(L,{children:e.jsxs("form",{onSubmit:h,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{htmlFor:"email",children:"Email"}),e.jsx(O,{id:"email",type:"email",value:r.email,onChange:s=>c("email",s.target.value),placeholder:"Enter your email",required:!0,autoFocus:!0}),o.email&&e.jsx(w,{variant:"destructive",children:e.jsx(S,{children:o.email})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{htmlFor:"password",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(O,{id:"password",type:t?"text":"password",value:r.password,onChange:s=>c("password",s.target.value),placeholder:"Enter your password",required:!0}),e.jsx(I,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>a(!t),children:t?e.jsx(le,{className:"h-4 w-4"}):e.jsx(ce,{className:"h-4 w-4"})})]}),o.password&&e.jsx(w,{variant:"destructive",children:e.jsx(S,{children:o.password})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{htmlFor:"role",children:"Role"}),e.jsxs(Q,{value:r.role,onValueChange:s=>c("role",s),children:[e.jsx(W,{children:e.jsx(Y,{placeholder:"Select your role"})}),e.jsx(Z,{children:f.map(s=>e.jsx(ee,{value:s.value,children:s.label},s.value))})]}),o.role&&e.jsx(w,{variant:"destructive",children:e.jsx(S,{children:o.role})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(fe,{id:"remember",checked:r.remember,onCheckedChange:s=>c("remember",s)}),e.jsx(b,{htmlFor:"remember",className:"text-sm font-normal",children:"Remember me"})]}),e.jsx(I,{type:"submit",className:"w-full",disabled:n,children:n?"Signing in...":"Sign in"}),e.jsxs("div",{className:"text-center space-y-2",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx(N,{href:"/register",className:"text-green-600 hover:text-green-500 font-medium",children:"Sign up"})]}),e.jsx(N,{href:"/forgot-password",className:"text-sm text-gray-500 hover:text-gray-700",children:"Forgot your password?"})]})]})})]}),e.jsxs(_,{className:"mt-6",children:[e.jsxs(A,{children:[e.jsx(D,{className:"text-lg",children:"Demo Credentials"}),e.jsx(F,{children:"Use these credentials to test different user roles"})]}),e.jsx(L,{className:"space-y-2",children:e.jsxs("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[e.jsxs("div",{className:"p-2 bg-blue-50 rounded",children:[e.jsx("strong",{children:"Government Admin:"})," <EMAIL> / password"]}),e.jsxs("div",{className:"p-2 bg-green-50 rounded",children:[e.jsx("strong",{children:"NGO Coordinator:"})," <EMAIL> / password"]}),e.jsxs("div",{className:"p-2 bg-yellow-50 rounded",children:[e.jsx("strong",{children:"Farmer:"})," <EMAIL> / password"]}),e.jsxs("div",{className:"p-2 bg-purple-50 rounded",children:[e.jsx("strong",{children:"Bank Analyst:"})," <EMAIL> / password"]})]})})]})]})})]})}function ve(t){return`/${t}`}export{_e as default};
