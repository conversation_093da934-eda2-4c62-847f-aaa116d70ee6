import{r as w,m as j,j as s,L as v}from"./app-BRSV0K6a.js";import{I as n}from"./input-error-CUsZGa5I.js";import{A as _}from"./app-layout-B779mfJ0.js";import{S as y,H as N}from"./layout-DXx_D9nR.js";import{B as C}from"./button-DeV0q5_x.js";import{I as p}from"./input-C84b3OWV.js";import{L as d}from"./label-CpTq5nI-.js";import{z as S}from"./transition-DSF__ay3.js";/* empty css            */import"./app-content-DoSKBbyq.js";import"./index-SRmR8U3Z.js";import"./index-D2bTj4tq.js";import"./app-logo-icon-uNlWQ8Ti.js";const b=[{title:"Password settings",href:"/settings/password"}];function U(){const i=w.useRef(null),c=w.useRef(null),{data:e,setData:a,errors:o,put:f,reset:t,processing:x,recentlySuccessful:h}=j({current_password:"",password:"",password_confirmation:""}),g=r=>{r.preventDefault(),f(route("password.update"),{preserveScroll:!0,onSuccess:()=>t(),onError:l=>{var m,u;l.password&&(t("password","password_confirmation"),(m=i.current)==null||m.focus()),l.current_password&&(t("current_password"),(u=c.current)==null||u.focus())}})};return s.jsxs(_,{breadcrumbs:b,children:[s.jsx(v,{title:"Password settings"}),s.jsx(y,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx(N,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),s.jsxs("form",{onSubmit:g,className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(d,{htmlFor:"current_password",children:"Current password"}),s.jsx(p,{id:"current_password",ref:c,value:e.current_password,onChange:r=>a("current_password",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"current-password",placeholder:"Current password"}),s.jsx(n,{message:o.current_password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(d,{htmlFor:"password",children:"New password"}),s.jsx(p,{id:"password",ref:i,value:e.password,onChange:r=>a("password",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"New password"}),s.jsx(n,{message:o.password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(d,{htmlFor:"password_confirmation",children:"Confirm password"}),s.jsx(p,{id:"password_confirmation",value:e.password_confirmation,onChange:r=>a("password_confirmation",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"Confirm password"}),s.jsx(n,{message:o.password_confirmation})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(C,{disabled:x,children:"Save password"}),s.jsx(S,{show:h,enter:"transition ease-in-out",enterFrom:"opacity-0",leave:"transition ease-in-out",leaveTo:"opacity-0",children:s.jsx("p",{className:"text-sm text-neutral-600",children:"Saved"})})]})]})]})})]})}export{U as default};
