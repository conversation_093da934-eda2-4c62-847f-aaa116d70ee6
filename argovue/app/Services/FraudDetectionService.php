<?php

namespace App\Services;

use App\Models\SubsidyClaim;
use App\Models\Field;
use App\Models\SatelliteData;
use App\Models\Alert;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FraudDetectionService
{
    private const HIGH_RISK_THRESHOLD = 70;
    private const MEDIUM_RISK_THRESHOLD = 40;
    
    public function analyzeClaim(SubsidyClaim $claim): array
    {
        $riskFactors = [];
        $riskScore = 0;
        
        // Field verification checks
        $fieldRisk = $this->analyzeFieldData($claim->field);
        $riskScore += $fieldRisk['score'];
        $riskFactors = array_merge($riskFactors, $fieldRisk['factors']);
        
        // Satellite data verification
        $satelliteRisk = $this->analyzeSatelliteData($claim->field);
        $riskScore += $satelliteRisk['score'];
        $riskFactors = array_merge($riskFactors, $satelliteRisk['factors']);
        
        // Historical pattern analysis
        $historicalRisk = $this->analyzeHistoricalPatterns($claim);
        $riskScore += $historicalRisk['score'];
        $riskFactors = array_merge($riskFactors, $historicalRisk['factors']);
        
        // Application data consistency
        $consistencyRisk = $this->analyzeApplicationConsistency($claim);
        $riskScore += $consistencyRisk['score'];
        $riskFactors = array_merge($riskFactors, $consistencyRisk['factors']);
        
        // Geographic anomalies
        $geoRisk = $this->analyzeGeographicAnomalies($claim);
        $riskScore += $geoRisk['score'];
        $riskFactors = array_merge($riskFactors, $geoRisk['factors']);
        
        // Cap the risk score at 100
        $riskScore = min($riskScore, 100);
        
        // Update the claim with fraud analysis
        $claim->update([
            'fraud_risk_score' => $riskScore,
            'fraud_indicators' => $riskFactors,
        ]);
        
        // Generate alert if high risk
        if ($riskScore >= self::HIGH_RISK_THRESHOLD) {
            $this->generateFraudAlert($claim, $riskScore, $riskFactors);
        }
        
        return [
            'risk_score' => $riskScore,
            'risk_level' => $this->getRiskLevel($riskScore),
            'risk_factors' => $riskFactors,
            'recommendation' => $this->getRecommendation($riskScore),
        ];
    }
    
    public function analyzeFieldData(Field $field): array
    {
        $score = 0;
        $factors = [];
        
        // Check if field exists and is properly mapped
        if (!$field->is_mapped) {
            $score += 25;
            $factors[] = 'field_not_properly_mapped';
        }
        
        // Check field size consistency
        if ($field->area_hectares > 1000) {
            $score += 15;
            $factors[] = 'unusually_large_field_size';
        }
        
        if ($field->area_hectares < 0.1) {
            $score += 20;
            $factors[] = 'unusually_small_field_size';
        }
        
        // Check if field has recent activity
        if (!$field->last_satellite_update || 
            Carbon::parse($field->last_satellite_update)->diffInDays(now()) > 90) {
            $score += 15;
            $factors[] = 'no_recent_satellite_data';
        }
        
        // Check crop type consistency with region
        $regionCrops = $this->getTypicalCropsForRegion($field->state);
        if (!in_array($field->crop_type, $regionCrops)) {
            $score += 10;
            $factors[] = 'unusual_crop_for_region';
        }
        
        return ['score' => $score, 'factors' => $factors];
    }
    
    public function analyzeSatelliteData(Field $field): array
    {
        $score = 0;
        $factors = [];
        
        // Get recent satellite data
        $recentData = SatelliteData::where('field_id', $field->id)
            ->where('capture_date', '>=', now()->subMonths(6))
            ->orderBy('capture_date', 'desc')
            ->get();
        
        if ($recentData->isEmpty()) {
            $score += 30;
            $factors[] = 'no_satellite_data_available';
            return ['score' => $score, 'factors' => $factors];
        }
        
        $latestData = $recentData->first();
        
        // Check vegetation health consistency
        if ($latestData->vegetation_health === 'critical' || $latestData->vegetation_health === 'poor') {
            $score += 20;
            $factors[] = 'poor_vegetation_health';
        }
        
        // Check NDVI values
        if ($latestData->ndvi_average < 0.2) {
            $score += 25;
            $factors[] = 'very_low_vegetation_index';
        }
        
        // Check for consistent agricultural activity
        $ndviValues = $recentData->pluck('ndvi_average')->toArray();
        if (count($ndviValues) > 3) {
            $variance = $this->calculateVariance($ndviValues);
            if ($variance < 0.01) {
                $score += 15;
                $factors[] = 'suspiciously_consistent_ndvi';
            }
        }
        
        // Check for seasonal patterns
        if (!$this->hasSeasonalPattern($recentData)) {
            $score += 10;
            $factors[] = 'no_seasonal_vegetation_pattern';
        }
        
        return ['score' => $score, 'factors' => $factors];
    }
    
    public function analyzeHistoricalPatterns(SubsidyClaim $claim): array
    {
        $score = 0;
        $factors = [];
        
        // Check previous claims by the same claimant
        $previousClaims = SubsidyClaim::where('claimant_id', $claim->claimant_id)
            ->where('id', '!=', $claim->id)
            ->where('created_at', '>=', now()->subYears(3))
            ->get();
        
        // Multiple claims in short period
        $recentClaims = $previousClaims->where('created_at', '>=', now()->subMonths(6));
        if ($recentClaims->count() > 3) {
            $score += 20;
            $factors[] = 'multiple_recent_claims';
        }
        
        // Check for rejected claims
        $rejectedClaims = $previousClaims->where('status', 'rejected');
        if ($rejectedClaims->count() > 1) {
            $score += 15;
            $factors[] = 'history_of_rejected_claims';
        }
        
        // Check for flagged claims
        $flaggedClaims = $previousClaims->where('status', 'flagged');
        if ($flaggedClaims->count() > 0) {
            $score += 25;
            $factors[] = 'history_of_flagged_claims';
        }
        
        // Check amount patterns
        $amounts = $previousClaims->pluck('amount_requested')->toArray();
        if (count($amounts) > 2) {
            $avgAmount = array_sum($amounts) / count($amounts);
            if ($claim->amount_requested > $avgAmount * 2) {
                $score += 15;
                $factors[] = 'unusually_high_claim_amount';
            }
        }
        
        return ['score' => $score, 'factors' => $factors];
    }
    
    public function analyzeApplicationConsistency(SubsidyClaim $claim): array
    {
        $score = 0;
        $factors = [];
        
        $appData = $claim->application_data;
        $field = $claim->field;
        
        // Check crop area consistency
        if (isset($appData['crop_area']) && $field) {
            $areaDifference = abs($appData['crop_area'] - $field->area_hectares);
            $percentageDiff = ($areaDifference / $field->area_hectares) * 100;
            
            if ($percentageDiff > 20) {
                $score += 15;
                $factors[] = 'crop_area_field_size_mismatch';
            }
        }
        
        // Check expected yield reasonableness
        if (isset($appData['expected_yield']) && $field) {
            $yieldPerHectare = $appData['expected_yield'] / $field->area_hectares;
            $typicalYield = $this->getTypicalYieldForCrop($field->crop_type, $field->state);
            
            if ($yieldPerHectare > $typicalYield * 1.5) {
                $score += 20;
                $factors[] = 'unrealistic_yield_expectations';
            }
        }
        
        // Check farming experience vs claim sophistication
        if (isset($appData['farming_experience']) && $appData['farming_experience'] < 2) {
            if ($claim->amount_requested > 500000) { // Large claim for inexperienced farmer
                $score += 10;
                $factors[] = 'inexperienced_farmer_large_claim';
            }
        }
        
        // Check bank details format
        if (isset($appData['bank_details'])) {
            if (!$this->validateBankDetails($appData['bank_details'])) {
                $score += 15;
                $factors[] = 'invalid_bank_details_format';
            }
        }
        
        return ['score' => $score, 'factors' => $factors];
    }
    
    public function analyzeGeographicAnomalies(SubsidyClaim $claim): array
    {
        $score = 0;
        $factors = [];
        
        $field = $claim->field;
        if (!$field) {
            return ['score' => 0, 'factors' => []];
        }
        
        // Check for claims in disaster-affected areas
        $recentDisasters = Alert::where('type', 'disaster')
            ->where('status', 'active')
            ->where('created_at', '>=', now()->subMonths(3))
            ->get();
        
        foreach ($recentDisasters as $disaster) {
            if ($this->isFieldInDisasterArea($field, $disaster)) {
                // Claims in disaster areas might be legitimate but need extra scrutiny
                $score += 5;
                $factors[] = 'field_in_recent_disaster_area';
                break;
            }
        }
        
        // Check for multiple claims in same geographic area
        $nearbyFields = Field::where('state', $field->state)
            ->where('lga', $field->lga)
            ->where('id', '!=', $field->id)
            ->get();
        
        $nearbyClaims = SubsidyClaim::whereIn('field_id', $nearbyFields->pluck('id'))
            ->where('created_at', '>=', now()->subMonths(1))
            ->where('claimant_id', '!=', $claim->claimant_id)
            ->count();
        
        if ($nearbyClaims > 10) {
            $score += 10;
            $factors[] = 'high_claim_density_in_area';
        }
        
        return ['score' => $score, 'factors' => $factors];
    }
    
    private function getRiskLevel(int $score): string
    {
        if ($score >= self::HIGH_RISK_THRESHOLD) return 'high';
        if ($score >= self::MEDIUM_RISK_THRESHOLD) return 'medium';
        return 'low';
    }
    
    private function getRecommendation(int $score): string
    {
        if ($score >= self::HIGH_RISK_THRESHOLD) {
            return 'Requires manual review and field verification before approval';
        }
        if ($score >= self::MEDIUM_RISK_THRESHOLD) {
            return 'Requires additional documentation and verification';
        }
        return 'Low risk - can proceed with standard approval process';
    }
    
    private function generateFraudAlert(SubsidyClaim $claim, int $riskScore, array $factors): void
    {
        Alert::create([
            'alert_id' => 'ALT-' . str_pad(Alert::count() + 1, 8, '0', STR_PAD_LEFT),
            'field_id' => $claim->field_id,
            'type' => 'fraud',
            'severity' => 'high',
            'title' => 'High Fraud Risk Detected',
            'description' => "Subsidy claim {$claim->claim_id} has been flagged with high fraud risk (score: {$riskScore})",
            'status' => 'active',
            'source' => 'ai_model',
            'metadata' => [
                'claim_id' => $claim->id,
                'risk_score' => $riskScore,
                'risk_factors' => $factors,
                'confidence_score' => 0.9,
            ],
            'expires_at' => now()->addDays(30),
        ]);
    }
    
    private function getTypicalCropsForRegion(string $state): array
    {
        $cropsByRegion = [
            'Lagos' => ['rice', 'cassava', 'maize', 'yam'],
            'Kano' => ['millet', 'sorghum', 'groundnut', 'maize'],
            'Kaduna' => ['rice', 'maize', 'millet', 'sorghum'],
            'Rivers' => ['rice', 'cassava', 'yam', 'plantain'],
            'Ogun' => ['cassava', 'maize', 'rice', 'yam'],
            // Add more states as needed
        ];
        
        return $cropsByRegion[$state] ?? ['rice', 'maize', 'cassava']; // Default crops
    }
    
    private function getTypicalYieldForCrop(string $cropType, string $state): float
    {
        $yields = [
            'rice' => 3.5,
            'maize' => 2.8,
            'cassava' => 12.0,
            'millet' => 1.2,
            'sorghum' => 1.5,
            'yam' => 15.0,
            'beans' => 1.8,
            'groundnut' => 2.0,
        ];
        
        return $yields[$cropType] ?? 2.0; // Default yield per hectare
    }
    
    private function calculateVariance(array $values): float
    {
        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);
        
        return $variance;
    }
    
    private function hasSeasonalPattern(object $satelliteData): bool
    {
        // Simple check for seasonal variation in NDVI
        if ($satelliteData->count() < 4) return true; // Not enough data
        
        $ndviValues = $satelliteData->pluck('ndvi_average')->toArray();
        $variance = $this->calculateVariance($ndviValues);
        
        return $variance > 0.02; // Minimum variance for seasonal pattern
    }
    
    private function validateBankDetails(array $bankDetails): bool
    {
        return isset($bankDetails['account_number']) &&
               isset($bankDetails['bank_name']) &&
               isset($bankDetails['account_name']) &&
               strlen($bankDetails['account_number']) >= 10;
    }
    
    private function isFieldInDisasterArea(Field $field, Alert $disaster): bool
    {
        // Simple geographic check - in real implementation, use proper GIS
        $metadata = $disaster->metadata ?? [];
        if (!isset($metadata['affected_regions'])) return false;
        
        return in_array($field->state, $metadata['affected_regions']) ||
               in_array($field->lga, $metadata['affected_regions']);
    }
}
