<?php

namespace App\Services;

use App\Models\Field;
use App\Models\SatelliteData;
use App\Models\Alert;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SatelliteDataService
{
    private const SENTINEL_API_URL = 'https://scihub.copernicus.eu/dhus/search';
    private const LANDSAT_API_URL = 'https://m2m.cr.usgs.gov/api/api/json/stable/';
    
    public function processSatelliteData(Field $field, array $satelliteData): SatelliteData
    {
        // Calculate vegetation indices
        $ndviStats = $this->calculateNDVIStatistics($satelliteData['ndvi_data'] ?? []);
        $vegetationHealth = $this->assessVegetationHealth($ndviStats['average']);
        
        // Detect anomalies
        $anomalies = $this->detectAnomalies($field, $ndviStats);
        
        // Create satellite data record
        $data = SatelliteData::create([
            'field_id' => $field->id,
            'satellite_source' => $satelliteData['source'] ?? 'sentinel-2',
            'capture_date' => $satelliteData['capture_date'] ?? now(),
            'cloud_coverage' => $satelliteData['cloud_coverage'] ?? 0,
            'ndvi_average' => $ndviStats['average'],
            'ndvi_min' => $ndviStats['min'],
            'ndvi_max' => $ndviStats['max'],
            'ndvi_std' => $ndviStats['std'],
            'evi_average' => $satelliteData['evi_average'] ?? null,
            'savi_average' => $satelliteData['savi_average'] ?? null,
            'vegetation_health' => $vegetationHealth,
            'change_detection' => $this->calculateChangeDetection($field, $ndviStats['average']),
            'anomalies_detected' => $anomalies,
            'image_url' => $satelliteData['image_url'] ?? null,
            'metadata' => $satelliteData['metadata'] ?? [],
            'processed_at' => now(),
        ]);
        
        // Update field's last satellite update
        $field->update([
            'last_satellite_update' => now(),
            'health_status' => $vegetationHealth,
            'latest_ndvi' => $ndviStats['average'],
        ]);
        
        // Generate alerts if necessary
        $this->generateAlertsFromSatelliteData($field, $data);
        
        return $data;
    }
    
    public function fetchSentinelData(Field $field, Carbon $startDate, Carbon $endDate): array
    {
        try {
            $bbox = $this->calculateBoundingBox($field->coordinates);
            
            $response = Http::timeout(30)->get(self::SENTINEL_API_URL, [
                'q' => sprintf(
                    'platformname:Sentinel-2 AND footprint:"Intersects(POLYGON((%s)))" AND beginposition:[%s TO %s] AND cloudcoverpercentage:[0 TO 20]',
                    $this->formatPolygonForQuery($bbox),
                    $startDate->format('Y-m-d\TH:i:s.v\Z'),
                    $endDate->format('Y-m-d\TH:i:s.v\Z')
                ),
                'format' => 'json',
                'rows' => 10,
                'orderby' => 'beginposition desc'
            ]);
            
            if ($response->successful()) {
                return $this->parseSentinelResponse($response->json());
            }
            
            Log::warning('Failed to fetch Sentinel data', [
                'field_id' => $field->id,
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return [];
        } catch (\Exception $e) {
            Log::error('Error fetching Sentinel data', [
                'field_id' => $field->id,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }
    
    public function calculateNDVIStatistics(array $ndviData): array
    {
        if (empty($ndviData)) {
            return [
                'average' => 0,
                'min' => 0,
                'max' => 0,
                'std' => 0,
            ];
        }
        
        $count = count($ndviData);
        $sum = array_sum($ndviData);
        $average = $sum / $count;
        
        $variance = array_sum(array_map(function($x) use ($average) {
            return pow($x - $average, 2);
        }, $ndviData)) / $count;
        
        return [
            'average' => round($average, 4),
            'min' => round(min($ndviData), 4),
            'max' => round(max($ndviData), 4),
            'std' => round(sqrt($variance), 4),
        ];
    }
    
    public function assessVegetationHealth(float $ndvi): string
    {
        if ($ndvi >= 0.7) return 'excellent';
        if ($ndvi >= 0.5) return 'good';
        if ($ndvi >= 0.3) return 'fair';
        if ($ndvi >= 0.1) return 'poor';
        return 'critical';
    }
    
    public function detectAnomalies(Field $field, array $ndviStats): array
    {
        $anomalies = [];
        
        // Get historical data for comparison
        $historicalData = SatelliteData::where('field_id', $field->id)
            ->where('capture_date', '>=', now()->subMonths(12))
            ->orderBy('capture_date', 'desc')
            ->limit(10)
            ->get();
        
        if ($historicalData->count() > 3) {
            $historicalAverage = $historicalData->avg('ndvi_average');
            $threshold = $historicalAverage * 0.2; // 20% deviation threshold
            
            if (abs($ndviStats['average'] - $historicalAverage) > $threshold) {
                $anomalies[] = $ndviStats['average'] < $historicalAverage ? 'vegetation_decline' : 'vegetation_surge';
            }
        }
        
        // Check for extreme values
        if ($ndviStats['average'] < 0.1) {
            $anomalies[] = 'severe_vegetation_loss';
        }
        
        if ($ndviStats['std'] > 0.3) {
            $anomalies[] = 'high_vegetation_variability';
        }
        
        return $anomalies;
    }
    
    public function calculateChangeDetection(Field $field, float $currentNdvi): ?array
    {
        $previousData = SatelliteData::where('field_id', $field->id)
            ->where('capture_date', '<', now())
            ->orderBy('capture_date', 'desc')
            ->first();
        
        if (!$previousData) {
            return null;
        }
        
        $changePercentage = (($currentNdvi - $previousData->ndvi_average) / $previousData->ndvi_average) * 100;
        
        $changeType = 'stable';
        if (abs($changePercentage) > 5) {
            $changeType = $changePercentage > 0 ? 'improvement' : 'decline';
        }
        
        return [
            'previous_ndvi' => $previousData->ndvi_average,
            'change_percentage' => round($changePercentage, 2),
            'change_type' => $changeType,
        ];
    }
    
    public function generateAlertsFromSatelliteData(Field $field, SatelliteData $data): void
    {
        // Generate alerts based on vegetation health
        if ($data->vegetation_health === 'critical') {
            Alert::create([
                'alert_id' => 'ALT-' . str_pad(Alert::count() + 1, 8, '0', STR_PAD_LEFT),
                'field_id' => $field->id,
                'type' => 'vegetation',
                'severity' => 'critical',
                'title' => 'Critical Vegetation Health Detected',
                'description' => "Field {$field->name} shows critical vegetation health with NDVI of {$data->ndvi_average}",
                'status' => 'active',
                'source' => 'satellite',
                'metadata' => [
                    'ndvi_value' => $data->ndvi_average,
                    'vegetation_health' => $data->vegetation_health,
                    'confidence_score' => 0.95,
                ],
                'expires_at' => now()->addDays(7),
            ]);
        }
        
        // Generate alerts for anomalies
        if (!empty($data->anomalies_detected)) {
            foreach ($data->anomalies_detected as $anomaly) {
                $severity = $anomaly === 'severe_vegetation_loss' ? 'critical' : 'medium';
                
                Alert::create([
                    'alert_id' => 'ALT-' . str_pad(Alert::count() + 1, 8, '0', STR_PAD_LEFT),
                    'field_id' => $field->id,
                    'type' => 'anomaly',
                    'severity' => $severity,
                    'title' => 'Vegetation Anomaly Detected',
                    'description' => "Anomaly detected in field {$field->name}: {$anomaly}",
                    'status' => 'active',
                    'source' => 'satellite',
                    'metadata' => [
                        'anomaly_type' => $anomaly,
                        'ndvi_value' => $data->ndvi_average,
                        'confidence_score' => 0.85,
                    ],
                    'expires_at' => now()->addDays(5),
                ]);
            }
        }
    }
    
    private function calculateBoundingBox(array $coordinates): array
    {
        $polygon = $coordinates['coordinates'][0];
        $lats = array_column($polygon, 1);
        $lngs = array_column($polygon, 0);
        
        return [
            'min_lat' => min($lats),
            'max_lat' => max($lats),
            'min_lng' => min($lngs),
            'max_lng' => max($lngs),
        ];
    }
    
    private function formatPolygonForQuery(array $bbox): string
    {
        return sprintf(
            '%f %f,%f %f,%f %f,%f %f,%f %f',
            $bbox['min_lng'], $bbox['min_lat'],
            $bbox['max_lng'], $bbox['min_lat'],
            $bbox['max_lng'], $bbox['max_lat'],
            $bbox['min_lng'], $bbox['max_lat'],
            $bbox['min_lng'], $bbox['min_lat']
        );
    }
    
    private function parseSentinelResponse(array $response): array
    {
        // Parse Sentinel API response and extract relevant data
        $products = $response['feed']['entry'] ?? [];
        $results = [];
        
        foreach ($products as $product) {
            $results[] = [
                'id' => $product['id'],
                'title' => $product['title'],
                'date' => $product['date'][0]['content'] ?? null,
                'cloud_coverage' => $this->extractCloudCoverage($product),
                'download_url' => $this->extractDownloadUrl($product),
            ];
        }
        
        return $results;
    }
    
    private function extractCloudCoverage(array $product): float
    {
        $str = $product['str'] ?? [];
        foreach ($str as $item) {
            if ($item['name'] === 'cloudcoverpercentage') {
                return (float) $item['content'];
            }
        }
        return 0.0;
    }
    
    private function extractDownloadUrl(array $product): ?string
    {
        $links = $product['link'] ?? [];
        foreach ($links as $link) {
            if (isset($link['href']) && strpos($link['href'], 'download') !== false) {
                return $link['href'];
            }
        }
        return null;
    }
}
