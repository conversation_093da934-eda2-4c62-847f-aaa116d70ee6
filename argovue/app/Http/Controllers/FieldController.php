<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\SatelliteData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class FieldController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Field::with(['owner', 'organization', 'satelliteData' => function($q) {
            $q->latest('capture_date')->limit(1);
        }]);

        // Filter by owner if farmer or if requested
        if ($user->role === 'farmer') {
            $query->where('owner_id', $user->id);
        } elseif ($request->get('owner') === 'me') {
            $query->where('owner_id', $user->id);
        }

        // Filter by organization if applicable
        if ($user->organization_id && $user->role !== 'government_admin') {
            $query->where('organization_id', $user->organization_id);
        }

        // Search filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('field_id', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%")
                  ->orWhere('lga', 'like', "%{$search}%");
            });
        }

        if ($request->filled('crop_type')) {
            $query->where('crop_type', $request->get('crop_type'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        $fields = $query->paginate(20);

        return Inertia::render('Fields/Index', [
            'fields' => $fields,
            'filters' => $request->only(['search', 'crop_type', 'status', 'owner']),
        ]);
    }

    public function create()
    {
        return Inertia::render('Fields/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'area_hectares' => 'required|numeric|min:0.1|max:10000',
            'crop_type' => 'required|in:rice,maize,cassava,millet,sorghum,yam,beans,groundnut,other',
            'crop_variety' => 'nullable|string|max:255',
            'coordinates' => 'required|array',
            'center_latitude' => 'required|numeric|between:-90,90',
            'center_longitude' => 'required|numeric|between:-180,180',
            'state' => 'required|string|max:255',
            'lga' => 'required|string|max:255',
            'ward' => 'nullable|string|max:255',
            'planting_date' => 'nullable|date',
            'expected_harvest_date' => 'nullable|date|after:planting_date',
            'expected_yield_tons' => 'nullable|numeric|min:0',
            'soil_data' => 'nullable|array',
            'notes' => 'nullable|string|max:1000',
        ]);

        $validated['field_id'] = 'FLD-' . str_pad(Field::count() + 1, 6, '0', STR_PAD_LEFT);
        $validated['owner_id'] = Auth::id();
        $validated['organization_id'] = Auth::user()->organization_id;
        $validated['is_mapped'] = true;

        $field = Field::create($validated);

        return redirect()->route('fields.show', $field)
            ->with('success', 'Field created successfully.');
    }

    public function show(Field $field)
    {
        $this->authorize('view', $field);

        $field->load([
            'owner',
            'organization',
            'satelliteData' => function($q) {
                $q->orderBy('capture_date', 'desc')->limit(10);
            },
            'subsidyClaims' => function($q) {
                $q->latest()->limit(5);
            },
            'alerts' => function($q) {
                $q->active()->latest()->limit(5);
            }
        ]);

        return Inertia::render('Fields/Show', [
            'field' => $field,
        ]);
    }

    public function edit(Field $field)
    {
        $this->authorize('update', $field);

        return Inertia::render('Fields/Edit', [
            'field' => $field,
        ]);
    }

    public function update(Request $request, Field $field)
    {
        $this->authorize('update', $field);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'area_hectares' => 'required|numeric|min:0.1|max:10000',
            'crop_type' => 'required|in:rice,maize,cassava,millet,sorghum,yam,beans,groundnut,other',
            'crop_variety' => 'nullable|string|max:255',
            'coordinates' => 'required|array',
            'center_latitude' => 'required|numeric|between:-90,90',
            'center_longitude' => 'required|numeric|between:-180,180',
            'state' => 'required|string|max:255',
            'lga' => 'required|string|max:255',
            'ward' => 'nullable|string|max:255',
            'planting_date' => 'nullable|date',
            'expected_harvest_date' => 'nullable|date|after:planting_date',
            'expected_yield_tons' => 'nullable|numeric|min:0',
            'soil_data' => 'nullable|array',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive,disputed,verified',
        ]);

        $field->update($validated);

        return redirect()->route('fields.show', $field)
            ->with('success', 'Field updated successfully.');
    }

    public function destroy(Field $field)
    {
        $this->authorize('delete', $field);

        $field->delete();

        return redirect()->route('fields.index')
            ->with('success', 'Field deleted successfully.');
    }

    public function satelliteData(Field $field)
    {
        $this->authorize('view', $field);

        $satelliteData = $field->satelliteData()
            ->orderBy('capture_date', 'desc')
            ->paginate(50);

        return Inertia::render('Fields/SatelliteData', [
            'field' => $field,
            'satelliteData' => $satelliteData,
        ]);
    }
}
