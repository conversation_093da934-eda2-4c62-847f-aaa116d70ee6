<?php

namespace App\Http\Controllers;

use App\Models\Alert;
use App\Models\Field;
use App\Models\Report;
use App\Models\SubsidyClaim;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $dashboardData = $this->getDashboardDataByRole($user);

        return Inertia::render('Dashboard/Index', [
            'user' => $user->load('organization'),
            'dashboardData' => $dashboardData,
        ]);
    }

    private function getDashboardDataByRole(User $user)
    {
        $baseData = [
            'totalFields' => Field::count(),
            'activeAlerts' => Alert::active()->count(),
            'recentReports' => Report::completed()->latest()->take(5)->get(),
        ];

        switch ($user->role) {
            case 'government_admin':
                return array_merge($baseData, [
                    'pendingClaims' => SubsidyClaim::where('status', 'pending')->count(),
                    'flaggedClaims' => SubsidyClaim::where('status', 'flagged')->count(),
                    'totalSubsidyAmount' => SubsidyClaim::where('status', 'approved')->sum('approved_amount'),
                    'recentClaims' => SubsidyClaim::with(['field', 'claimant'])
                        ->latest()
                        ->take(10)
                        ->get(),
                    'criticalAlerts' => Alert::active()->where('severity', 'critical')->get(),
                ]);

            case 'ngo_coordinator':
                return array_merge($baseData, [
                    'affectedFields' => Field::whereHas('alerts', function($q) {
                        $q->active()->whereIn('type', ['drought', 'flood', 'disaster']);
                    })->count(),
                    'disasterAlerts' => Alert::active()
                        ->whereIn('type', ['drought', 'flood', 'weather'])
                        ->latest()
                        ->get(),
                    'organizationFields' => Field::where('organization_id', $user->organization_id)->count(),
                ]);

            case 'farmer':
                return array_merge($baseData, [
                    'myFields' => Field::where('owner_id', $user->id)->count(),
                    'myAlerts' => Alert::whereHas('field', function($q) use ($user) {
                        $q->where('owner_id', $user->id);
                    })->active()->get(),
                    'myClaims' => SubsidyClaim::where('claimant_id', $user->id)
                        ->latest()
                        ->take(5)
                        ->get(),
                    'fieldHealth' => Field::where('owner_id', $user->id)
                        ->with('satelliteData')
                        ->get()
                        ->map(function($field) {
                            return [
                                'id' => $field->id,
                                'name' => $field->name,
                                'health' => $field->health_status,
                                'latest_ndvi' => $field->latest_ndvi,
                            ];
                        }),
                ]);

            case 'bank_analyst':
                return array_merge($baseData, [
                    'loanApplications' => SubsidyClaim::whereIn('status', ['pending', 'under_review'])->count(),
                    'highRiskClaims' => SubsidyClaim::where('fraud_risk_score', '>=', 70)->count(),
                    'totalLoanValue' => SubsidyClaim::whereIn('status', ['approved', 'paid'])->sum('approved_amount'),
                    'riskAnalysis' => SubsidyClaim::selectRaw('
                        CASE
                            WHEN fraud_risk_score >= 80 THEN "high"
                            WHEN fraud_risk_score >= 60 THEN "medium"
                            WHEN fraud_risk_score >= 40 THEN "low"
                            ELSE "very_low"
                        END as risk_level,
                        COUNT(*) as count
                    ')
                    ->whereNotNull('fraud_risk_score')
                    ->groupBy('risk_level')
                    ->get(),
                ]);

            default:
                return $baseData;
        }
    }
}
