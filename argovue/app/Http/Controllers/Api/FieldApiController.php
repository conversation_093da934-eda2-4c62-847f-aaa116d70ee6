<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Models\SatelliteData;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class FieldApiController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Field::with(['owner', 'organization', 'satelliteData' => function($q) {
            $q->latest('capture_date')->limit(1);
        }]);

        // Apply filters
        if ($request->filled('state')) {
            $query->where('state', $request->get('state'));
        }

        if ($request->filled('crop_type')) {
            $query->where('crop_type', $request->get('crop_type'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('organization_id')) {
            $query->where('organization_id', $request->get('organization_id'));
        }

        // Pagination
        $perPage = min($request->get('per_page', 20), 100);
        $fields = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $fields,
        ]);
    }

    public function show(Field $field): JsonResponse
    {
        $field->load([
            'owner',
            'organization',
            'satelliteData' => function($q) {
                $q->orderBy('capture_date', 'desc')->limit(10);
            },
            'subsidyClaims' => function($q) {
                $q->latest()->limit(5);
            },
            'alerts' => function($q) {
                $q->active()->latest()->limit(5);
            }
        ]);

        return response()->json([
            'success' => true,
            'data' => $field,
        ]);
    }

    public function updateStatus(Request $request, Field $field): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:active,inactive,disputed,verified',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $field->update([
            'status' => $request->get('status'),
            'notes' => $request->get('notes', $field->notes),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field status updated successfully',
            'data' => $field,
        ]);
    }

    public function mobileDashboard(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $data = [
            'user' => $user->load('organization'),
            'summary' => $this->getMobileSummary($user),
            'recent_fields' => $this->getRecentFields($user),
            'active_alerts' => $this->getActiveAlerts($user),
            'weather_info' => $this->getWeatherInfo($user),
        ];

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    public function myFields(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $query = Field::where('owner_id', $user->id)
            ->with(['satelliteData' => function($q) {
                $q->latest('capture_date')->limit(1);
            }]);

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        $fields = $query->get();

        return response()->json([
            'success' => true,
            'data' => $fields,
        ]);
    }

    public function uploadPhoto(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'field_id' => 'required|exists:fields,id',
            'photo' => 'required|image|mimes:jpeg,png,jpg|max:5120', // 5MB max
            'description' => 'nullable|string|max:500',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $field = Field::findOrFail($request->get('field_id'));
        
        // Check if user owns the field
        if ($field->owner_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to field',
            ], 403);
        }

        // Store the photo
        $photo = $request->file('photo');
        $filename = 'field_photos/' . $field->id . '/' . time() . '_' . $photo->getClientOriginalName();
        $path = Storage::disk('public')->put($filename, file_get_contents($photo));

        // Create a field photo record (you might want to create a FieldPhoto model)
        $photoData = [
            'field_id' => $field->id,
            'user_id' => Auth::id(),
            'filename' => $filename,
            'description' => $request->get('description'),
            'latitude' => $request->get('latitude'),
            'longitude' => $request->get('longitude'),
            'taken_at' => now(),
        ];

        return response()->json([
            'success' => true,
            'message' => 'Photo uploaded successfully',
            'data' => [
                'url' => Storage::disk('public')->url($filename),
                'metadata' => $photoData,
            ],
        ]);
    }

    private function getMobileSummary($user): array
    {
        $summary = [
            'total_fields' => 0,
            'active_alerts' => 0,
            'pending_claims' => 0,
            'health_status' => 'good',
        ];

        if ($user->role === 'farmer') {
            $fields = Field::where('owner_id', $user->id)->get();
            $summary['total_fields'] = $fields->count();
            $summary['active_alerts'] = $fields->sum(function($field) {
                return $field->alerts()->active()->count();
            });
            
            // Calculate overall health status
            $healthScores = $fields->pluck('health_status')->filter();
            if ($healthScores->contains('critical')) {
                $summary['health_status'] = 'critical';
            } elseif ($healthScores->contains('poor')) {
                $summary['health_status'] = 'poor';
            } elseif ($healthScores->contains('fair')) {
                $summary['health_status'] = 'fair';
            }
        }

        return $summary;
    }

    private function getRecentFields($user): array
    {
        if ($user->role !== 'farmer') {
            return [];
        }

        return Field::where('owner_id', $user->id)
            ->with(['satelliteData' => function($q) {
                $q->latest('capture_date')->limit(1);
            }])
            ->orderBy('last_satellite_update', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    private function getActiveAlerts($user): array
    {
        if ($user->role !== 'farmer') {
            return [];
        }

        return Field::where('owner_id', $user->id)
            ->with(['alerts' => function($q) {
                $q->active()->latest()->limit(3);
            }])
            ->get()
            ->pluck('alerts')
            ->flatten()
            ->toArray();
    }

    private function getWeatherInfo($user): array
    {
        // Mock weather data - in real implementation, integrate with weather API
        return [
            'temperature' => 28,
            'humidity' => 65,
            'rainfall_today' => 0,
            'rainfall_week' => 15.5,
            'forecast' => [
                ['day' => 'Today', 'condition' => 'Sunny', 'temp_high' => 32, 'temp_low' => 24],
                ['day' => 'Tomorrow', 'condition' => 'Partly Cloudy', 'temp_high' => 30, 'temp_low' => 23],
                ['day' => 'Day 3', 'condition' => 'Rain', 'temp_high' => 27, 'temp_low' => 21],
            ],
        ];
    }
}
