<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Models\SatelliteData;
use App\Services\SatelliteDataService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SatelliteDataApiController extends Controller
{
    protected $satelliteDataService;

    public function __construct(SatelliteDataService $satelliteDataService)
    {
        $this->satelliteDataService = $satelliteDataService;
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'field_id' => 'required|exists:fields,id',
            'satellite_source' => 'required|in:sentinel-2,landsat-8,landsat-9,modis,other',
            'capture_date' => 'required|date',
            'cloud_coverage' => 'required|numeric|between:0,100',
            'ndvi_data' => 'required|array',
            'ndvi_data.*' => 'numeric|between:-1,1',
            'evi_average' => 'nullable|numeric|between:-1,1',
            'savi_average' => 'nullable|numeric|between:-1,1',
            'image_url' => 'nullable|url',
            'metadata' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $field = Field::findOrFail($request->get('field_id'));
            
            $satelliteData = $this->satelliteDataService->processSatelliteData($field, $request->all());

            Log::info('Satellite data processed successfully', [
                'field_id' => $field->id,
                'satellite_data_id' => $satelliteData->id,
                'source' => $request->get('satellite_source'),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Satellite data processed successfully',
                'data' => $satelliteData,
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error processing satellite data', [
                'field_id' => $request->get('field_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error processing satellite data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getByField(Field $field, Request $request): JsonResponse
    {
        $query = SatelliteData::where('field_id', $field->id);

        // Apply date filters
        if ($request->filled('start_date')) {
            $query->where('capture_date', '>=', $request->get('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('capture_date', '<=', $request->get('end_date'));
        }

        // Apply source filter
        if ($request->filled('satellite_source')) {
            $query->where('satellite_source', $request->get('satellite_source'));
        }

        // Apply cloud coverage filter
        if ($request->filled('max_cloud_coverage')) {
            $query->where('cloud_coverage', '<=', $request->get('max_cloud_coverage'));
        }

        // Order by capture date
        $query->orderBy('capture_date', 'desc');

        // Pagination
        $perPage = min($request->get('per_page', 50), 200);
        $satelliteData = $query->paginate($perPage);

        // Add statistics
        $statistics = $this->calculateStatistics($field, $request);

        return response()->json([
            'success' => true,
            'data' => $satelliteData,
            'statistics' => $statistics,
        ]);
    }

    public function bulkStore(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'satellite_data' => 'required|array|min:1|max:100',
            'satellite_data.*.field_id' => 'required|exists:fields,id',
            'satellite_data.*.satellite_source' => 'required|in:sentinel-2,landsat-8,landsat-9,modis,other',
            'satellite_data.*.capture_date' => 'required|date',
            'satellite_data.*.cloud_coverage' => 'required|numeric|between:0,100',
            'satellite_data.*.ndvi_data' => 'required|array',
            'satellite_data.*.ndvi_data.*' => 'numeric|between:-1,1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $results = [];
        $errors = [];

        foreach ($request->get('satellite_data') as $index => $data) {
            try {
                $field = Field::findOrFail($data['field_id']);
                $satelliteData = $this->satelliteDataService->processSatelliteData($field, $data);
                
                $results[] = [
                    'index' => $index,
                    'field_id' => $field->id,
                    'satellite_data_id' => $satelliteData->id,
                    'status' => 'success',
                ];

            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index,
                    'field_id' => $data['field_id'] ?? null,
                    'error' => $e->getMessage(),
                ];

                Log::error('Error in bulk satellite data processing', [
                    'index' => $index,
                    'field_id' => $data['field_id'] ?? null,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $successCount = count($results);
        $errorCount = count($errors);

        return response()->json([
            'success' => $errorCount === 0,
            'message' => "Processed {$successCount} records successfully, {$errorCount} errors",
            'data' => [
                'successful' => $results,
                'errors' => $errors,
                'summary' => [
                    'total' => count($request->get('satellite_data')),
                    'successful' => $successCount,
                    'failed' => $errorCount,
                ],
            ],
        ], $errorCount > 0 ? 207 : 201); // 207 Multi-Status for partial success
    }

    public function getLatestByRegion(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'state' => 'nullable|string',
            'lga' => 'nullable|string',
            'crop_type' => 'nullable|string',
            'days_back' => 'nullable|integer|min:1|max:365',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $daysBack = $request->get('days_back', 30);
        $cutoffDate = now()->subDays($daysBack);

        $query = SatelliteData::with(['field' => function($q) use ($request) {
            if ($request->filled('state')) {
                $q->where('state', $request->get('state'));
            }
            if ($request->filled('lga')) {
                $q->where('lga', $request->get('lga'));
            }
            if ($request->filled('crop_type')) {
                $q->where('crop_type', $request->get('crop_type'));
            }
        }])
        ->where('capture_date', '>=', $cutoffDate)
        ->whereHas('field', function($q) use ($request) {
            if ($request->filled('state')) {
                $q->where('state', $request->get('state'));
            }
            if ($request->filled('lga')) {
                $q->where('lga', $request->get('lga'));
            }
            if ($request->filled('crop_type')) {
                $q->where('crop_type', $request->get('crop_type'));
            }
        });

        // Get latest data per field
        $latestData = $query->orderBy('capture_date', 'desc')
            ->get()
            ->groupBy('field_id')
            ->map(function($group) {
                return $group->first();
            })
            ->values();

        // Calculate regional statistics
        $regionalStats = [
            'total_fields' => $latestData->count(),
            'average_ndvi' => $latestData->avg('ndvi_average'),
            'health_distribution' => $latestData->groupBy('vegetation_health')->map->count(),
            'coverage_by_source' => $latestData->groupBy('satellite_source')->map->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $latestData,
            'regional_statistics' => $regionalStats,
            'filters_applied' => $request->only(['state', 'lga', 'crop_type', 'days_back']),
        ]);
    }

    private function calculateStatistics(Field $field, Request $request): array
    {
        $query = SatelliteData::where('field_id', $field->id);

        if ($request->filled('start_date')) {
            $query->where('capture_date', '>=', $request->get('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('capture_date', '<=', $request->get('end_date'));
        }

        $data = $query->get();

        if ($data->isEmpty()) {
            return [];
        }

        return [
            'total_observations' => $data->count(),
            'date_range' => [
                'earliest' => $data->min('capture_date'),
                'latest' => $data->max('capture_date'),
            ],
            'ndvi_statistics' => [
                'average' => round($data->avg('ndvi_average'), 4),
                'minimum' => round($data->min('ndvi_average'), 4),
                'maximum' => round($data->max('ndvi_average'), 4),
                'trend' => $this->calculateTrend($data->pluck('ndvi_average')->toArray()),
            ],
            'health_distribution' => $data->groupBy('vegetation_health')->map->count(),
            'source_distribution' => $data->groupBy('satellite_source')->map->count(),
            'average_cloud_coverage' => round($data->avg('cloud_coverage'), 2),
        ];
    }

    private function calculateTrend(array $values): string
    {
        if (count($values) < 2) return 'insufficient_data';

        $firstHalf = array_slice($values, 0, ceil(count($values) / 2));
        $secondHalf = array_slice($values, floor(count($values) / 2));

        $firstAvg = array_sum($firstHalf) / count($firstHalf);
        $secondAvg = array_sum($secondHalf) / count($secondHalf);

        $change = (($secondAvg - $firstAvg) / $firstAvg) * 100;

        if (abs($change) < 5) return 'stable';
        return $change > 0 ? 'improving' : 'declining';
    }
}
