<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SatelliteData extends Model
{
    protected $fillable = [
        'field_id',
        'capture_date',
        'satellite_source',
        'ndvi_value',
        'evi_value',
        'savi_value',
        'cloud_coverage',
        'rainfall_mm',
        'temperature_celsius',
        'soil_moisture',
        'image_url',
        'thumbnail_url',
        'analysis_data',
        'quality',
        'anomaly_detected',
        'anomaly_description',
    ];

    protected $casts = [
        'capture_date' => 'date',
        'analysis_data' => 'array',
        'anomaly_detected' => 'boolean',
    ];

    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function getVegetationHealthAttribute()
    {
        if (!$this->ndvi_value) return 'unknown';

        if ($this->ndvi_value >= 0.7) return 'excellent';
        if ($this->ndvi_value >= 0.5) return 'good';
        if ($this->ndvi_value >= 0.3) return 'fair';
        return 'poor';
    }
}
