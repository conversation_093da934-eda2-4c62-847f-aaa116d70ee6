<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Report extends Model
{
    protected $fillable = [
        'title',
        'description',
        'type',
        'generated_by',
        'organization_id',
        'filters',
        'data',
        'file_path',
        'file_type',
        'file_size',
        'period_start',
        'period_end',
        'regions_covered',
        'status',
        'error_message',
        'generated_at',
        'expires_at',
        'download_count',
    ];

    protected $casts = [
        'filters' => 'array',
        'data' => 'array',
        'regions_covered' => 'array',
        'period_start' => 'date',
        'period_end' => 'date',
        'generated_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function generator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'generating' => 'blue',
            'completed' => 'green',
            'failed' => 'red',
            'expired' => 'gray',
            default => 'gray'
        };
    }

    public function getFileSizeHumanAttribute()
    {
        if (!$this->file_size) return null;

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}
