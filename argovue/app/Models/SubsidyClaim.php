<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubsidyClaim extends Model
{
    protected $fillable = [
        'claim_number',
        'field_id',
        'claimant_id',
        'reviewer_id',
        'program_name',
        'subsidy_type',
        'claimed_amount',
        'approved_amount',
        'claimed_area_hectares',
        'verified_area_hectares',
        'status',
        'claim_description',
        'supporting_documents',
        'application_date',
        'review_date',
        'approval_date',
        'reviewer_notes',
        'fraud_risk_score',
        'verification_data',
        'field_visit_required',
        'field_visit_date',
        'field_agent_id',
        'field_visit_report',
    ];

    protected $casts = [
        'supporting_documents' => 'array',
        'verification_data' => 'array',
        'field_visit_report' => 'array',
        'field_visit_required' => 'boolean',
        'application_date' => 'date',
        'review_date' => 'date',
        'approval_date' => 'date',
        'field_visit_date' => 'date',
    ];

    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function claimant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'claimant_id');
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    public function fieldAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'field_agent_id');
    }

    public function getFraudRiskLevelAttribute()
    {
        if (!$this->fraud_risk_score) return 'unknown';

        if ($this->fraud_risk_score >= 80) return 'high';
        if ($this->fraud_risk_score >= 60) return 'medium';
        if ($this->fraud_risk_score >= 40) return 'low';
        return 'very_low';
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'under_review' => 'blue',
            'verified' => 'green',
            'flagged' => 'red',
            'approved' => 'green',
            'rejected' => 'red',
            'paid' => 'green',
            default => 'gray'
        };
    }
}
