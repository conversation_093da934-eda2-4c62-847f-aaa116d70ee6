<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Organization extends Model
{
    protected $fillable = [
        'name',
        'type',
        'registration_number',
        'description',
        'contact_email',
        'contact_phone',
        'address',
        'website',
        'regions_covered',
        'is_active',
    ];

    protected $casts = [
        'address' => 'array',
        'regions_covered' => 'array',
        'is_active' => 'boolean',
    ];

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function fields(): HasMany
    {
        return $this->hasMany(Field::class);
    }

    public function reports(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Report::class);
    }
}
