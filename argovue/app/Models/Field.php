<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Field extends Model
{
    protected $fillable = [
        'name',
        'field_id',
        'owner_id',
        'organization_id',
        'area_hectares',
        'crop_type',
        'crop_variety',
        'coordinates',
        'center_latitude',
        'center_longitude',
        'state',
        'lga',
        'ward',
        'status',
        'planting_date',
        'expected_harvest_date',
        'expected_yield_tons',
        'soil_data',
        'notes',
        'is_mapped',
        'last_satellite_update',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'soil_data' => 'array',
        'is_mapped' => 'boolean',
        'planting_date' => 'date',
        'expected_harvest_date' => 'date',
        'last_satellite_update' => 'datetime',
    ];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function satelliteData(): HasMany
    {
        return $this->hasMany(SatelliteData::class);
    }

    public function subsidyClaims(): HasMany
    {
        return $this->hasMany(SubsidyClaim::class);
    }

    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class);
    }

    public function getLatestNdviAttribute()
    {
        return $this->satelliteData()
            ->whereNotNull('ndvi_value')
            ->latest('capture_date')
            ->first()?->ndvi_value;
    }

    public function getHealthStatusAttribute()
    {
        $latestNdvi = $this->latest_ndvi;
        if (!$latestNdvi) return 'unknown';

        if ($latestNdvi >= 0.7) return 'excellent';
        if ($latestNdvi >= 0.5) return 'good';
        if ($latestNdvi >= 0.3) return 'fair';
        return 'poor';
    }
}
