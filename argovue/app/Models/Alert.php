<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Alert extends Model
{
    protected $fillable = [
        'title',
        'description',
        'type',
        'severity',
        'status',
        'field_id',
        'created_by',
        'assigned_to',
        'affected_regions',
        'affected_fields',
        'latitude',
        'longitude',
        'metadata',
        'alert_date',
        'acknowledged_at',
        'resolved_at',
        'resolution_notes',
        'sms_sent',
        'email_sent',
        'notification_log',
    ];

    protected $casts = [
        'affected_regions' => 'array',
        'affected_fields' => 'array',
        'metadata' => 'array',
        'notification_log' => 'array',
        'sms_sent' => 'boolean',
        'email_sent' => 'boolean',
        'alert_date' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function getSeverityColorAttribute()
    {
        return match($this->severity) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'active' => 'red',
            'acknowledged' => 'yellow',
            'resolved' => 'green',
            'dismissed' => 'gray',
            default => 'gray'
        };
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }
}
