<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_login_screen_can_be_rendered(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
    }

    public function test_users_can_authenticate_using_the_login_screen(): void
    {
        $organization = Organization::factory()->create();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'farmer',
            'organization_id' => $organization->id,
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'farmer',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect('/dashboard');
    }

    public function test_users_can_not_authenticate_with_invalid_password(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
            'role' => 'farmer',
        ]);

        $this->assertGuest();
    }

    public function test_users_can_logout(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/logout');

        $this->assertGuest();
        $response->assertRedirect('/');
    }

    public function test_government_admin_can_access_admin_features(): void
    {
        $organization = Organization::factory()->create(['type' => 'government']);
        
        $user = User::factory()->create([
            'role' => 'government_admin',
            'organization_id' => $organization->id,
            'permissions' => ['view_all_fields', 'manage_subsidies', 'generate_reports'],
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Dashboard/Index')
                ->has('user')
                ->has('dashboardData')
        );
    }

    public function test_farmer_can_access_farmer_features(): void
    {
        $user = User::factory()->create([
            'role' => 'farmer',
            'permissions' => ['manage_own_fields', 'apply_subsidies'],
        ]);

        $response = $this->actingAs($user)->get('/fields');

        $response->assertStatus(200);
    }

    public function test_bank_analyst_can_access_risk_analysis(): void
    {
        $organization = Organization::factory()->create(['type' => 'bank']);
        
        $user = User::factory()->create([
            'role' => 'bank_analyst',
            'organization_id' => $organization->id,
            'permissions' => ['assess_loan_risk', 'verify_activities'],
        ]);

        $response = $this->actingAs($user)->get('/subsidy-claims');

        $response->assertStatus(200);
    }

    public function test_ngo_coordinator_can_access_disaster_monitoring(): void
    {
        $organization = Organization::factory()->create(['type' => 'ngo']);
        
        $user = User::factory()->create([
            'role' => 'ngo_coordinator',
            'organization_id' => $organization->id,
            'permissions' => ['view_disaster_alerts', 'coordinate_relief'],
        ]);

        $response = $this->actingAs($user)->get('/alerts');

        $response->assertStatus(200);
    }

    public function test_role_based_navigation_is_filtered(): void
    {
        $farmer = User::factory()->create(['role' => 'farmer']);
        $admin = User::factory()->create(['role' => 'government_admin']);

        // Test farmer dashboard
        $farmerResponse = $this->actingAs($farmer)->get('/dashboard');
        $farmerResponse->assertStatus(200);

        // Test admin dashboard
        $adminResponse = $this->actingAs($admin)->get('/dashboard');
        $adminResponse->assertStatus(200);

        // Both should have different dashboard data based on role
        $this->assertNotEquals(
            $farmerResponse->getOriginalContent(),
            $adminResponse->getOriginalContent()
        );
    }

    public function test_unauthorized_access_is_blocked(): void
    {
        // Test unauthenticated access
        $response = $this->get('/dashboard');
        $response->assertRedirect('/login');

        $response = $this->get('/fields');
        $response->assertRedirect('/login');

        $response = $this->get('/subsidy-claims');
        $response->assertRedirect('/login');
    }

    public function test_user_registration_with_role_selection(): void
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'role' => 'farmer',
            'phone' => '+234-8-123-4567',
            'language' => 'en',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect('/dashboard');

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals('farmer', $user->role);
        $this->assertEquals('+234-8-123-4567', $user->phone);
    }

    public function test_organization_assignment_during_registration(): void
    {
        $organization = Organization::factory()->create(['type' => 'ngo']);

        $response = $this->post('/register', [
            'name' => 'NGO Worker',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'role' => 'ngo_coordinator',
            'organization_id' => $organization->id,
            'phone' => '+234-8-123-4567',
            'language' => 'en',
        ]);

        $this->assertAuthenticated();
        
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals($organization->id, $user->organization_id);
        $this->assertEquals('ngo_coordinator', $user->role);
    }
}
