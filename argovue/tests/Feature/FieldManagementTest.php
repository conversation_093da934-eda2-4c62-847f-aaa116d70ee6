<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Field;
use App\Models\Organization;
use App\Models\SatelliteData;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class FieldManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test organization
        $this->organization = Organization::factory()->create();
        
        // Create test users
        $this->farmer = User::factory()->create([
            'role' => 'farmer',
            'permissions' => ['manage_own_fields', 'apply_subsidies'],
        ]);
        
        $this->admin = User::factory()->create([
            'role' => 'government_admin',
            'organization_id' => $this->organization->id,
            'permissions' => ['view_all_fields', 'manage_subsidies', 'generate_reports'],
        ]);
    }

    public function test_farmer_can_create_field(): void
    {
        $fieldData = [
            'name' => 'Test Rice Farm',
            'area_hectares' => 25.5,
            'crop_type' => 'rice',
            'crop_variety' => 'FARO 44',
            'coordinates' => [
                'type' => 'Polygon',
                'coordinates' => [[[7.4, 9.1], [7.5, 9.1], [7.5, 9.2], [7.4, 9.2], [7.4, 9.1]]]
            ],
            'center_latitude' => 9.15,
            'center_longitude' => 7.45,
            'state' => 'Kaduna',
            'lga' => 'Zaria',
            'ward' => 'Sabon Gari',
            'planting_date' => '2024-06-15',
            'expected_harvest_date' => '2024-10-15',
            'expected_yield_tons' => 76.5,
            'notes' => 'Well-irrigated field with good drainage',
        ];

        $response = $this->actingAs($this->farmer)->post('/fields', $fieldData);

        $response->assertRedirect();
        $this->assertDatabaseHas('fields', [
            'name' => 'Test Rice Farm',
            'owner_id' => $this->farmer->id,
            'crop_type' => 'rice',
            'area_hectares' => 25.5,
        ]);
    }

    public function test_farmer_can_view_own_fields(): void
    {
        $field = Field::factory()->create([
            'owner_id' => $this->farmer->id,
            'name' => 'My Test Field',
        ]);

        $response = $this->actingAs($this->farmer)->get('/fields');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Fields/Index')
                ->has('fields.data', 1)
                ->where('fields.data.0.name', 'My Test Field')
        );
    }

    public function test_farmer_cannot_view_other_farmers_fields(): void
    {
        $otherFarmer = User::factory()->create(['role' => 'farmer']);
        $otherField = Field::factory()->create([
            'owner_id' => $otherFarmer->id,
            'name' => 'Other Farmer Field',
        ]);

        $myField = Field::factory()->create([
            'owner_id' => $this->farmer->id,
            'name' => 'My Field',
        ]);

        $response = $this->actingAs($this->farmer)->get('/fields');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Fields/Index')
                ->has('fields.data', 1)
                ->where('fields.data.0.name', 'My Field')
        );
    }

    public function test_admin_can_view_all_fields(): void
    {
        $farmer1 = User::factory()->create(['role' => 'farmer']);
        $farmer2 = User::factory()->create(['role' => 'farmer']);

        Field::factory()->create(['owner_id' => $farmer1->id, 'name' => 'Field 1']);
        Field::factory()->create(['owner_id' => $farmer2->id, 'name' => 'Field 2']);

        $response = $this->actingAs($this->admin)->get('/fields');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Fields/Index')
                ->has('fields.data', 2)
        );
    }

    public function test_field_can_be_updated(): void
    {
        $field = Field::factory()->create([
            'owner_id' => $this->farmer->id,
            'name' => 'Original Name',
            'crop_type' => 'rice',
        ]);

        $updateData = [
            'name' => 'Updated Field Name',
            'area_hectares' => $field->area_hectares,
            'crop_type' => 'maize',
            'coordinates' => $field->coordinates,
            'center_latitude' => $field->center_latitude,
            'center_longitude' => $field->center_longitude,
            'state' => $field->state,
            'lga' => $field->lga,
            'status' => 'active',
        ];

        $response = $this->actingAs($this->farmer)->put("/fields/{$field->id}", $updateData);

        $response->assertRedirect("/fields/{$field->id}");
        $this->assertDatabaseHas('fields', [
            'id' => $field->id,
            'name' => 'Updated Field Name',
            'crop_type' => 'maize',
        ]);
    }

    public function test_field_can_be_deleted(): void
    {
        $field = Field::factory()->create([
            'owner_id' => $this->farmer->id,
        ]);

        $response = $this->actingAs($this->farmer)->delete("/fields/{$field->id}");

        $response->assertRedirect('/fields');
        $this->assertDatabaseMissing('fields', ['id' => $field->id]);
    }

    public function test_field_search_functionality(): void
    {
        Field::factory()->create([
            'owner_id' => $this->farmer->id,
            'name' => 'Rice Farm Alpha',
            'crop_type' => 'rice',
            'state' => 'Lagos',
        ]);

        Field::factory()->create([
            'owner_id' => $this->farmer->id,
            'name' => 'Maize Farm Beta',
            'crop_type' => 'maize',
            'state' => 'Kano',
        ]);

        // Search by name
        $response = $this->actingAs($this->farmer)->get('/fields?search=Rice');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('fields.data', 1)
                ->where('fields.data.0.name', 'Rice Farm Alpha')
        );

        // Filter by crop type
        $response = $this->actingAs($this->farmer)->get('/fields?crop_type=maize');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('fields.data', 1)
                ->where('fields.data.0.crop_type', 'maize')
        );
    }

    public function test_satellite_data_can_be_viewed(): void
    {
        $field = Field::factory()->create([
            'owner_id' => $this->farmer->id,
        ]);

        SatelliteData::factory()->create([
            'field_id' => $field->id,
            'ndvi_average' => 0.75,
            'vegetation_health' => 'good',
        ]);

        $response = $this->actingAs($this->farmer)->get("/fields/{$field->id}/satellite-data");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Fields/SatelliteData')
                ->has('satelliteData.data', 1)
                ->where('satelliteData.data.0.ndvi_average', 0.75)
        );
    }

    public function test_field_validation_rules(): void
    {
        // Test required fields
        $response = $this->actingAs($this->farmer)->post('/fields', []);
        $response->assertSessionHasErrors(['name', 'area_hectares', 'crop_type']);

        // Test invalid crop type
        $response = $this->actingAs($this->farmer)->post('/fields', [
            'name' => 'Test Field',
            'area_hectares' => 10,
            'crop_type' => 'invalid_crop',
            'coordinates' => [],
            'center_latitude' => 9.15,
            'center_longitude' => 7.45,
            'state' => 'Lagos',
            'lga' => 'Ikeja',
        ]);
        $response->assertSessionHasErrors(['crop_type']);

        // Test invalid coordinates
        $response = $this->actingAs($this->farmer)->post('/fields', [
            'name' => 'Test Field',
            'area_hectares' => 10,
            'crop_type' => 'rice',
            'coordinates' => [],
            'center_latitude' => 91, // Invalid latitude
            'center_longitude' => 7.45,
            'state' => 'Lagos',
            'lga' => 'Ikeja',
        ]);
        $response->assertSessionHasErrors(['center_latitude']);
    }

    public function test_field_status_updates(): void
    {
        $field = Field::factory()->create([
            'owner_id' => $this->farmer->id,
            'status' => 'active',
        ]);

        // Admin can update field status
        $response = $this->actingAs($this->admin)->post("/fields/{$field->id}/update-status", [
            'status' => 'verified',
            'notes' => 'Field verified by satellite data',
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('fields', [
            'id' => $field->id,
            'status' => 'verified',
        ]);
    }

    public function test_field_ownership_authorization(): void
    {
        $otherFarmer = User::factory()->create(['role' => 'farmer']);
        $field = Field::factory()->create([
            'owner_id' => $otherFarmer->id,
        ]);

        // Farmer cannot edit other farmer's field
        $response = $this->actingAs($this->farmer)->get("/fields/{$field->id}/edit");
        $response->assertStatus(403);

        // Farmer cannot update other farmer's field
        $response = $this->actingAs($this->farmer)->put("/fields/{$field->id}", [
            'name' => 'Hacked Field',
        ]);
        $response->assertStatus(403);

        // Farmer cannot delete other farmer's field
        $response = $this->actingAs($this->farmer)->delete("/fields/{$field->id}");
        $response->assertStatus(403);
    }
}
