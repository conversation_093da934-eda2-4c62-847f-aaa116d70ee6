<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\SatelliteData;
use App\Models\Alert;
use App\Services\SatelliteDataService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SatelliteDataTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected SatelliteDataService $satelliteService;
    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->satelliteService = app(SatelliteDataService::class);
        $this->field = Field::factory()->create();
    }

    public function test_satellite_data_processing(): void
    {
        $satelliteData = [
            'source' => 'sentinel-2',
            'capture_date' => '2024-06-15',
            'cloud_coverage' => 15,
            'ndvi_data' => [0.7, 0.75, 0.8, 0.72, 0.78],
            'evi_average' => 0.65,
            'savi_average' => 0.55,
            'metadata' => ['resolution' => '10m', 'bands' => ['B4', 'B8']],
        ];

        $result = $this->satelliteService->processSatelliteData($this->field, $satelliteData);

        $this->assertInstanceOf(SatelliteData::class, $result);
        $this->assertEquals($this->field->id, $result->field_id);
        $this->assertEquals('sentinel-2', $result->satellite_source);
        $this->assertEquals(0.75, $result->ndvi_average);
        $this->assertEquals('good', $result->vegetation_health);
    }

    public function test_ndvi_statistics_calculation(): void
    {
        $ndviData = [0.1, 0.3, 0.5, 0.7, 0.9];
        
        $stats = $this->satelliteService->calculateNDVIStatistics($ndviData);

        $this->assertEquals(0.5, $stats['average']);
        $this->assertEquals(0.1, $stats['min']);
        $this->assertEquals(0.9, $stats['max']);
        $this->assertGreaterThan(0, $stats['std']);
    }

    public function test_vegetation_health_assessment(): void
    {
        $this->assertEquals('excellent', $this->satelliteService->assessVegetationHealth(0.8));
        $this->assertEquals('good', $this->satelliteService->assessVegetationHealth(0.6));
        $this->assertEquals('fair', $this->satelliteService->assessVegetationHealth(0.4));
        $this->assertEquals('poor', $this->satelliteService->assessVegetationHealth(0.2));
        $this->assertEquals('critical', $this->satelliteService->assessVegetationHealth(0.05));
    }

    public function test_anomaly_detection(): void
    {
        // Create historical data
        SatelliteData::factory()->count(5)->create([
            'field_id' => $this->field->id,
            'ndvi_average' => 0.7,
            'capture_date' => now()->subDays(30),
        ]);

        // Test with normal NDVI
        $normalStats = ['average' => 0.72];
        $anomalies = $this->satelliteService->detectAnomalies($this->field, $normalStats);
        $this->assertEmpty($anomalies);

        // Test with very low NDVI
        $lowStats = ['average' => 0.05];
        $anomalies = $this->satelliteService->detectAnomalies($this->field, $lowStats);
        $this->assertContains('severe_vegetation_loss', $anomalies);

        // Test with high variability
        $variableStats = ['average' => 0.7, 'std' => 0.4];
        $anomalies = $this->satelliteService->detectAnomalies($this->field, $variableStats);
        $this->assertContains('high_vegetation_variability', $anomalies);
    }

    public function test_change_detection(): void
    {
        // Create previous satellite data
        SatelliteData::factory()->create([
            'field_id' => $this->field->id,
            'ndvi_average' => 0.6,
            'capture_date' => now()->subDays(15),
        ]);

        // Test improvement
        $changeData = $this->satelliteService->calculateChangeDetection($this->field, 0.7);
        $this->assertEquals('improvement', $changeData['change_type']);
        $this->assertGreaterThan(0, $changeData['change_percentage']);

        // Test decline
        $changeData = $this->satelliteService->calculateChangeDetection($this->field, 0.5);
        $this->assertEquals('decline', $changeData['change_type']);
        $this->assertLessThan(0, $changeData['change_percentage']);

        // Test stable
        $changeData = $this->satelliteService->calculateChangeDetection($this->field, 0.61);
        $this->assertEquals('stable', $changeData['change_type']);
    }

    public function test_alert_generation_from_satellite_data(): void
    {
        $satelliteData = [
            'source' => 'sentinel-2',
            'capture_date' => '2024-06-15',
            'cloud_coverage' => 10,
            'ndvi_data' => [0.05, 0.03, 0.08, 0.02, 0.06], // Very low NDVI
        ];

        $result = $this->satelliteService->processSatelliteData($this->field, $satelliteData);

        // Check that critical vegetation health generated an alert
        $this->assertEquals('critical', $result->vegetation_health);
        
        $alert = Alert::where('field_id', $this->field->id)
            ->where('type', 'vegetation')
            ->where('severity', 'critical')
            ->first();
            
        $this->assertNotNull($alert);
        $this->assertEquals('active', $alert->status);
    }

    public function test_field_update_after_satellite_processing(): void
    {
        $satelliteData = [
            'source' => 'sentinel-2',
            'capture_date' => '2024-06-15',
            'cloud_coverage' => 5,
            'ndvi_data' => [0.75, 0.78, 0.72, 0.76, 0.74],
        ];

        $this->satelliteService->processSatelliteData($this->field, $satelliteData);

        $this->field->refresh();
        
        $this->assertNotNull($this->field->last_satellite_update);
        $this->assertEquals('good', $this->field->health_status);
        $this->assertEquals(0.75, $this->field->latest_ndvi);
    }

    public function test_api_satellite_data_ingestion(): void
    {
        $apiData = [
            'field_id' => $this->field->id,
            'satellite_source' => 'landsat-8',
            'capture_date' => '2024-06-15',
            'cloud_coverage' => 20,
            'ndvi_data' => [0.6, 0.65, 0.7, 0.62, 0.68],
            'evi_average' => 0.55,
            'metadata' => ['scene_id' => 'LC08_L1TP_123456'],
        ];

        $response = $this->postJson('/api/v1/satellite-data', $apiData, [
            'X-API-Key' => 'test-api-key'
        ]);

        $response->assertStatus(201);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('satellite_data', [
            'field_id' => $this->field->id,
            'satellite_source' => 'landsat-8',
            'ndvi_average' => 0.65,
        ]);
    }

    public function test_bulk_satellite_data_processing(): void
    {
        $field2 = Field::factory()->create();
        
        $bulkData = [
            'satellite_data' => [
                [
                    'field_id' => $this->field->id,
                    'satellite_source' => 'sentinel-2',
                    'capture_date' => '2024-06-15',
                    'cloud_coverage' => 10,
                    'ndvi_data' => [0.7, 0.75, 0.8],
                ],
                [
                    'field_id' => $field2->id,
                    'satellite_source' => 'sentinel-2',
                    'capture_date' => '2024-06-15',
                    'cloud_coverage' => 15,
                    'ndvi_data' => [0.6, 0.65, 0.7],
                ],
            ]
        ];

        $response = $this->postJson('/api/v1/satellite-data/bulk', $bulkData, [
            'X-API-Key' => 'test-api-key'
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'data' => [
                'summary' => [
                    'total' => 2,
                    'successful' => 2,
                    'failed' => 0,
                ]
            ]
        ]);

        $this->assertDatabaseCount('satellite_data', 2);
    }

    public function test_regional_satellite_data_retrieval(): void
    {
        $field1 = Field::factory()->create(['state' => 'Lagos', 'lga' => 'Ikeja']);
        $field2 = Field::factory()->create(['state' => 'Lagos', 'lga' => 'Surulere']);
        $field3 = Field::factory()->create(['state' => 'Kano', 'lga' => 'Nassarawa']);

        SatelliteData::factory()->create([
            'field_id' => $field1->id,
            'capture_date' => now()->subDays(5),
        ]);
        
        SatelliteData::factory()->create([
            'field_id' => $field2->id,
            'capture_date' => now()->subDays(3),
        ]);
        
        SatelliteData::factory()->create([
            'field_id' => $field3->id,
            'capture_date' => now()->subDays(2),
        ]);

        $response = $this->getJson('/api/v1/satellite-data/regional?state=Lagos', [
            'X-API-Key' => 'test-api-key'
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $data = $response->json('data');
        $this->assertCount(2, $data); // Only Lagos fields
        
        $regionalStats = $response->json('regional_statistics');
        $this->assertEquals(2, $regionalStats['total_fields']);
    }

    public function test_satellite_data_validation(): void
    {
        $invalidData = [
            'field_id' => 999999, // Non-existent field
            'satellite_source' => 'invalid-source',
            'capture_date' => 'invalid-date',
            'cloud_coverage' => 150, // Invalid percentage
            'ndvi_data' => [2.0, -2.0], // Invalid NDVI values
        ];

        $response = $this->postJson('/api/v1/satellite-data', $invalidData, [
            'X-API-Key' => 'test-api-key'
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'field_id',
            'satellite_source',
            'capture_date',
            'cloud_coverage',
            'ndvi_data.0',
            'ndvi_data.1',
        ]);
    }

    public function test_satellite_data_filtering_and_pagination(): void
    {
        // Create multiple satellite data entries
        SatelliteData::factory()->count(25)->create([
            'field_id' => $this->field->id,
            'satellite_source' => 'sentinel-2',
        ]);
        
        SatelliteData::factory()->count(15)->create([
            'field_id' => $this->field->id,
            'satellite_source' => 'landsat-8',
        ]);

        $response = $this->getJson("/api/v1/satellite-data/{$this->field->id}?satellite_source=sentinel-2&per_page=10", [
            'X-API-Key' => 'test-api-key'
        ]);

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(10, $data['data']); // Pagination limit
        $this->assertEquals(25, $data['total']); // Total sentinel-2 records
        
        // Check that all returned records are sentinel-2
        foreach ($data['data'] as $record) {
            $this->assertEquals('sentinel-2', $record['satellite_source']);
        }
    }
}
